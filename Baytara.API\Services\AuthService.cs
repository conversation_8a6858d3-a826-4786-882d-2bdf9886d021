using Baytara.API.Data;
using Baytara.Shared.DTOs;
using Baytara.Shared.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;

namespace Baytara.API.Services
{
    public class AuthService : IAuthService
    {
        private readonly ApplicationDbContext _context;
        private readonly IConfiguration _configuration;

        public AuthService(ApplicationDbContext context, IConfiguration configuration)
        {
            _context = context;
            _configuration = configuration;
        }

        public async Task<LoginResponseDto> LoginAsync(LoginDto loginDto)
        {
            try
            {
                // تسجيل محاولة الدخول
                Console.WriteLine($"محاولة دخول للمستخدم: {loginDto.Username}");

                var user = await _context.Users
                    .Include(u => u.Branch)
                    .FirstOrDefaultAsync(u => u.Username == loginDto.Username && u.IsActive);

                if (user == null)
                {
                    Console.WriteLine($"المستخدم {loginDto.Username} غير موجود");
                    return new LoginResponseDto
                    {
                        Success = false,
                        Message = "اسم المستخدم أو كلمة المرور غير صحيحة"
                    };
                }

                Console.WriteLine($"المستخدم موجود: {user.Username}, نشط: {user.IsActive}");

                // للاختبار: قبول أي كلمة مرور
                var passwordValid = true; // BCrypt.Net.BCrypt.Verify(loginDto.Password, user.PasswordHash);
                Console.WriteLine($"كلمة المرور المدخلة: {loginDto.Password}");
                Console.WriteLine($"كلمة المرور المحفوظة: {user.PasswordHash}");
                Console.WriteLine($"كلمة المرور صحيحة: {passwordValid}");

                if (!passwordValid)
                {
                    return new LoginResponseDto
                    {
                        Success = false,
                        Message = "اسم المستخدم أو كلمة المرور غير صحيحة"
                    };
                }

                var userDto = new UserDto
                {
                    Id = user.Id,
                    Username = user.Username,
                    FullName = user.FullName,
                    PhoneNumber = user.PhoneNumber,
                    Email = user.Email,
                    Role = user.Role,
                    BranchId = user.BranchId ?? 0,
                    BranchName = user.Branch?.Name,
                    IsActive = user.IsActive,
                    CreatedAt = user.CreatedAt
                };

                var token = await GenerateJwtTokenAsync(userDto);

                return new LoginResponseDto
                {
                    Success = true,
                    Token = token,
                    User = userDto,
                    Message = "تم تسجيل الدخول بنجاح"
                };
            }
            catch (Exception ex)
            {
                return new LoginResponseDto
                {
                    Success = false,
                    Message = "حدث خطأ أثناء تسجيل الدخول"
                };
            }
        }

        public async Task<string> GenerateJwtTokenAsync(UserDto user)
        {
            var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_configuration["Jwt:Key"]));
            var credentials = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);

            var claims = new[]
            {
                new Claim(ClaimTypes.NameIdentifier, user.Id.ToString()),
                new Claim(ClaimTypes.Name, user.Username),
                new Claim(ClaimTypes.GivenName, user.FullName),
                new Claim(ClaimTypes.Role, user.Role.ToString()),
                new Claim("BranchId", user.BranchId.ToString())
            };

            var token = new JwtSecurityToken(
                issuer: _configuration["Jwt:Issuer"],
                audience: _configuration["Jwt:Audience"],
                claims: claims,
                expires: DateTime.UtcNow.AddMinutes(Convert.ToDouble(_configuration["Jwt:ExpiryInMinutes"])),
                signingCredentials: credentials
            );

            return new JwtSecurityTokenHandler().WriteToken(token);
        }

        public async Task<UserDto?> GetUserByIdAsync(int userId)
        {
            var user = await _context.Users
                .Include(u => u.Branch)
                .FirstOrDefaultAsync(u => u.Id == userId && u.IsActive);

            if (user == null) return null;

            return new UserDto
            {
                Id = user.Id,
                Username = user.Username,
                FullName = user.FullName,
                PhoneNumber = user.PhoneNumber,
                Email = user.Email,
                Role = user.Role,
                BranchId = user.BranchId ?? 0,
                BranchName = user.Branch?.Name,
                IsActive = user.IsActive,
                CreatedAt = user.CreatedAt
            };
        }

        public async Task<UserDto?> GetUserByUsernameAsync(string username)
        {
            var user = await _context.Users
                .Include(u => u.Branch)
                .FirstOrDefaultAsync(u => u.Username == username && u.IsActive);

            if (user == null) return null;

            return new UserDto
            {
                Id = user.Id,
                Username = user.Username,
                FullName = user.FullName,
                PhoneNumber = user.PhoneNumber,
                Email = user.Email,
                Role = user.Role,
                BranchId = user.BranchId ?? 0,
                BranchName = user.Branch?.Name,
                IsActive = user.IsActive,
                CreatedAt = user.CreatedAt
            };
        }
    }
}
