@page "/reports"
@using Baytara.Client.Services
@using Baytara.Shared.DTOs
@inject IApiService ApiService
@inject IJSRuntime JSRuntime

<PageTitle>التقارير - بيطره</PageTitle>

<div class="reports-page">
    <div class="page-header">
        <div class="header-content">
            <h1 class="page-title">
                <i class="fas fa-chart-bar"></i>
                التقارير والإحصائيات
            </h1>
            <p class="page-subtitle">تقارير شاملة عن أداء العيادة البيطرية</p>
        </div>
        <div class="header-actions">
            <div class="filter-group">
                <div class="date-filter">
                    <label>من تاريخ:</label>
                    <input type="date" @bind="startDate" class="form-control" />
                </div>
                <div class="date-filter">
                    <label>إلى تاريخ:</label>
                    <input type="date" @bind="endDate" class="form-control" />
                </div>
                <div class="branch-filter">
                    <label>الفرع:</label>
                    <select @bind="selectedBranchId" class="form-control">
                        <option value="0">جميع الفروع</option>
                        @foreach (var branch in branches)
                        {
                            <option value="@branch.Id">@branch.Name</option>
                        }
                    </select>
                </div>
            </div>
            <div class="action-buttons">
                <button class="btn btn-primary" @onclick="LoadReports">
                    <i class="fas fa-search"></i>
                    تحديث التقارير
                </button>
                <button class="btn btn-success" @onclick="ShowExportModal">
                    <i class="fas fa-file-export"></i>
                    تصدير شامل
                </button>
            </div>
        </div>
    </div>

    @if (isLoading)
    {
        <div class="loading-container">
            <div class="spinner">
                <i class="fas fa-spinner fa-spin"></i>
            </div>
            <p>جاري تحميل التقارير...</p>
        </div>
    }
    else
    {
        <!-- Summary Cards -->
        <div class="summary-cards">
            <div class="summary-card revenue">
                <div class="card-icon">
                    <i class="fas fa-money-bill-wave"></i>
                </div>
                <div class="card-content">
                    <h3>@totalRevenue.ToString("N0") ر.س</h3>
                    <p>إجمالي الإيرادات</p>
                </div>
            </div>
            
            <div class="summary-card treatments">
                <div class="card-icon">
                    <i class="fas fa-stethoscope"></i>
                </div>
                <div class="card-content">
                    <h3>@totalTreatments</h3>
                    <p>إجمالي العلاجات</p>
                </div>
            </div>
            
            <div class="summary-card animals">
                <div class="card-icon">
                    <i class="fas fa-paw"></i>
                </div>
                <div class="card-content">
                    <h3>@totalAnimals</h3>
                    <p>إجمالي الحيوانات</p>
                </div>
            </div>
            
            <div class="summary-card breeders">
                <div class="card-icon">
                    <i class="fas fa-users"></i>
                </div>
                <div class="card-content">
                    <h3>@totalBreeders</h3>
                    <p>إجمالي المربين</p>
                </div>
            </div>
        </div>

        <!-- Report Sections -->
        <div class="reports-grid">
            <!-- Treatments by Date Chart -->
            <div class="report-card">
                <div class="card-header">
                    <h3>
                        <i class="fas fa-chart-line"></i>
                        العلاجات حسب التاريخ
                    </h3>
                    <button class="btn btn-outline btn-sm" @onclick="ExportTreatmentsByDate">
                        <i class="fas fa-download"></i>
                        تصدير
                    </button>
                </div>
                <div class="card-body">
                    @if (treatmentsByDate.Any())
                    {
                        <div class="chart-container">
                            <canvas id="treatmentsByDateChart" width="400" height="200"></canvas>
                        </div>
                        <div class="chart-data">
                            @foreach (var item in treatmentsByDate.Take(5))
                            {
                                <div class="data-row">
                                    <span class="date">@item.Date.ToString("dd/MM")</span>
                                    <span class="count">@item.Count علاج</span>
                                    <span class="revenue">@item.Revenue.ToString("N0") ر.س</span>
                                </div>
                            }
                        </div>
                    }
                    else
                    {
                        <div class="no-data">
                            <i class="fas fa-chart-line"></i>
                            <p>لا توجد بيانات للفترة المحددة</p>
                        </div>
                    }
                </div>
            </div>

            <!-- Treatments by Animal Type -->
            <div class="report-card">
                <div class="card-header">
                    <h3>
                        <i class="fas fa-chart-pie"></i>
                        العلاجات حسب نوع الحيوان
                    </h3>
                    <button class="btn btn-outline btn-sm" @onclick="ExportTreatmentsByAnimalType">
                        <i class="fas fa-download"></i>
                        تصدير
                    </button>
                </div>
                <div class="card-body">
                    @if (treatmentsByAnimalType.Any())
                    {
                        <div class="pie-chart-container">
                            <canvas id="treatmentsByAnimalTypeChart" width="300" height="300"></canvas>
                        </div>
                        <div class="legend">
                            @foreach (var item in treatmentsByAnimalType)
                            {
                                <div class="legend-item">
                                    <span class="legend-color" style="background-color: @GetRandomColor(item.AnimalTypeName)"></span>
                                    <span class="legend-text">@item.AnimalTypeName (@item.Count)</span>
                                    <span class="legend-revenue">@item.Revenue.ToString("N0") ر.س</span>
                                </div>
                            }
                        </div>
                    }
                    else
                    {
                        <div class="no-data">
                            <i class="fas fa-chart-pie"></i>
                            <p>لا توجد بيانات للفترة المحددة</p>
                        </div>
                    }
                </div>
            </div>

            <!-- Top Medicines -->
            <div class="report-card">
                <div class="card-header">
                    <h3>
                        <i class="fas fa-pills"></i>
                        الأدوية الأكثر استخداماً
                    </h3>
                    <button class="btn btn-outline btn-sm" @onclick="ExportMedicineUsage">
                        <i class="fas fa-download"></i>
                        تصدير
                    </button>
                </div>
                <div class="card-body">
                    @if (topMedicines.Any())
                    {
                        <div class="medicines-list">
                            @foreach (var medicine in topMedicines.Take(10))
                            {
                                <div class="medicine-row">
                                    <div class="medicine-info">
                                        <span class="medicine-name">@medicine.Name</span>
                                        <small class="medicine-usage">استخدم @medicine.Quantity مرة</small>
                                    </div>
                                    <div class="medicine-value">
                                        <span class="value">@((medicine.Quantity * medicine.UnitPrice).ToString("N0")) ر.س</span>
                                    </div>
                                </div>
                            }
                        </div>
                    }
                    else
                    {
                        <div class="no-data">
                            <i class="fas fa-pills"></i>
                            <p>لا توجد بيانات للأدوية</p>
                        </div>
                    }
                </div>
            </div>

            <!-- Top Breeders -->
            <div class="report-card">
                <div class="card-header">
                    <h3>
                        <i class="fas fa-crown"></i>
                        أفضل المربين
                    </h3>
                    <button class="btn btn-outline btn-sm" @onclick="ExportTopBreeders">
                        <i class="fas fa-download"></i>
                        تصدير
                    </button>
                </div>
                <div class="card-body">
                    @if (topBreeders.Any())
                    {
                        <div class="breeders-list">
                            @foreach (var breeder in topBreeders.Take(10))
                            {
                                <div class="breeder-row">
                                    <div class="breeder-info">
                                        <span class="breeder-name">@breeder.BreederName</span>
                                        <small class="breeder-stats">@breeder.AnimalsCount حيوان - @breeder.TreatmentsCount علاج</small>
                                    </div>
                                    <div class="breeder-value">
                                        <span class="value">@breeder.TotalSpent.ToString("N0") ر.س</span>
                                    </div>
                                </div>
                            }
                        </div>
                    }
                    else
                    {
                        <div class="no-data">
                            <i class="fas fa-users"></i>
                            <p>لا توجد بيانات للمربين</p>
                        </div>
                    }
                </div>
            </div>

            <!-- Medicine Alerts -->
            <div class="report-card">
                <div class="card-header">
                    <h3>
                        <i class="fas fa-exclamation-triangle"></i>
                        تنبيهات المخزون
                    </h3>
                    <button class="btn btn-outline btn-sm" @onclick="ExportMedicineAlerts">
                        <i class="fas fa-download"></i>
                        تصدير
                    </button>
                </div>
                <div class="card-body">
                    @if (medicineAlerts.Any())
                    {
                        <div class="alerts-list">
                            @foreach (var alert in medicineAlerts.Take(10))
                            {
                                <div class="alert-row @GetAlertClass(alert.AlertType)">
                                    <div class="alert-icon">
                                        <i class="@GetAlertIcon(alert.AlertType)"></i>
                                    </div>
                                    <div class="alert-info">
                                        <span class="alert-medicine">@alert.Name</span>
                                        <small class="alert-message">@GetAlertMessage(alert)</small>
                                    </div>
                                    <div class="alert-branch">
                                        <small>@alert.BranchName</small>
                                    </div>
                                </div>
                            }
                        </div>
                    }
                    else
                    {
                        <div class="no-data success">
                            <i class="fas fa-check-circle"></i>
                            <p>لا توجد تنبيهات - جميع الأدوية في حالة جيدة</p>
                        </div>
                    }
                </div>
            </div>

            <!-- Financial Summary -->
            <div class="report-card full-width">
                <div class="card-header">
                    <h3>
                        <i class="fas fa-calculator"></i>
                        الملخص المالي المتقدم
                    </h3>
                    <button class="btn btn-outline btn-sm" @onclick="ExportFinancialSummary">
                        <i class="fas fa-download"></i>
                        تصدير
                    </button>
                </div>
                <div class="card-body">
                    @if (financialReport != null)
                    {
                        <div class="financial-grid">
                            <div class="financial-item">
                                <span class="label">إجمالي الإيرادات:</span>
                                <span class="value positive">@financialReport.TotalRevenue.ToString("N2") ر.س</span>
                            </div>
                            <div class="financial-item">
                                <span class="label">إيرادات العلاجات:</span>
                                <span class="value">@financialReport.TreatmentsRevenue.ToString("N2") ر.س</span>
                            </div>
                            <div class="financial-item">
                                <span class="label">إيرادات المختبر:</span>
                                <span class="value">@financialReport.LabTestsRevenue.ToString("N2") ر.س</span>
                            </div>
                            <div class="financial-item">
                                <span class="label">صافي الربح:</span>
                                <span class="value positive">@financialReport.NetProfit.ToString("N2") ر.س</span>
                            </div>
                            <div class="financial-item">
                                <span class="label">متوسط تكلفة العلاج:</span>
                                <span class="value">@financialReport.AverageTreatmentCost.ToString("N2") ر.س</span>
                            </div>
                            <div class="financial-item">
                                <span class="label">متوسط الإيراد اليومي:</span>
                                <span class="value">@financialReport.AverageDailyRevenue.ToString("N2") ر.س</span>
                            </div>
                        </div>

                        @if (financialReport.MonthlyRevenue.Any())
                        {
                            <div class="monthly-revenue-section">
                                <h4>الإيرادات الشهرية</h4>
                                <div class="monthly-revenue-chart">
                                    <canvas id="monthlyRevenueChart" width="800" height="300"></canvas>
                                </div>
                                <div class="monthly-data">
                                    @foreach (var month in financialReport.MonthlyRevenue.TakeLast(6))
                                    {
                                        <div class="month-item">
                                            <span class="month-name">@month.MonthName @month.Year</span>
                                            <span class="month-revenue">@month.Revenue.ToString("N0") ر.س</span>
                                            <small class="month-details">@month.TreatmentsCount علاج - @month.LabTestsCount فحص</small>
                                        </div>
                                    }
                                </div>
                            </div>
                        }
                    }
                    else
                    {
                        <div class="no-data">
                            <i class="fas fa-calculator"></i>
                            <p>لا توجد بيانات مالية للفترة المحددة</p>
                        </div>
                    }
                </div>
            </div>

            <!-- Animal Type Statistics -->
            <div class="report-card">
                <div class="card-header">
                    <h3>
                        <i class="fas fa-chart-bar"></i>
                        إحصائيات أنواع الحيوانات
                    </h3>
                    <button class="btn btn-outline btn-sm" @onclick="ExportAnimalTypeStatistics">
                        <i class="fas fa-download"></i>
                        تصدير
                    </button>
                </div>
                <div class="card-body">
                    @if (animalTypeStatistics.Any())
                    {
                        <div class="animal-types-list">
                            @foreach (var stat in animalTypeStatistics.Take(8))
                            {
                                <div class="animal-type-row">
                                    <div class="animal-type-info">
                                        <span class="animal-type-name">@stat.AnimalTypeName</span>
                                        <div class="animal-type-stats">
                                            <small>@stat.AnimalsCount حيوان - @stat.TreatmentsCount علاج - @stat.LabTestsCount فحص</small>
                                        </div>
                                    </div>
                                    <div class="animal-type-revenue">
                                        <span class="revenue">@stat.TotalRevenue.ToString("N0") ر.س</span>
                                        <small class="avg-cost">متوسط: @stat.AverageTreatmentCost.ToString("N0") ر.س</small>
                                    </div>
                                </div>
                            }
                        </div>
                    }
                    else
                    {
                        <div class="no-data">
                            <i class="fas fa-paw"></i>
                            <p>لا توجد إحصائيات لأنواع الحيوانات</p>
                        </div>
                    }
                </div>
            </div>

            <!-- Branch Performance -->
            <div class="report-card">
                <div class="card-header">
                    <h3>
                        <i class="fas fa-building"></i>
                        أداء الفروع
                    </h3>
                    <button class="btn btn-outline btn-sm" @onclick="ExportBranchPerformance">
                        <i class="fas fa-download"></i>
                        تصدير
                    </button>
                </div>
                <div class="card-body">
                    @if (branchPerformance.Any())
                    {
                        <div class="branches-list">
                            @foreach (var branch in branchPerformance.Take(5))
                            {
                                <div class="branch-row">
                                    <div class="branch-info">
                                        <span class="branch-name">@branch.BranchName</span>
                                        <div class="branch-stats">
                                            <small>@branch.TreatmentsCount علاج - @branch.LabTestsCount فحص - @branch.ActiveBreeders مربي نشط</small>
                                        </div>
                                    </div>
                                    <div class="branch-performance">
                                        <span class="revenue">@branch.TotalRevenue.ToString("N0") ر.س</span>
                                        <small class="avg-revenue">متوسط: @branch.AverageRevenuePerTreatment.ToString("N0") ر.س</small>
                                    </div>
                                </div>
                            }
                        </div>
                    }
                    else
                    {
                        <div class="no-data">
                            <i class="fas fa-building"></i>
                            <p>لا توجد بيانات أداء للفروع</p>
                        </div>
                    }
                </div>
            </div>
        </div>
    }
</div>

<!-- Export Modal -->
@if (showExportModal)
{
    <div class="modal-overlay" @onclick="CloseExportModal">
        <div class="modal-content export-modal" @onclick:stopPropagation="true">
            <div class="modal-header">
                <h3>تصدير التقارير</h3>
                <button class="btn btn-close" @onclick="CloseExportModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="modal-body">
                <div class="export-options">
                    <div class="form-group">
                        <label>نوع التقرير:</label>
                        <select @bind="exportRequest.ReportType" class="form-control">
                            <option value="financial">التقرير المالي</option>
                            <option value="treatments">تقرير العلاجات</option>
                            <option value="labtests">تقرير المختبر</option>
                            <option value="medicines">تقرير الأدوية</option>
                            <option value="breeders">تقرير المربين</option>
                            <option value="comprehensive">تقرير شامل</option>
                        </select>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label>من تاريخ:</label>
                            <input type="date" @bind="exportRequest.StartDate" class="form-control" />
                        </div>
                        <div class="form-group">
                            <label>إلى تاريخ:</label>
                            <input type="date" @bind="exportRequest.EndDate" class="form-control" />
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label>الفرع:</label>
                            <select @bind="exportRequest.BranchId" class="form-control">
                                <option value="">جميع الفروع</option>
                                @foreach (var branch in branches)
                                {
                                    <option value="@branch.Id">@branch.Name</option>
                                }
                            </select>
                        </div>
                        <div class="form-group">
                            <label>صيغة التصدير:</label>
                            <select @bind="exportRequest.ExportFormat" class="form-control">
                                <option value="pdf">PDF</option>
                                <option value="excel">Excel</option>
                                <option value="csv">CSV</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="checkbox-label">
                            <input type="checkbox" @bind="exportRequest.IncludeCharts" />
                            <span class="checkmark"></span>
                            تضمين الرسوم البيانية
                        </label>
                    </div>
                </div>

                <div class="modal-actions">
                    <button type="button" class="btn btn-secondary" @onclick="CloseExportModal">إلغاء</button>
                    <button type="button" class="btn btn-success" @onclick="ProcessExport" disabled="@isExporting">
                        @if (isExporting)
                        {
                            <i class="fas fa-spinner fa-spin"></i>
                            <span>جاري التصدير...</span>
                        }
                        else
                        {
                            <i class="fas fa-file-export"></i>
                            <span>تصدير التقرير</span>
                        }
                    </button>
                </div>
            </div>
        </div>
    </div>
}

<style>
    .reports-page {
        max-width: 1400px;
        margin: 0 auto;
    }

    .page-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 30px;
        padding-bottom: 20px;
        border-bottom: 2px solid #eee;
        flex-wrap: wrap;
        gap: 20px;
    }

    .header-content h1 {
        font-size: 2.2rem;
        color: #333;
        margin-bottom: 5px;
        display: flex;
        align-items: center;
        gap: 15px;
    }

    .page-subtitle {
        font-size: 1.1rem;
        color: #666;
        margin-bottom: 0;
    }

    .header-actions {
        display: flex;
        gap: 20px;
        align-items: end;
        flex-wrap: wrap;
    }

    .filter-group {
        display: flex;
        gap: 15px;
        align-items: end;
        flex-wrap: wrap;
    }

    .action-buttons {
        display: flex;
        gap: 10px;
        align-items: center;
    }

    .date-filter,
    .branch-filter {
        display: flex;
        flex-direction: column;
        gap: 5px;
        min-width: 150px;
    }

    .date-filter label {
        font-size: 0.9rem;
        color: #666;
        font-weight: 500;
    }

    .form-control {
        padding: 8px 12px;
        border: 2px solid #e9ecef;
        border-radius: 6px;
        font-size: 0.9rem;
        transition: border-color 0.3s ease;
    }

    .form-control:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }

    .btn {
        padding: 10px 20px;
        border: none;
        border-radius: 8px;
        cursor: pointer;
        font-size: 0.9rem;
        font-weight: 500;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 8px;
        text-decoration: none;
    }

    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
    }

    .btn-outline {
        background: white;
        border: 2px solid #e9ecef;
        color: #666;
    }

    .btn-outline:hover {
        border-color: #667eea;
        color: #667eea;
    }

    .btn-sm {
        padding: 6px 12px;
        font-size: 0.8rem;
    }

    .btn-success {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
    }

    .btn-success:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
    }

    .loading-container {
        text-align: center;
        padding: 60px 20px;
        color: #666;
    }

    .loading-container .spinner {
        font-size: 3rem;
        color: #667eea;
        margin-bottom: 20px;
    }

    /* Summary Cards */
    .summary-cards {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin-bottom: 40px;
    }

    .summary-card {
        background: white;
        border-radius: 15px;
        padding: 25px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        display: flex;
        align-items: center;
        gap: 20px;
        border-left: 5px solid;
        transition: transform 0.3s ease;
    }

    .summary-card:hover {
        transform: translateY(-3px);
    }

    .summary-card.revenue { border-left-color: #28a745; }
    .summary-card.treatments { border-left-color: #17a2b8; }
    .summary-card.animals { border-left-color: #ffc107; }
    .summary-card.breeders { border-left-color: #667eea; }

    .card-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        color: white;
    }

    .summary-card.revenue .card-icon { background: #28a745; }
    .summary-card.treatments .card-icon { background: #17a2b8; }
    .summary-card.animals .card-icon { background: #ffc107; }
    .summary-card.breeders .card-icon { background: #667eea; }

    .card-content h3 {
        font-size: 2rem;
        font-weight: 700;
        color: #333;
        margin-bottom: 5px;
    }

    .card-content p {
        font-size: 1rem;
        color: #666;
        margin-bottom: 0;
    }

    /* Reports Grid */
    .reports-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
        gap: 25px;
    }

    .report-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }

    .report-card.full-width {
        grid-column: 1 / -1;
    }

    .card-header {
        padding: 20px 25px;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-bottom: 1px solid #dee2e6;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .card-header h3 {
        font-size: 1.2rem;
        color: #333;
        margin-bottom: 0;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .card-header h3 i {
        color: #667eea;
    }

    .card-body {
        padding: 25px;
    }

    /* Chart Containers */
    .chart-container {
        margin-bottom: 20px;
        text-align: center;
    }

    .pie-chart-container {
        text-align: center;
        margin-bottom: 20px;
    }

    .chart-data {
        display: flex;
        flex-direction: column;
        gap: 10px;
    }

    .data-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 15px;
        background: #f8f9fa;
        border-radius: 8px;
        font-size: 0.9rem;
    }

    .data-row .date {
        font-weight: 600;
        color: #333;
    }

    .data-row .count {
        color: #17a2b8;
    }

    .data-row .revenue {
        color: #28a745;
        font-weight: 600;
    }

    /* Legend */
    .legend {
        display: flex;
        flex-direction: column;
        gap: 8px;
    }

    .legend-item {
        display: flex;
        align-items: center;
        gap: 10px;
        font-size: 0.9rem;
    }

    .legend-color {
        width: 16px;
        height: 16px;
        border-radius: 50%;
    }

    .legend-text {
        flex: 1;
        color: #333;
    }

    .legend-revenue {
        color: #28a745;
        font-weight: 600;
    }

    /* Lists */
    .medicines-list,
    .breeders-list,
    .alerts-list {
        display: flex;
        flex-direction: column;
        gap: 12px;
    }

    .medicine-row,
    .breeder-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px;
        background: #f8f9fa;
        border-radius: 10px;
        border-left: 4px solid #667eea;
    }

    .medicine-info,
    .breeder-info {
        display: flex;
        flex-direction: column;
        gap: 3px;
    }

    .medicine-name,
    .breeder-name {
        font-weight: 600;
        color: #333;
        font-size: 0.95rem;
    }

    .medicine-usage,
    .breeder-stats {
        font-size: 0.8rem;
        color: #666;
    }

    .medicine-value,
    .breeder-value {
        text-align: right;
    }

    .value {
        font-weight: 600;
        color: #28a745;
        font-size: 1rem;
    }

    /* Alert Rows */
    .alert-row {
        display: flex;
        align-items: center;
        gap: 15px;
        padding: 12px 15px;
        border-radius: 8px;
        border-left: 4px solid;
    }

    .alert-row.low-stock {
        background: rgba(255, 193, 7, 0.1);
        border-left-color: #ffc107;
    }

    .alert-row.expiring {
        background: rgba(255, 152, 0, 0.1);
        border-left-color: #ff9800;
    }

    .alert-row.expired {
        background: rgba(220, 53, 69, 0.1);
        border-left-color: #dc3545;
    }

    .alert-icon {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 0.8rem;
    }

    .alert-row.low-stock .alert-icon { background: #ffc107; }
    .alert-row.expiring .alert-icon { background: #ff9800; }
    .alert-row.expired .alert-icon { background: #dc3545; }

    .alert-info {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 3px;
    }

    .alert-medicine {
        font-weight: 600;
        color: #333;
        font-size: 0.9rem;
    }

    .alert-message {
        font-size: 0.8rem;
        color: #666;
    }

    .alert-branch {
        text-align: right;
    }

    .alert-branch small {
        font-size: 0.75rem;
        color: #999;
    }

    /* Financial Grid */
    .financial-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
    }

    .financial-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20px;
        background: #f8f9fa;
        border-radius: 10px;
        border-left: 4px solid #667eea;
    }

    .financial-item .label {
        font-weight: 500;
        color: #666;
    }

    .financial-item .value {
        font-weight: 700;
        color: #333;
        font-size: 1.1rem;
    }

    .financial-item .value.positive {
        color: #28a745;
    }

    /* No Data State */
    .no-data {
        text-align: center;
        padding: 40px 20px;
        color: #999;
    }

    .no-data.success {
        color: #28a745;
    }

    .no-data i {
        font-size: 3rem;
        margin-bottom: 15px;
        opacity: 0.5;
    }

    .no-data.success i {
        opacity: 1;
    }

    .no-data p {
        font-size: 1rem;
        margin-bottom: 0;
    }

    /* New report styles */
    .monthly-revenue-section {
        margin-top: 30px;
        padding-top: 20px;
        border-top: 1px solid #eee;
    }

    .monthly-revenue-section h4 {
        color: #333;
        margin-bottom: 20px;
        font-size: 1.2rem;
    }

    .monthly-data {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
        margin-top: 20px;
    }

    .month-item {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 8px;
        border-left: 4px solid #667eea;
    }

    .month-name {
        font-weight: 600;
        color: #333;
        display: block;
        margin-bottom: 5px;
    }

    .month-revenue {
        font-size: 1.1rem;
        font-weight: 700;
        color: #28a745;
        display: block;
        margin-bottom: 3px;
    }

    .month-details {
        color: #666;
        font-size: 0.85rem;
    }

    .animal-types-list,
    .branches-list {
        display: flex;
        flex-direction: column;
        gap: 15px;
    }

    .animal-type-row,
    .branch-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px;
        background: #f8f9fa;
        border-radius: 8px;
        border-left: 4px solid #667eea;
    }

    .animal-type-info,
    .branch-info {
        flex: 1;
    }

    .animal-type-name,
    .branch-name {
        font-weight: 600;
        color: #333;
        display: block;
        margin-bottom: 5px;
    }

    .animal-type-stats,
    .branch-stats {
        color: #666;
        font-size: 0.85rem;
    }

    .animal-type-revenue,
    .branch-performance {
        text-align: left;
        min-width: 120px;
    }

    .revenue {
        font-size: 1.1rem;
        font-weight: 700;
        color: #28a745;
        display: block;
        margin-bottom: 3px;
    }

    .avg-cost,
    .avg-revenue {
        color: #666;
        font-size: 0.8rem;
    }

    /* Export Modal */
    .export-modal {
        max-width: 600px;
        width: 90%;
    }

    .export-options {
        display: flex;
        flex-direction: column;
        gap: 20px;
    }

    .form-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 15px;
    }

    .checkbox-label {
        display: flex;
        align-items: center;
        gap: 10px;
        cursor: pointer;
        font-weight: 500;
    }

    .checkmark {
        width: 20px;
        height: 20px;
        border: 2px solid #ddd;
        border-radius: 4px;
        position: relative;
    }

    input[type="checkbox"]:checked + .checkmark {
        background: #667eea;
        border-color: #667eea;
    }

    input[type="checkbox"]:checked + .checkmark::after {
        content: '✓';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: white;
        font-weight: bold;
    }

    /* Responsive Design */
    @@media (max-width: 768px) {
        .page-header {
            flex-direction: column;
            align-items: flex-start;
        }

        .header-actions {
            width: 100%;
            justify-content: space-between;
        }

        .summary-cards {
            grid-template-columns: 1fr;
        }

        .reports-grid {
            grid-template-columns: 1fr;
        }

        .financial-grid {
            grid-template-columns: 1fr;
        }

        .data-row,
        .medicine-row,
        .breeder-row {
            flex-direction: column;
            align-items: flex-start;
            gap: 10px;
        }

        .alert-row {
            flex-wrap: wrap;
        }
    }
</style>

@code {
    private bool isLoading = true;
    private bool isExporting = false;
    private DateTime startDate = DateTime.UtcNow.AddDays(-30);
    private DateTime endDate = DateTime.UtcNow;
    private int selectedBranchId = 0;

    // Modal states
    private bool showExportModal = false;

    // Summary data
    private decimal totalRevenue = 0;
    private int totalTreatments = 0;
    private int totalAnimals = 0;
    private int totalBreeders = 0;

    // Chart data
    private List<TreatmentsByDateDto> treatmentsByDate = new();
    private List<TreatmentsByAnimalTypeDto> treatmentsByAnimalType = new();

    // Lists data
    private List<MedicineDto> topMedicines = new();
    private List<BreederStatisticsDto> topBreeders = new();
    private List<MedicineAlertDto> medicineAlerts = new();

    // New report data
    private FinancialReportDto? financialReport = null;
    private List<AnimalTypeStatisticsDto> animalTypeStatistics = new();
    private List<BranchPerformanceDto> branchPerformance = new();
    private List<BranchDto> branches = new();

    // Export request
    private DetailedReportRequestDto exportRequest = new()
    {
        StartDate = DateTime.UtcNow.AddDays(-30),
        EndDate = DateTime.UtcNow,
        ReportType = "financial",
        ExportFormat = "pdf",
        IncludeCharts = true
    };

    protected override async Task OnInitializedAsync()
    {
        await LoadData();
        await LoadReports();
    }

    private async Task LoadData()
    {
        try
        {
            // Load branches for filter
            branches = (await ApiService.GetBranchesAsync()).ToList();
        }
        catch (Exception ex)
        {
            // Handle error silently
        }
    }

    private async Task LoadReports()
    {
        try
        {
            isLoading = true;
            StateHasChanged();

            var branchId = selectedBranchId > 0 ? selectedBranchId : (int?)null;

            // Load all reports in parallel
            var dashboardTask = ApiService.GetDashboardDataAsync();
            var financialTask = ApiService.GetFinancialReportAsync(startDate, endDate, branchId);
            var animalStatsTask = ApiService.GetAnimalTypeStatisticsAsync(startDate, endDate, branchId);
            var branchPerfTask = ApiService.GetBranchPerformanceAsync(startDate, endDate);
            var breederStatsTask = ApiService.GetBreederStatisticsAsync(startDate, endDate, branchId);
            var medicinesTask = ApiService.GetMedicinesAsync();

            await Task.WhenAll(dashboardTask, financialTask, animalStatsTask, branchPerfTask, breederStatsTask, medicinesTask);

            // Load dashboard data for summary
            var dashboardData = await dashboardTask;
            if (dashboardData != null)
            {
                totalRevenue = dashboardData.TotalRevenue;
                totalTreatments = dashboardData.TotalTreatments;
                totalAnimals = dashboardData.TotalAnimals;
                totalBreeders = dashboardData.TotalBreeders;

                treatmentsByDate = dashboardData.TreatmentsByDate;
                treatmentsByAnimalType = dashboardData.TreatmentsByAnimalType;
                medicineAlerts = dashboardData.MedicineAlerts;
            }

            // Load new reports
            financialReport = await financialTask;
            animalTypeStatistics = (await animalStatsTask).ToList();
            branchPerformance = (await branchPerfTask).ToList();
            topBreeders = (await breederStatsTask).ToList();
            topMedicines = (await medicinesTask).OrderByDescending(m => m.Quantity).ToList();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", "حدث خطأ في تحميل التقارير");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private decimal GetDailyAverage()
    {
        var days = (endDate - startDate).Days;
        return days > 0 ? totalRevenue / days : 0;
    }

    private string GetRandomColor(string text)
    {
        var colors = new[]
        {
            "#667eea", "#764ba2", "#28a745", "#17a2b8", "#ffc107", "#dc3545",
            "#6f42c1", "#fd7e14", "#20c997", "#e83e8c", "#6c757d", "#343a40"
        };

        var hash = text.GetHashCode();
        var index = Math.Abs(hash) % colors.Length;
        return colors[index];
    }

    private string GetAlertClass(string alertType)
    {
        return alertType.ToLower() switch
        {
            "lowstock" => "low-stock",
            "expiring" => "expiring",
            "expired" => "expired",
            _ => ""
        };
    }

    private string GetAlertIcon(string alertType)
    {
        return alertType.ToLower() switch
        {
            "lowstock" => "fas fa-exclamation-triangle",
            "expiring" => "fas fa-clock",
            "expired" => "fas fa-times-circle",
            _ => "fas fa-info-circle"
        };
    }

    private string GetAlertMessage(MedicineAlertDto alert)
    {
        return alert.AlertType.ToLower() switch
        {
            "lowstock" => $"المخزون منخفض: {alert.Quantity} من {alert.MinimumStock}",
            "expiring" => $"ينتهي في: {alert.ExpiryDate?.ToString("dd/MM/yyyy")}",
            "expired" => $"منتهي الصلاحية: {alert.ExpiryDate?.ToString("dd/MM/yyyy")}",
            _ => "تنبيه"
        };
    }

    // Export functions
    private async Task ExportTreatmentsByDate()
    {
        await JSRuntime.InvokeVoidAsync("alert", "سيتم تصدير تقرير العلاجات حسب التاريخ");
    }

    private async Task ExportTreatmentsByAnimalType()
    {
        await JSRuntime.InvokeVoidAsync("alert", "سيتم تصدير تقرير العلاجات حسب نوع الحيوان");
    }

    private async Task ExportMedicineUsage()
    {
        await JSRuntime.InvokeVoidAsync("alert", "سيتم تصدير تقرير استخدام الأدوية");
    }

    private async Task ExportTopBreeders()
    {
        await JSRuntime.InvokeVoidAsync("alert", "سيتم تصدير تقرير أفضل المربين");
    }

    private async Task ExportMedicineAlerts()
    {
        await JSRuntime.InvokeVoidAsync("alert", "سيتم تصدير تقرير تنبيهات المخزون");
    }

    private async Task ExportFinancialSummary()
    {
        await JSRuntime.InvokeVoidAsync("alert", "سيتم تصدير الملخص المالي");
    }

    private async Task ExportAnimalTypeStatistics()
    {
        await JSRuntime.InvokeVoidAsync("alert", "سيتم تصدير إحصائيات أنواع الحيوانات قريباً");
    }

    private async Task ExportBranchPerformance()
    {
        await JSRuntime.InvokeVoidAsync("alert", "سيتم تصدير تقرير أداء الفروع قريباً");
    }

    private void ShowExportModal()
    {
        exportRequest.StartDate = startDate;
        exportRequest.EndDate = endDate;
        exportRequest.BranchId = selectedBranchId > 0 ? selectedBranchId : null;
        showExportModal = true;
    }

    private void CloseExportModal()
    {
        showExportModal = false;
    }

    private async Task ProcessExport()
    {
        try
        {
            isExporting = true;
            StateHasChanged();

            var success = await ApiService.ExportReportAsync(exportRequest);

            if (success)
            {
                await JSRuntime.InvokeVoidAsync("alert", "تم إنشاء التقرير بنجاح! سيتم تحميله قريباً.");
                CloseExportModal();
            }
            else
            {
                await JSRuntime.InvokeVoidAsync("alert", "حدث خطأ في إنشاء التقرير");
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", "حدث خطأ غير متوقع");
        }
        finally
        {
            isExporting = false;
            StateHasChanged();
        }
    }
}
