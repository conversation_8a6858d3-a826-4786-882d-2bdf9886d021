using Baytara.Shared.DTOs;

namespace Baytara.Client.Services
{
    public interface IApiService
    {
        // Dashboard
        Task<DashboardDto?> GetDashboardDataAsync();
        
        // Breeders
        Task<IEnumerable<BreederDto>> GetBreedersAsync();
        Task<BreederDto?> GetBreederAsync(int id);
        Task<BreederDto?> CreateBreederAsync(CreateBreederDto createBreederDto);
        Task<BreederDto?> UpdateBreederAsync(int id, UpdateBreederDto updateBreederDto);
        Task<bool> DeleteBreederAsync(int id);
        
        // Animals
        Task<IEnumerable<AnimalDto>> GetAnimalsAsync(int? breederId = null);
        Task<AnimalDto?> GetAnimalAsync(int id);
        Task<AnimalDto?> CreateAnimalAsync(CreateAnimalDto createAnimalDto);
        Task<IEnumerable<AnimalDto>> CreateMultipleAnimalsAsync(CreateMultipleAnimalsDto createMultipleDto);
        Task<AnimalDto?> UpdateAnimalAsync(int id, UpdateAnimalDto updateAnimalDto);
        Task<bool> DeleteAnimalAsync(int id);
        
        // Animal Types
        Task<IEnumerable<AnimalTypeDto>> GetAnimalTypesAsync(int? category = null);
        Task<AnimalTypeDto?> GetAnimalTypeAsync(int id);
        Task<AnimalTypeDto?> CreateAnimalTypeAsync(CreateAnimalTypeDto createAnimalTypeDto);
        Task<AnimalTypeDto?> UpdateAnimalTypeAsync(int id, UpdateAnimalTypeDto updateAnimalTypeDto);
        Task<bool> DeleteAnimalTypeAsync(int id);
        
        // Medicines
        Task<IEnumerable<MedicineDto>> GetMedicinesAsync(bool? lowStock = null, bool? expiring = null);
        Task<MedicineDto?> GetMedicineAsync(int id);
        Task<MedicineDto?> CreateMedicineAsync(CreateMedicineDto createMedicineDto);
        Task<MedicineDto?> UpdateMedicineAsync(int id, UpdateMedicineDto updateMedicineDto);
        Task<bool> DeleteMedicineAsync(int id);
        Task<IEnumerable<MedicineAlertDto>> GetMedicineAlertsAsync();

        // Treatments
        Task<IEnumerable<TreatmentDto>> GetTreatmentsAsync();
        Task<TreatmentDto?> GetTreatmentAsync(int id);
        Task<TreatmentDto> CreateTreatmentAsync(CreateTreatmentDto createTreatmentDto);
        Task<bool> UpdateTreatmentAsync(int id, UpdateTreatmentDto updateTreatmentDto);
        Task<bool> DeleteTreatmentAsync(int id);
        Task<IEnumerable<TreatmentDto>> GetTreatmentsByBreederAsync(int breederId);
        Task<IEnumerable<TreatmentDto>> GetTreatmentsByAnimalAsync(int animalId);

        // Lab Tests
        Task<IEnumerable<LabTestDto>> GetLabTestsAsync();
        Task<LabTestDto?> GetLabTestAsync(int id);
        Task<LabTestDto> CreateLabTestAsync(CreateLabTestDto createLabTestDto);
        Task<bool> UpdateLabTestAsync(int id, UpdateLabTestDto updateLabTestDto);
        Task<bool> DeleteLabTestAsync(int id);
        Task<IEnumerable<LabTestDto>> GetLabTestsByBreederAsync(int breederId);
        Task<IEnumerable<LabTestDto>> GetLabTestsByAnimalAsync(int animalId);
        Task<IEnumerable<LabTestDto>> GetPendingLabTestsAsync();

        // Reports
        Task<FinancialReportDto?> GetFinancialReportAsync(DateTime? startDate = null, DateTime? endDate = null, int? branchId = null);
        Task<IEnumerable<MedicineUsageDto>> GetMedicineUsageAsync(DateTime? startDate = null, DateTime? endDate = null, int? branchId = null);
        Task<IEnumerable<AnimalTypeStatisticsDto>> GetAnimalTypeStatisticsAsync(DateTime? startDate = null, DateTime? endDate = null, int? branchId = null);
        Task<IEnumerable<BranchPerformanceDto>> GetBranchPerformanceAsync(DateTime? startDate = null, DateTime? endDate = null);
        Task<IEnumerable<BreederStatisticsDto>> GetBreederStatisticsAsync(DateTime? startDate = null, DateTime? endDate = null, int? branchId = null, int top = 20);
        Task<bool> ExportReportAsync(DetailedReportRequestDto request);

        // Branches
        Task<IEnumerable<BranchDto>> GetBranchesAsync();
    }
}
