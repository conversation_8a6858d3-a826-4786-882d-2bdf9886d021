using Baytara.Shared.DTOs;
using System.Net.Http.Json;

namespace Baytara.Client.Services
{
    public class ApiService : IApiService
    {
        private readonly HttpClient _httpClient;

        public ApiService(HttpClient httpClient)
        {
            _httpClient = httpClient;
        }

        // Dashboard
        public async Task<DashboardDto?> GetDashboardDataAsync()
        {
            try
            {
                return await _httpClient.GetFromJsonAsync<DashboardDto>("api/dashboard");
            }
            catch
            {
                return null;
            }
        }

        // Breeders
        public async Task<IEnumerable<BreederDto>> GetBreedersAsync()
        {
            try
            {
                Console.WriteLine("Client: استدعاء GetBreedersAsync");
                var result = await _httpClient.GetFromJsonAsync<IEnumerable<BreederDto>>("api/breeders");
                Console.WriteLine($"Client: تم استلام {result?.Count() ?? 0} مربي");
                return result ?? new List<BreederDto>();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Client: خطأ في GetBreedersAsync: {ex.Message}");
                return new List<BreederDto>();
            }
        }

        public async Task<BreederDto?> GetBreederAsync(int id)
        {
            try
            {
                return await _httpClient.GetFromJsonAsync<BreederDto>($"api/breeders/{id}");
            }
            catch
            {
                return null;
            }
        }

        public async Task<BreederDto?> CreateBreederAsync(CreateBreederDto createBreederDto)
        {
            try
            {
                var response = await _httpClient.PostAsJsonAsync("api/breeders", createBreederDto);
                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<BreederDto>();
                }
                return null;
            }
            catch
            {
                return null;
            }
        }

        public async Task<BreederDto?> UpdateBreederAsync(int id, UpdateBreederDto updateBreederDto)
        {
            try
            {
                var response = await _httpClient.PutAsJsonAsync($"api/breeders/{id}", updateBreederDto);
                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<BreederDto>();
                }
                return null;
            }
            catch
            {
                return null;
            }
        }

        public async Task<bool> DeleteBreederAsync(int id)
        {
            try
            {
                var response = await _httpClient.DeleteAsync($"api/breeders/{id}");
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        // Animals
        public async Task<IEnumerable<AnimalDto>> GetAnimalsAsync(int? breederId = null)
        {
            try
            {
                var url = "api/animals";
                if (breederId.HasValue)
                {
                    url += $"?breederId={breederId}";
                }
                var result = await _httpClient.GetFromJsonAsync<IEnumerable<AnimalDto>>(url);
                return result ?? new List<AnimalDto>();
            }
            catch
            {
                return new List<AnimalDto>();
            }
        }

        public async Task<AnimalDto?> GetAnimalAsync(int id)
        {
            try
            {
                return await _httpClient.GetFromJsonAsync<AnimalDto>($"api/animals/{id}");
            }
            catch
            {
                return null;
            }
        }

        public async Task<AnimalDto?> CreateAnimalAsync(CreateAnimalDto createAnimalDto)
        {
            try
            {
                var response = await _httpClient.PostAsJsonAsync("api/animals", createAnimalDto);
                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<AnimalDto>();
                }
                return null;
            }
            catch
            {
                return null;
            }
        }

        public async Task<IEnumerable<AnimalDto>> CreateMultipleAnimalsAsync(CreateMultipleAnimalsDto createMultipleDto)
        {
            try
            {
                var response = await _httpClient.PostAsJsonAsync("api/animals/multiple", createMultipleDto);
                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadFromJsonAsync<IEnumerable<AnimalDto>>();
                    return result ?? new List<AnimalDto>();
                }
                return new List<AnimalDto>();
            }
            catch
            {
                return new List<AnimalDto>();
            }
        }

        public async Task<AnimalDto?> UpdateAnimalAsync(int id, UpdateAnimalDto updateAnimalDto)
        {
            try
            {
                var response = await _httpClient.PutAsJsonAsync($"api/animals/{id}", updateAnimalDto);
                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<AnimalDto>();
                }
                return null;
            }
            catch
            {
                return null;
            }
        }

        public async Task<bool> DeleteAnimalAsync(int id)
        {
            try
            {
                var response = await _httpClient.DeleteAsync($"api/animals/{id}");
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        // Animal Types
        public async Task<IEnumerable<AnimalTypeDto>> GetAnimalTypesAsync(int? category = null)
        {
            try
            {
                var url = "api/animaltypes";
                if (category.HasValue)
                {
                    url += $"?category={category}";
                }
                var result = await _httpClient.GetFromJsonAsync<IEnumerable<AnimalTypeDto>>(url);
                return result ?? new List<AnimalTypeDto>();
            }
            catch
            {
                return new List<AnimalTypeDto>();
            }
        }

        public async Task<AnimalTypeDto?> GetAnimalTypeAsync(int id)
        {
            try
            {
                return await _httpClient.GetFromJsonAsync<AnimalTypeDto>($"api/animaltypes/{id}");
            }
            catch
            {
                return null;
            }
        }

        public async Task<AnimalTypeDto?> CreateAnimalTypeAsync(CreateAnimalTypeDto createAnimalTypeDto)
        {
            try
            {
                var response = await _httpClient.PostAsJsonAsync("api/animaltypes", createAnimalTypeDto);
                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<AnimalTypeDto>();
                }
                return null;
            }
            catch
            {
                return null;
            }
        }

        public async Task<AnimalTypeDto?> UpdateAnimalTypeAsync(int id, UpdateAnimalTypeDto updateAnimalTypeDto)
        {
            try
            {
                var response = await _httpClient.PutAsJsonAsync($"api/animaltypes/{id}", updateAnimalTypeDto);
                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<AnimalTypeDto>();
                }
                return null;
            }
            catch
            {
                return null;
            }
        }

        public async Task<bool> DeleteAnimalTypeAsync(int id)
        {
            try
            {
                var response = await _httpClient.DeleteAsync($"api/animaltypes/{id}");
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        // Medicines
        public async Task<IEnumerable<MedicineDto>> GetMedicinesAsync(bool? lowStock = null, bool? expiring = null)
        {
            try
            {
                var url = "api/medicines";
                var queryParams = new List<string>();
                
                if (lowStock.HasValue)
                    queryParams.Add($"lowStock={lowStock.Value}");
                if (expiring.HasValue)
                    queryParams.Add($"expiring={expiring.Value}");
                
                if (queryParams.Any())
                    url += "?" + string.Join("&", queryParams);
                
                var result = await _httpClient.GetFromJsonAsync<IEnumerable<MedicineDto>>(url);
                return result ?? new List<MedicineDto>();
            }
            catch
            {
                return new List<MedicineDto>();
            }
        }

        public async Task<MedicineDto?> GetMedicineAsync(int id)
        {
            try
            {
                return await _httpClient.GetFromJsonAsync<MedicineDto>($"api/medicines/{id}");
            }
            catch
            {
                return null;
            }
        }

        public async Task<MedicineDto?> CreateMedicineAsync(CreateMedicineDto createMedicineDto)
        {
            try
            {
                var response = await _httpClient.PostAsJsonAsync("api/medicines", createMedicineDto);
                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<MedicineDto>();
                }
                return null;
            }
            catch
            {
                return null;
            }
        }

        public async Task<MedicineDto?> UpdateMedicineAsync(int id, UpdateMedicineDto updateMedicineDto)
        {
            try
            {
                var response = await _httpClient.PutAsJsonAsync($"api/medicines/{id}", updateMedicineDto);
                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<MedicineDto>();
                }
                return null;
            }
            catch
            {
                return null;
            }
        }

        public async Task<bool> DeleteMedicineAsync(int id)
        {
            try
            {
                var response = await _httpClient.DeleteAsync($"api/medicines/{id}");
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        public async Task<IEnumerable<MedicineAlertDto>> GetMedicineAlertsAsync()
        {
            try
            {
                var result = await _httpClient.GetFromJsonAsync<IEnumerable<MedicineAlertDto>>("api/medicines/alerts");
                return result ?? new List<MedicineAlertDto>();
            }
            catch
            {
                return new List<MedicineAlertDto>();
            }
        }

        // Treatments - Placeholder implementations
        public async Task<IEnumerable<TreatmentDto>> GetTreatmentsAsync()
        {
            // TODO: Implement when TreatmentsController is ready
            return new List<TreatmentDto>();
        }

        public async Task<TreatmentDto?> GetTreatmentAsync(int id)
        {
            // TODO: Implement when TreatmentsController is ready
            return null;
        }

        public async Task<TreatmentDto> CreateTreatmentAsync(CreateTreatmentDto createTreatmentDto)
        {
            // TODO: Implement when TreatmentsController is ready
            throw new NotImplementedException();
        }

        public async Task<bool> UpdateTreatmentAsync(int id, UpdateTreatmentDto updateTreatmentDto)
        {
            // TODO: Implement when TreatmentsController is ready
            return false;
        }

        public async Task<bool> DeleteTreatmentAsync(int id)
        {
            // TODO: Implement when TreatmentsController is ready
            return false;
        }

        public async Task<IEnumerable<TreatmentDto>> GetTreatmentsByBreederAsync(int breederId)
        {
            // TODO: Implement when TreatmentsController is ready
            return new List<TreatmentDto>();
        }

        public async Task<IEnumerable<TreatmentDto>> GetTreatmentsByAnimalAsync(int animalId)
        {
            // TODO: Implement when TreatmentsController is ready
            return new List<TreatmentDto>();
        }

        // Lab Tests - Placeholder implementations
        public async Task<IEnumerable<LabTestDto>> GetLabTestsAsync()
        {
            // TODO: Implement when LabTestsController is ready
            return new List<LabTestDto>();
        }

        public async Task<LabTestDto?> GetLabTestAsync(int id)
        {
            // TODO: Implement when LabTestsController is ready
            return null;
        }

        public async Task<LabTestDto> CreateLabTestAsync(CreateLabTestDto createLabTestDto)
        {
            // TODO: Implement when LabTestsController is ready
            throw new NotImplementedException();
        }

        public async Task<bool> UpdateLabTestAsync(int id, UpdateLabTestDto updateLabTestDto)
        {
            // TODO: Implement when LabTestsController is ready
            return false;
        }

        public async Task<bool> DeleteLabTestAsync(int id)
        {
            // TODO: Implement when LabTestsController is ready
            return false;
        }

        public async Task<IEnumerable<LabTestDto>> GetLabTestsByBreederAsync(int breederId)
        {
            // TODO: Implement when LabTestsController is ready
            return new List<LabTestDto>();
        }

        public async Task<IEnumerable<LabTestDto>> GetLabTestsByAnimalAsync(int animalId)
        {
            // TODO: Implement when LabTestsController is ready
            return new List<LabTestDto>();
        }

        public async Task<IEnumerable<LabTestDto>> GetPendingLabTestsAsync()
        {
            // TODO: Implement when LabTestsController is ready
            return new List<LabTestDto>();
        }

        // Reports implementations
        public async Task<FinancialReportDto?> GetFinancialReportAsync(DateTime? startDate = null, DateTime? endDate = null, int? branchId = null)
        {
            try
            {
                var queryParams = new List<string>();
                if (startDate.HasValue) queryParams.Add($"startDate={startDate.Value:yyyy-MM-dd}");
                if (endDate.HasValue) queryParams.Add($"endDate={endDate.Value:yyyy-MM-dd}");
                if (branchId.HasValue) queryParams.Add($"branchId={branchId.Value}");

                var query = queryParams.Any() ? "?" + string.Join("&", queryParams) : "";
                var result = await _httpClient.GetFromJsonAsync<FinancialReportDto>($"api/reports/financial{query}");
                return result;
            }
            catch
            {
                return null;
            }
        }

        public async Task<IEnumerable<MedicineUsageDto>> GetMedicineUsageAsync(DateTime? startDate = null, DateTime? endDate = null, int? branchId = null)
        {
            try
            {
                var queryParams = new List<string>();
                if (startDate.HasValue) queryParams.Add($"startDate={startDate.Value:yyyy-MM-dd}");
                if (endDate.HasValue) queryParams.Add($"endDate={endDate.Value:yyyy-MM-dd}");
                if (branchId.HasValue) queryParams.Add($"branchId={branchId.Value}");

                var query = queryParams.Any() ? "?" + string.Join("&", queryParams) : "";
                var result = await _httpClient.GetFromJsonAsync<IEnumerable<MedicineUsageDto>>($"api/reports/medicine-usage{query}");
                return result ?? new List<MedicineUsageDto>();
            }
            catch
            {
                return new List<MedicineUsageDto>();
            }
        }

        public async Task<IEnumerable<AnimalTypeStatisticsDto>> GetAnimalTypeStatisticsAsync(DateTime? startDate = null, DateTime? endDate = null, int? branchId = null)
        {
            try
            {
                var queryParams = new List<string>();
                if (startDate.HasValue) queryParams.Add($"startDate={startDate.Value:yyyy-MM-dd}");
                if (endDate.HasValue) queryParams.Add($"endDate={endDate.Value:yyyy-MM-dd}");
                if (branchId.HasValue) queryParams.Add($"branchId={branchId.Value}");

                var query = queryParams.Any() ? "?" + string.Join("&", queryParams) : "";
                var result = await _httpClient.GetFromJsonAsync<IEnumerable<AnimalTypeStatisticsDto>>($"api/reports/animal-type-statistics{query}");
                return result ?? new List<AnimalTypeStatisticsDto>();
            }
            catch
            {
                return new List<AnimalTypeStatisticsDto>();
            }
        }

        public async Task<IEnumerable<BranchPerformanceDto>> GetBranchPerformanceAsync(DateTime? startDate = null, DateTime? endDate = null)
        {
            try
            {
                var queryParams = new List<string>();
                if (startDate.HasValue) queryParams.Add($"startDate={startDate.Value:yyyy-MM-dd}");
                if (endDate.HasValue) queryParams.Add($"endDate={endDate.Value:yyyy-MM-dd}");

                var query = queryParams.Any() ? "?" + string.Join("&", queryParams) : "";
                var result = await _httpClient.GetFromJsonAsync<IEnumerable<BranchPerformanceDto>>($"api/reports/branch-performance{query}");
                return result ?? new List<BranchPerformanceDto>();
            }
            catch
            {
                return new List<BranchPerformanceDto>();
            }
        }

        public async Task<IEnumerable<BreederStatisticsDto>> GetBreederStatisticsAsync(DateTime? startDate = null, DateTime? endDate = null, int? branchId = null, int top = 20)
        {
            try
            {
                var queryParams = new List<string>();
                if (startDate.HasValue) queryParams.Add($"startDate={startDate.Value:yyyy-MM-dd}");
                if (endDate.HasValue) queryParams.Add($"endDate={endDate.Value:yyyy-MM-dd}");
                if (branchId.HasValue) queryParams.Add($"branchId={branchId.Value}");
                queryParams.Add($"top={top}");

                var query = queryParams.Any() ? "?" + string.Join("&", queryParams) : "";
                var result = await _httpClient.GetFromJsonAsync<IEnumerable<BreederStatisticsDto>>($"api/reports/breeder-statistics{query}");
                return result ?? new List<BreederStatisticsDto>();
            }
            catch
            {
                return new List<BreederStatisticsDto>();
            }
        }

        public async Task<bool> ExportReportAsync(DetailedReportRequestDto request)
        {
            try
            {
                var response = await _httpClient.PostAsJsonAsync("api/reports/export", request);
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        // Branches implementation
        public async Task<IEnumerable<BranchDto>> GetBranchesAsync()
        {
            try
            {
                var result = await _httpClient.GetFromJsonAsync<IEnumerable<BranchDto>>("api/branches");
                return result ?? new List<BranchDto>();
            }
            catch
            {
                return new List<BranchDto>();
            }
        }
    }
}
