using Baytara.API.Data;
using Baytara.Shared.DTOs;
using Baytara.Shared.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace Baytara.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class BreedersController : ControllerBase
    {
        private readonly ApplicationDbContext _context;

        public BreedersController(ApplicationDbContext context)
        {
            _context = context;
        }

        [HttpGet]
        [AllowAnonymous] // مؤقت للاختبار
        public async Task<ActionResult<IEnumerable<BreederDto>>> GetBreeders()
        {
            try
            {
                Console.WriteLine("تم استدعاء GetBreeders");

                var breeders = await _context.Breeders
                    .Include(b => b.Branch)
                    .Where(b => b.IsActive)
                    .Select(b => new BreederDto
                    {
                        Id = b.Id,
                        Name = b.Name,
                        PhoneNumber = b.PhoneNumber,
                        NationalId = b.NationalId,
                        Location = b.Location,
                        BranchId = b.BranchId,
                        BranchName = b.Branch.Name,
                        IsActive = b.IsActive,
                        CreatedAt = b.CreatedAt,
                        AnimalsCount = b.Animals.Count(a => a.IsActive),
                        TreatmentsCount = b.Treatments.Count()
                    })
                    .OrderByDescending(b => b.CreatedAt)
                    .ToListAsync();

                Console.WriteLine($"تم العثور على {breeders.Count} مربي");
                return Ok(breeders);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في GetBreeders: {ex.Message}");
                return StatusCode(500, "حدث خطأ في الخادم");
            }
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<BreederDto>> GetBreeder(int id)
        {
            var userBranchId = GetUserBranchId();
            
            var breeder = await _context.Breeders
                .Include(b => b.Branch)
                .Where(b => b.Id == id && (!userBranchId.HasValue || b.BranchId == userBranchId.Value))
                .Select(b => new BreederDto
                {
                    Id = b.Id,
                    Name = b.Name,
                    PhoneNumber = b.PhoneNumber,
                    NationalId = b.NationalId,
                    Location = b.Location,
                    BranchId = b.BranchId,
                    BranchName = b.Branch.Name,
                    IsActive = b.IsActive,
                    CreatedAt = b.CreatedAt,
                    AnimalsCount = b.Animals.Count(a => a.IsActive),
                    TreatmentsCount = b.Treatments.Count()
                })
                .FirstOrDefaultAsync();

            if (breeder == null)
            {
                return NotFound();
            }

            return Ok(breeder);
        }

        [HttpPost]
        public async Task<ActionResult<BreederDto>> CreateBreeder([FromBody] CreateBreederDto createBreederDto)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var userBranchId = GetUserBranchId();
            
            // If user is not system admin, force their branch
            if (userBranchId.HasValue)
            {
                createBreederDto.BranchId = userBranchId.Value;
            }

            // Check if branch exists
            var branchExists = await _context.Branches.AnyAsync(b => b.Id == createBreederDto.BranchId && b.IsActive);
            if (!branchExists)
            {
                return BadRequest("الفرع المحدد غير موجود");
            }

            var breeder = new Breeder
            {
                Name = createBreederDto.Name,
                PhoneNumber = createBreederDto.PhoneNumber,
                NationalId = createBreederDto.NationalId,
                Location = createBreederDto.Location,
                BranchId = createBreederDto.BranchId,
                IsActive = true,
                CreatedAt = DateTime.UtcNow
            };

            _context.Breeders.Add(breeder);
            await _context.SaveChangesAsync();

            var breederDto = await GetBreeder(breeder.Id);
            return CreatedAtAction(nameof(GetBreeder), new { id = breeder.Id }, breederDto.Value);
        }

        [HttpPut("{id}")]
        public async Task<ActionResult<BreederDto>> UpdateBreeder(int id, [FromBody] UpdateBreederDto updateBreederDto)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var userBranchId = GetUserBranchId();
            
            var breeder = await _context.Breeders
                .Where(b => b.Id == id && (!userBranchId.HasValue || b.BranchId == userBranchId.Value))
                .FirstOrDefaultAsync();

            if (breeder == null)
            {
                return NotFound();
            }

            // If user is not system admin, don't allow branch change
            if (userBranchId.HasValue)
            {
                updateBreederDto.BranchId = breeder.BranchId;
            }

            // Check if branch exists
            var branchExists = await _context.Branches.AnyAsync(b => b.Id == updateBreederDto.BranchId && b.IsActive);
            if (!branchExists)
            {
                return BadRequest("الفرع المحدد غير موجود");
            }

            breeder.Name = updateBreederDto.Name;
            breeder.PhoneNumber = updateBreederDto.PhoneNumber;
            breeder.NationalId = updateBreederDto.NationalId;
            breeder.Location = updateBreederDto.Location;
            breeder.BranchId = updateBreederDto.BranchId;
            breeder.IsActive = updateBreederDto.IsActive;
            breeder.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            var breederDto = await GetBreeder(id);
            return Ok(breederDto.Value);
        }

        [HttpDelete("{id}")]
        public async Task<ActionResult> DeleteBreeder(int id)
        {
            var userBranchId = GetUserBranchId();
            
            var breeder = await _context.Breeders
                .Where(b => b.Id == id && (!userBranchId.HasValue || b.BranchId == userBranchId.Value))
                .FirstOrDefaultAsync();

            if (breeder == null)
            {
                return NotFound();
            }

            // Check if breeder has animals or treatments
            var hasAnimals = await _context.Animals.AnyAsync(a => a.BreederId == id);
            var hasTreatments = await _context.Treatments.AnyAsync(t => t.BreederId == id);

            if (hasAnimals || hasTreatments)
            {
                // Soft delete
                breeder.IsActive = false;
                breeder.UpdatedAt = DateTime.UtcNow;
            }
            else
            {
                // Hard delete
                _context.Breeders.Remove(breeder);
            }

            await _context.SaveChangesAsync();
            return NoContent();
        }

        private int? GetUserBranchId()
        {
            var userRole = User.FindFirst(System.Security.Claims.ClaimTypes.Role)?.Value;
            if (userRole == UserRole.SystemAdmin.ToString())
            {
                return null; // System admin can see all branches
            }

            var branchIdClaim = User.FindFirst("BranchId")?.Value;
            if (int.TryParse(branchIdClaim, out int branchId))
            {
                return branchId;
            }

            return null;
        }
    }
}
