@page "/"

<PageTitle>الرئيسية - بيطره</PageTitle>

<div class="home-page">
    <div class="welcome-section">
        <h1>مرحباً بك في نظام بيطره</h1>
        <p>نظام شامل لإدارة العيادات البيطرية</p>
    </div>

    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-users"></i>
            </div>
            <div class="stat-content">
                <h3>@totalBreeders</h3>
                <p>إجمالي المربين</p>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-paw"></i>
            </div>
            <div class="stat-content">
                <h3>@totalAnimals</h3>
                <p>إجمالي الحيوانات</p>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-pills"></i>
            </div>
            <div class="stat-content">
                <h3>@totalMedicines</h3>
                <p>إجمالي الأدوية</p>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-stethoscope"></i>
            </div>
            <div class="stat-content">
                <h3>@totalTreatments</h3>
                <p>إجمالي العلاجات</p>
            </div>
        </div>
    </div>

    <div class="features-section">
        <h2>المميزات الرئيسية</h2>
        <div class="features-grid">
            <div class="feature-card">
                <i class="fas fa-user-md"></i>
                <h3>إدارة المربين</h3>
                <p>تسجيل وإدارة بيانات المربين والحيوانات</p>
            </div>
            <div class="feature-card">
                <i class="fas fa-capsules"></i>
                <h3>إدارة الأدوية</h3>
                <p>تتبع المخزون والتنبيهات</p>
            </div>
            <div class="feature-card">
                <i class="fas fa-heart-pulse"></i>
                <h3>نظام العلاج</h3>
                <p>تشخيص وعلاج شامل</p>
            </div>
            <div class="feature-card">
                <i class="fas fa-chart-bar"></i>
                <h3>التقارير</h3>
                <p>تقارير مفصلة وإحصائيات</p>
            </div>
        </div>
    </div>
</div>

<style>
    .home-page {
        padding: 20px;
    }

    .welcome-section {
        text-align: center;
        margin-bottom: 40px;
        padding: 40px 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
    }

    .welcome-section h1 {
        font-size: 2.5rem;
        margin-bottom: 10px;
    }

    .welcome-section p {
        font-size: 1.2rem;
        opacity: 0.9;
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin-bottom: 40px;
    }

    .stat-card {
        background: white;
        padding: 25px;
        border-radius: 15px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        display: flex;
        align-items: center;
        gap: 20px;
        transition: transform 0.3s ease;
    }

    .stat-card:hover {
        transform: translateY(-5px);
    }

    .stat-icon {
        width: 60px;
        height: 60px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.5rem;
    }

    .stat-content h3 {
        font-size: 2rem;
        font-weight: 700;
        color: #333;
        margin-bottom: 5px;
    }

    .stat-content p {
        color: #666;
        margin-bottom: 0;
    }

    .features-section h2 {
        text-align: center;
        margin-bottom: 30px;
        color: #333;
    }

    .features-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 25px;
    }

    .feature-card {
        background: white;
        padding: 30px;
        border-radius: 15px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        text-align: center;
        transition: transform 0.3s ease;
    }

    .feature-card:hover {
        transform: translateY(-5px);
    }

    .feature-card i {
        font-size: 3rem;
        color: #667eea;
        margin-bottom: 20px;
    }

    .feature-card h3 {
        color: #333;
        margin-bottom: 15px;
    }

    .feature-card p {
        color: #666;
        line-height: 1.6;
    }

    @@media (max-width: 768px) {
        .welcome-section h1 {
            font-size: 2rem;
        }

        .stats-grid {
            grid-template-columns: 1fr;
        }

        .features-grid {
            grid-template-columns: 1fr;
        }
    }
</style>

@code {
    private int totalBreeders = 25;
    private int totalAnimals = 150;
    private int totalMedicines = 45;
    private int totalTreatments = 89;
}
