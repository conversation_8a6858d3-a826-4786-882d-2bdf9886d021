<div class="top-row ps-3 navbar navbar-dark">
    <div class="container-fluid">
        <a class="navbar-brand" href="">بيطره</a>
    </div>
</div>

<input type="checkbox" title="Navigation menu" class="navbar-toggler" />

<div class="nav-scrollable" onclick="document.querySelector('.navbar-toggler').click()">
    <nav class="flex-column">
        <div class="nav-item px-3">
            <NavLink class="nav-link" href="" Match="NavLinkMatch.All">
                <span class="bi bi-house-door-fill-nav-menu" aria-hidden="true"></span> الرئيسية
            </NavLink>
        </div>
        <div class="nav-item px-3">
            <NavLink class="nav-link" href="breeders">
                <span class="bi bi-people-fill" aria-hidden="true"></span> المربين
            </NavLink>
        </div>
        <div class="nav-item px-3">
            <NavLink class="nav-link" href="medicines">
                <span class="bi bi-capsule" aria-hidden="true"></span> الأدوية
            </NavLink>
        </div>
        <div class="nav-item px-3">
            <NavLink class="nav-link" href="treatments">
                <span class="bi bi-heart-pulse" aria-hidden="true"></span> العلاجات
            </NavLink>
        </div>
        <div class="nav-item px-3">
            <NavLink class="nav-link" href="reports">
                <span class="bi bi-graph-up" aria-hidden="true"></span> التقارير
            </NavLink>
        </div>
    </nav>
</div>

<style>
    .navbar-toggler {
        appearance: none;
        cursor: pointer;
        width: 3.5rem;
        height: 2.5rem;
        color: white;
        position: absolute;
        top: 0.5rem;
        left: 0.5rem;
        border: 1px solid rgba(255, 255, 255, 0.1);
        background: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.55%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='m4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e") no-repeat center/1.75rem rgba(255, 255, 255, 0.1);
    }

    .navbar-toggler:checked {
        background-color: rgba(255, 255, 255, 0.5);
    }

    .top-row {
        height: 3.5rem;
        background-color: rgba(0,0,0,0.4);
    }

    .navbar-brand {
        font-size: 1.1rem;
        color: white;
        text-decoration: none;
    }

    .nav-scrollable {
        display: none;
        height: calc(100vh - 3.5rem);
        overflow-y: auto;
    }

    .navbar-toggler:checked ~ .nav-scrollable {
        display: block;
    }

    .nav-item {
        font-size: 0.9rem;
        padding-bottom: 0.5rem;
    }

    .nav-item:first-of-type {
        padding-top: 1rem;
    }

    .nav-item:last-of-type {
        padding-bottom: 1rem;
    }

    .nav-link {
        border-radius: 4px;
        height: 3rem;
        display: flex;
        align-items: center;
        line-height: 3rem;
        color: #d7d7d7;
        text-decoration: none;
    }

    .nav-link:hover {
        background-color: rgba(255,255,255,0.1);
        color: white;
    }

    .nav-link.active {
        background-color: rgba(255,255,255,0.25);
        color: white;
    }

    @@media (min-width: 641px) {
        .navbar-toggler {
            display: none;
        }

        .nav-scrollable {
            display: block;
        }
    }
</style>
