using Baytara.API.Data;
using Baytara.Shared.DTOs;
using Baytara.Shared.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace Baytara.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class DashboardController : ControllerBase
    {
        private readonly ApplicationDbContext _context;

        public DashboardController(ApplicationDbContext context)
        {
            _context = context;
        }

        [HttpGet]
        public async Task<ActionResult<DashboardDto>> GetDashboardData()
        {
            var userBranchId = GetUserBranchId();
            var startOfMonth = new DateTime(DateTime.UtcNow.Year, DateTime.UtcNow.Month, 1);
            var endOfMonth = startOfMonth.AddMonths(1).AddDays(-1);

            // Base queries with branch filtering
            var breedersQuery = _context.Breeders.Where(b => b.IsActive);
            var animalsQuery = _context.Animals.Where(a => a.IsActive);
            var treatmentsQuery = _context.Treatments.AsQueryable();
            var labTestsQuery = _context.LabTests.AsQueryable();
            var medicinesQuery = _context.Medicines.Where(m => m.IsActive);

            if (userBranchId.HasValue)
            {
                breedersQuery = breedersQuery.Where(b => b.BranchId == userBranchId.Value);
                animalsQuery = animalsQuery.Where(a => a.Breeder.BranchId == userBranchId.Value);
                treatmentsQuery = treatmentsQuery.Where(t => t.BranchId == userBranchId.Value);
                labTestsQuery = labTestsQuery.Where(lt => lt.Animal.Breeder.BranchId == userBranchId.Value);
                medicinesQuery = medicinesQuery.Where(m => m.BranchId == userBranchId.Value);
            }

            // Get basic counts
            var totalBreeders = await breedersQuery.CountAsync();
            var totalAnimals = await animalsQuery.CountAsync();
            var totalTreatments = await treatmentsQuery.CountAsync();
            var pendingTreatments = await treatmentsQuery.CountAsync(t => t.Status == TreatmentStatus.Pending || t.Status == TreatmentStatus.InProgress);
            var completedTreatments = await treatmentsQuery.CountAsync(t => t.Status == TreatmentStatus.Completed);
            var totalLabTests = await labTestsQuery.CountAsync();
            var pendingLabTests = await labTestsQuery.CountAsync(lt => lt.Status == LabTestStatus.Pending || lt.Status == LabTestStatus.InProgress);

            // Medicine alerts
            var medicines = await medicinesQuery.ToListAsync();
            var lowStockMedicines = medicines.Count(m => m.Quantity <= m.MinimumStock);
            var expiringMedicines = medicines.Count(m => m.ExpiryDate.HasValue && m.ExpiryDate.Value <= DateTime.UtcNow.AddDays(30));

            // Revenue calculations
            var totalRevenue = await treatmentsQuery.SumAsync(t => t.TotalCost);
            var monthlyRevenue = await treatmentsQuery
                .Where(t => t.TreatmentDate >= startOfMonth && t.TreatmentDate <= endOfMonth)
                .SumAsync(t => t.TotalCost);

            // Treatments by date (last 30 days)
            var thirtyDaysAgo = DateTime.UtcNow.AddDays(-30);
            var treatmentsByDate = await treatmentsQuery
                .Where(t => t.TreatmentDate >= thirtyDaysAgo)
                .GroupBy(t => t.TreatmentDate.Date)
                .Select(g => new TreatmentsByDateDto
                {
                    Date = g.Key,
                    Count = g.Count(),
                    Revenue = g.Sum(t => t.TotalCost)
                })
                .OrderBy(t => t.Date)
                .ToListAsync();

            // Treatments by animal type
            var treatmentsByAnimalType = await treatmentsQuery
                .Include(t => t.Animal)
                .ThenInclude(a => a.AnimalType)
                .GroupBy(t => t.Animal.AnimalType.Name)
                .Select(g => new TreatmentsByAnimalTypeDto
                {
                    AnimalTypeName = g.Key,
                    Count = g.Count(),
                    Revenue = g.Sum(t => t.TotalCost)
                })
                .OrderByDescending(t => t.Count)
                .Take(10)
                .ToListAsync();

            // Recent treatments
            var recentTreatments = await treatmentsQuery
                .Include(t => t.Animal)
                .ThenInclude(a => a.AnimalType)
                .Include(t => t.Breeder)
                .Include(t => t.Doctor)
                .OrderByDescending(t => t.TreatmentDate)
                .Take(10)
                .Select(t => new RecentTreatmentDto
                {
                    Id = t.Id,
                    AnimalName = t.Animal.Name ?? "غير محدد",
                    AnimalTypeName = t.Animal.AnimalType.Name,
                    BreederName = t.Breeder.Name,
                    DoctorName = t.Doctor.FullName,
                    TotalCost = t.TotalCost,
                    TreatmentDate = t.TreatmentDate,
                    Status = t.Status.ToString()
                })
                .ToListAsync();

            // Medicine alerts
            var medicineAlerts = new List<MedicineAlertDto>();
            foreach (var medicine in medicines)
            {
                if (medicine.Quantity <= medicine.MinimumStock)
                {
                    medicineAlerts.Add(new MedicineAlertDto
                    {
                        Id = medicine.Id,
                        Name = medicine.Name,
                        Quantity = medicine.Quantity,
                        MinimumStock = medicine.MinimumStock,
                        ExpiryDate = medicine.ExpiryDate,
                        AlertType = "LowStock",
                        BranchName = medicine.Branch?.Name ?? ""
                    });
                }

                if (medicine.ExpiryDate.HasValue)
                {
                    var daysUntilExpiry = (medicine.ExpiryDate.Value - DateTime.UtcNow).Days;
                    if (daysUntilExpiry <= 0)
                    {
                        medicineAlerts.Add(new MedicineAlertDto
                        {
                            Id = medicine.Id,
                            Name = medicine.Name,
                            Quantity = medicine.Quantity,
                            MinimumStock = medicine.MinimumStock,
                            ExpiryDate = medicine.ExpiryDate,
                            AlertType = "Expired",
                            BranchName = medicine.Branch?.Name ?? ""
                        });
                    }
                    else if (daysUntilExpiry <= 30)
                    {
                        medicineAlerts.Add(new MedicineAlertDto
                        {
                            Id = medicine.Id,
                            Name = medicine.Name,
                            Quantity = medicine.Quantity,
                            MinimumStock = medicine.MinimumStock,
                            ExpiryDate = medicine.ExpiryDate,
                            AlertType = "Expiring",
                            BranchName = medicine.Branch?.Name ?? ""
                        });
                    }
                }
            }

            var dashboard = new DashboardDto
            {
                TotalBreeders = totalBreeders,
                TotalAnimals = totalAnimals,
                TotalTreatments = totalTreatments,
                PendingTreatments = pendingTreatments,
                CompletedTreatments = completedTreatments,
                TotalLabTests = totalLabTests,
                PendingLabTests = pendingLabTests,
                LowStockMedicines = lowStockMedicines,
                ExpiringMedicines = expiringMedicines,
                TotalRevenue = totalRevenue,
                MonthlyRevenue = monthlyRevenue,
                TreatmentsByDate = treatmentsByDate,
                TreatmentsByAnimalType = treatmentsByAnimalType,
                RecentTreatments = recentTreatments,
                MedicineAlerts = medicineAlerts.Take(10).ToList()
            };

            return Ok(dashboard);
        }

        private int? GetUserBranchId()
        {
            var userRole = User.FindFirst(System.Security.Claims.ClaimTypes.Role)?.Value;
            if (userRole == UserRole.SystemAdmin.ToString())
            {
                return null; // System admin can see all branches
            }

            var branchIdClaim = User.FindFirst("BranchId")?.Value;
            if (int.TryParse(branchIdClaim, out int branchId))
            {
                return branchId;
            }

            return null;
        }
    }
}
