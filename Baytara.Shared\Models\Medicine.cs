using System.ComponentModel.DataAnnotations;

namespace Baytara.Shared.Models
{
    public class Medicine
    {
        public int Id { get; set; }
        
        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;
        
        [StringLength(500)]
        public string? Description { get; set; }
        
        [StringLength(50)]
        public string? Unit { get; set; }
        
        public int Quantity { get; set; }
        
        public int MinimumStock { get; set; }
        
        public decimal UnitPrice { get; set; }
        
        public DateTime? ExpiryDate { get; set; }
        
        [StringLength(50)]
        public string? BatchNumber { get; set; }
        
        [StringLength(100)]
        public string? Manufacturer { get; set; }
        
        public int BranchId { get; set; }
        public Branch Branch { get; set; } = null!;
        
        public bool IsActive { get; set; } = true;
        
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime? UpdatedAt { get; set; }
        
        // Navigation properties
        public ICollection<TreatmentMedicine> TreatmentMedicines { get; set; } = new List<TreatmentMedicine>();
    }
}
