{"format": 1, "restore": {"D:\\Baytara\\BaytaraSimple\\Server\\BaytaraSimple.Server.csproj": {}}, "projects": {"D:\\Baytara\\BaytaraSimple\\Client\\BaytaraSimple.Client.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Baytara\\BaytaraSimple\\Client\\BaytaraSimple.Client.csproj", "projectName": "BaytaraSimple.Client", "projectPath": "D:\\Baytara\\BaytaraSimple\\Client\\BaytaraSimple.Client.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Baytara\\BaytaraSimple\\Client\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files\\DevExpress 24.1\\Components\\Offline Packages", "C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages", "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 24.1.config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 24.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\DevExpress 24.1\\Components\\System\\Components\\Packages": {}, "C:\\Program Files\\DevExpress 24.2\\Components\\System\\Components\\Packages": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"D:\\Baytara\\BaytaraSimple\\Shared\\BaytaraSimple.Shared.csproj": {"projectPath": "D:\\Baytara\\BaytaraSimple\\Shared\\BaytaraSimple.Shared.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Microsoft.AspNetCore.Components.WebAssembly": {"target": "Package", "version": "[6.0.36, )"}, "Microsoft.AspNetCore.Components.WebAssembly.DevServer": {"suppressParent": "All", "target": "Package", "version": "[6.0.36, )"}, "Microsoft.NET.ILLink.Analyzers": {"suppressParent": "All", "target": "Package", "version": "[7.0.100-1.23211.1, )", "autoReferenced": true}, "Microsoft.NET.ILLink.Tasks": {"suppressParent": "All", "target": "Package", "version": "[7.0.100-1.23211.1, )", "autoReferenced": true}, "Microsoft.NET.Sdk.WebAssembly.Pack": {"suppressParent": "All", "target": "Package", "version": "[9.0.6, )", "autoReferenced": true}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.NETCore.App.Runtime.Mono.browser-wasm", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}, "runtimes": {"browser-wasm": {"#import": []}}}, "D:\\Baytara\\BaytaraSimple\\Server\\BaytaraSimple.Server.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Baytara\\BaytaraSimple\\Server\\BaytaraSimple.Server.csproj", "projectName": "BaytaraSimple.Server", "projectPath": "D:\\Baytara\\BaytaraSimple\\Server\\BaytaraSimple.Server.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Baytara\\BaytaraSimple\\Server\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files\\DevExpress 24.1\\Components\\Offline Packages", "C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages", "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 24.1.config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 24.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\DevExpress 24.1\\Components\\System\\Components\\Packages": {}, "C:\\Program Files\\DevExpress 24.2\\Components\\System\\Components\\Packages": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"D:\\Baytara\\BaytaraSimple\\Client\\BaytaraSimple.Client.csproj": {"projectPath": "D:\\Baytara\\BaytaraSimple\\Client\\BaytaraSimple.Client.csproj"}, "D:\\Baytara\\BaytaraSimple\\Shared\\BaytaraSimple.Shared.csproj": {"projectPath": "D:\\Baytara\\BaytaraSimple\\Shared\\BaytaraSimple.Shared.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Microsoft.AspNetCore.Components.WebAssembly.Server": {"target": "Package", "version": "[6.0.36, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "D:\\Baytara\\BaytaraSimple\\Shared\\BaytaraSimple.Shared.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Baytara\\BaytaraSimple\\Shared\\BaytaraSimple.Shared.csproj", "projectName": "BaytaraSimple.Shared", "projectPath": "D:\\Baytara\\BaytaraSimple\\Shared\\BaytaraSimple.Shared.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Baytara\\BaytaraSimple\\Shared\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files\\DevExpress 24.1\\Components\\Offline Packages", "C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages", "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 24.1.config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 24.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\DevExpress 24.1\\Components\\System\\Components\\Packages": {}, "C:\\Program Files\\DevExpress 24.2\\Components\\System\\Components\\Packages": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}}}