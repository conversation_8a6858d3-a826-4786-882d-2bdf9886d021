{"GlobalPropertiesHash": "L9HrQ5aqZc9TgknuMYvz5aUY2F6jJrq7N4QVMlQVpls=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["scVjqjm24wQ1vxh8HHGFMFIB8GTF7p7f1+n18ENHOuM=", "ns7vM7Wi+m/0LyMftyamlUwyP03nB/rTpkQpXeNOnFo=", "sH9JNMKbpZF7MCdrNMOQmWgNpr9ZE4hIsp5tRTa7fRo=", "hq3ztsa4CJissj0DDeOdPl15P9ydqE1gxy/y+5fG5nc=", "o4BCfy9AzQnnipOSRC5cdHFYhacJ9ii9u+GlUkyAyhs=", "XT0r8+PAKK/q+87AhXFy9zBIMgMOSHP6dqwnNXE/TP0=", "6DjkhZvUxcEtJyoiovHQwu4ygnhvSlSY9TWjKB+fTt4=", "4PSVM+g5YEwAFQUFRlTgjRdpGLJOBP1WUz6ZG3SEK00=", "lk2f1HFLVyPt1NzVmsdLEsOQoWgmqpP+2D0lfK8/ZLU=", "vm2uSZ3RqeVy+HT9UoHFSPVKX0zTrKDmp4kRjpPrBHs=", "me4s8KIv8hkTtjJ+p7vA28vmciQnCHrB3BOVN+dSooU=", "VzGDZlAroR98Sp+MHQCxB7I65LtNpQRa5821TjwXtCU=", "O5EVGb49lIc/jIZFc5KmjgRlZFJLKXKXsAd48IhC4OQ=", "FIo94CFsHyT0G2PxjcWsfjhR2ZDr/6Knvn2J6nKMGMo=", "jN0HwsmcbncWy2/fF1EEHEaCvBTzqd2ofVcDfjAYVC4=", "fEe85kK77XT0rn95pto/X8UxYwJqxoHYtXs1ArOeBC0=", "v/W8V63U7VnwEkPT3zo8rb3+ehYf8vPztCl6wun104A=", "iyVsykyjfw0H/FN2xWVAuutxICIObk5nP6w2yCHRN4I=", "SRa43QjZpD+uJ/mKQyoN4kiVa0Iv+NNQXlLJSNjEPVM=", "1JjIN1Gt4FrTomEwojtqhu2R6MttsCN8nJahx7oGS5A=", "fx0MCREV0SslUb28FAhTy7vZEkeLHDqnwvnuN3ozbq4=", "ddwLtg4tyxBPlTcb1K70uBiIgA0O/gRLz5UHg54oX00=", "EloVVQOEGZPzp3pK8xquRkr/KfLrjpoByVtdyOQkpoc=", "EMxwpcoSsKSsgep4mE67zkJ75EssmfqxZrsKlGI4Ahk=", "i0C8tG8HNUzJ1gWA9URHc/wEdzei+nucXNYZCt1WrDQ=", "o9kFqCf7DB0BdHLOmXVq7OuNEdVWXikCLZEc5RYw8IE=", "F9LJEYVkTFyfBpBCgeoUz2zXECryktCMr7NnR3L2jBs=", "chOousCLyLxrT7GbPM+el3zq520aDHqPKWRpQKkp3H0=", "ehe6HTR9qLvbyeMq6yBsi1po2KgkhDvLCWwE/KZbisA=", "+sQA04p2RJMUj/33kLUT44d7LKfBp24CzMkNbtYxRwU=", "oJjQoLPqvbogsKU2hoLcmvr0ykkbSseUIoF1TZ3zhns=", "B1vr2ozRCkwnZRxQuB51EUl1vI5Z86yAi0/j68DGezM=", "8/++6PgJLZBlM9FH3zIDKHVyNZKIGaLdQIvMLo6rGpY=", "suU56kwbZfFjYmmlRcUCSsYg8vm4VVt39GIFZ8RdI3w=", "ab5pVOGaxPIRwDheDCkXgf9fBczTRWQeZdC0+mLbsEA=", "Sy3C3aVZc1ByPq39q+7G31OP2OlLN2nENu7lOee8SOg=", "GeagCKKesjCIZLg79mchmx+e2vSVRNPVsS9hQtf6iLk=", "VYPTu6DSFgWF7uDMSLW48al8aebNAc+cmyZ2osrhQK0=", "YOv43x6W1pUvod5F/00H1h9/5RKWMOfWPLrQZyTmypc=", "potNpqaBR35dCezmaoIJlhKYyQOHDxlgcUx70VWr+5s=", "BENbDSt8A9Rr5B9y4Bj9qtG4YQYvCvQkqLtUw5G6QWg=", "m27d6w5DHjBAJZ/Mpb5uenyx98IZJJ/9cJ5vMiCNguU=", "4bVyb2uAFDmWuASi2sXzNvdOzMzkZuYBypD500XbMqU=", "X25wiPiWxYDasx1jvvQFgccixTUUW57Thi+Y6rntxCg=", "/crdcb4qldm3mSZDgGkaZHxBluk6HZzP/Z+uK7wr+jg=", "4uIzQmCuGO7TYLTsAKN7GGQmCSbHYihGRPzc3bl1iAs=", "RA/SIcJ8HYH6MHNK78EmtHQr+LpK+MaM1gmUs+0L4D4=", "rh7N5Gzo6GMPNhGyW/Qy0DhltSBvDyg5RIo4mb/UyNI=", "flmDTT8oEIsZDrYLhFgdlQHsdeLdsDvuwn3NZz26Xn8=", "+OkjGYexhp2BLytr/Cq5xEpEgCbMhggWz2xdTgZ/qYk=", "Kj3E/fiN5D90djhb01wLvtB7jDFkoE6eP6gK/V6LWjw=", "jQ+SSK4mITb5Z7ZsWoWsJDRyNCq5X9+I5XpIoLQUMgQ=", "C5bcXXqyr9txqyxPFo9gFvEKeN5z0FI7OaNpnfJIUjE=", "tL5PJvdP2VQnVaoCy8VP4FZQDjZGdaCFzIzz3sZYSCg=", "cWZ2NLBBAZTGDvum7DW4GnbwEfqIDcE0cDkQ72tZvzU=", "y7ewBkQjf1rQWSCq9oL78q8Q274Je8F948wg2/AZDMw=", "p5oC+g3M0Bb8uc6B4dIBWHJJvVaC3E5EjCysWleOiIs=", "DG8E85AwCHWxI+OWWSoeut/9Idvhhx4TyLXHPXEC5dc=", "i1MEGwaGo/QGJFcuT/0i8ZoUNp8OXZ/2O7HcaWUjb8M=", "Wc0gNH2+XKR/k6xeRSlMEm7BRVJ6MtyEq7dK80inEhY=", "RFJQWwd3pUbBmWA2dgNs6xdo7P1mb4LMa4/eKzpdmqA=", "af0I6Eu4M2qRcUVxVagq7fd8QcRA+ut5JyxSV1GvAN8=", "aLoM93+FlaRP2l4e7W/0kS9WhhOKw+LPphtBoqOqeQ8=", "2Y34Qs+877u67qjNJFFfM7shdpotSM1bl6ce7TgSDHg="], "CachedAssets": {"scVjqjm24wQ1vxh8HHGFMFIB8GTF7p7f1+n18ENHOuM=": {"Identity": "D:\\Baytara\\Baytara.Client\\wwwroot\\css\\app.css", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "css/app#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0z165pgkt1", "Integrity": "fHi9D9R4zzkezSlL71hh9pScE0I8Ty5Bv9PhdX/51S0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\app.css", "FileLength": 4391, "LastWriteTime": "2025-06-23T17:54:11.2383025+00:00"}, "ns7vM7Wi+m/0LyMftyamlUwyP03nB/rTpkQpXeNOnFo=": {"Identity": "D:\\Baytara\\Baytara.Client\\wwwroot\\favicon.png", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "favicon#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ifv42okdf2", "Integrity": "4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.png", "FileLength": 1148, "LastWriteTime": "2025-06-23T03:57:54.897649+00:00"}, "sH9JNMKbpZF7MCdrNMOQmWgNpr9ZE4hIsp5tRTa7fRo=": {"Identity": "D:\\Baytara\\Baytara.Client\\wwwroot\\icon-192.png", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "icon-192#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "f9uvjujlxy", "Integrity": "DbpQaq68ZSb5IoPosBErM1QWBfsbTxpJqhU0REi6wP4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\icon-192.png", "FileLength": 2626, "LastWriteTime": "2025-06-23T03:57:54.8985713+00:00"}, "hq3ztsa4CJissj0DDeOdPl15P9ydqE1gxy/y+5fG5nc=": {"Identity": "D:\\Baytara\\Baytara.Client\\wwwroot\\images\\logo.svg", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "images/logo#[.{fingerprint}]?.svg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "wxv5hxfkk4", "Integrity": "DAqGQeslApqg4wSpn7rmnd641H51tpwBO6Jxj+zQJPU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\logo.svg", "FileLength": 1361, "LastWriteTime": "2025-06-23T04:22:34.4246299+00:00"}, "o4BCfy9AzQnnipOSRC5cdHFYhacJ9ii9u+GlUkyAyhs=": {"Identity": "D:\\Baytara\\Baytara.Client\\wwwroot\\index.html", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "index#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "46c9877kjo", "Integrity": "3JlH2Uv5mXAgplw7oEYjDbsNlJ0WctAzyplqF9Sv2VU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\index.html", "FileLength": 1742, "LastWriteTime": "2025-06-23T04:22:04.4320488+00:00"}, "XT0r8+PAKK/q+87AhXFy9zBIMgMOSHP6dqwnNXE/TP0=": {"Identity": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "bqjiyaj88i", "Integrity": "Yy5/hBqRmmU2MJ1TKwP2aXoTO6+OjzrLmJIsC2Wy4H8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 70329, "LastWriteTime": "2025-06-23T03:57:54.9103933+00:00"}, "6DjkhZvUxcEtJyoiovHQwu4ygnhvSlSY9TWjKB+fTt4=": {"Identity": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2j<PERSON><PERSON><PERSON><PERSON>", "Integrity": "xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 203221, "LastWriteTime": "2025-06-23T03:57:54.9136287+00:00"}, "4PSVM+g5YEwAFQUFRlTgjRdpGLJOBP1WUz6ZG3SEK00=": {"Identity": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "erw9l3u2r3", "Integrity": "5nDHMGiyfZHl3UXePuhLDQR9ncPfBR1HJeZLXyJNV24=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 51795, "LastWriteTime": "2025-06-23T03:57:54.9136287+00:00"}, "lk2f1HFLVyPt1NzVmsdLEsOQoWgmqpP+2D0lfK8/ZLU=": {"Identity": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "aexeepp0ev", "Integrity": "kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 115986, "LastWriteTime": "2025-06-23T03:57:54.9292954+00:00"}, "vm2uSZ3RqeVy+HT9UoHFSPVKX0zTrKDmp4kRjpPrBHs=": {"Identity": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "d7shbmvgxk", "Integrity": "CZxoF8zjaLlyVkcvVCDlc8CeQR1w1RMrvgYx30cs8kM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 70403, "LastWriteTime": "2025-06-23T03:57:54.9292954+00:00"}, "me4s8KIv8hkTtjJ+p7vA28vmciQnCHrB3BOVN+dSooU=": {"Identity": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ausgxo2sd3", "Integrity": "/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 203225, "LastWriteTime": "2025-06-23T03:57:54.9475048+00:00"}, "VzGDZlAroR98Sp+MHQCxB7I65LtNpQRa5821TjwXtCU=": {"Identity": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "k8d9w2qqmf", "Integrity": "vMxTcvkC4Ly7LiAT3G8yEy9EpTr7Fge4SczWp07/p3k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 51870, "LastWriteTime": "2025-06-23T03:57:54.9495101+00:00"}, "O5EVGb49lIc/jIZFc5KmjgRlZFJLKXKXsAd48IhC4OQ=": {"Identity": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "cosvhxvwiu", "Integrity": "7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 116063, "LastWriteTime": "2025-06-23T03:57:54.9515157+00:00"}, "FIo94CFsHyT0G2PxjcWsfjhR2ZDr/6Knvn2J6nKMGMo=": {"Identity": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ub07r2b239", "Integrity": "lo9YI82OF03vojdu+XOR3+DRrLIpMhpzZNmHbM5CDMA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 12065, "LastWriteTime": "2025-06-23T03:57:54.9535211+00:00"}, "jN0HwsmcbncWy2/fF1EEHEaCvBTzqd2ofVcDfjAYVC4=": {"Identity": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fvhpjtyr6v", "Integrity": "RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 129371, "LastWriteTime": "2025-06-23T03:57:54.9612227+00:00"}, "fEe85kK77XT0rn95pto/X8UxYwJqxoHYtXs1ArOeBC0=": {"Identity": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b7pk76d08c", "Integrity": "l8vt5oozv958eMd9TFsPAWgl9JJK9YKfbVSs8mchQ84=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 10126, "LastWriteTime": "2025-06-23T03:57:54.9612227+00:00"}, "v/W8V63U7VnwEkPT3zo8rb3+ehYf8vPztCl6wun104A=": {"Identity": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fsbi9cje9m", "Integrity": "0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 51369, "LastWriteTime": "2025-06-23T03:57:54.9672037+00:00"}, "iyVsykyjfw0H/FN2xWVAuutxICIObk5nP6w2yCHRN4I=": {"Identity": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "rzd6atqjts", "Integrity": "V8psnHoJS/MPlCXWwc/J3tGtp9c3gGFRmqsIQgpn+Gg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 12058, "LastWriteTime": "2025-06-23T03:57:54.9717585+00:00"}, "SRa43QjZpD+uJ/mKQyoN4kiVa0Iv+NNQXlLJSNjEPVM=": {"Identity": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ee0r1s7dh0", "Integrity": "OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 129386, "LastWriteTime": "2025-06-23T03:57:54.9772762+00:00"}, "1JjIN1Gt4FrTomEwojtqhu2R6MttsCN8nJahx7oGS5A=": {"Identity": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "dxx9fxp4il", "Integrity": "/8jh8hcEMFKyS6goWqnNu7t3EzZPCGdQZgO6sCkI8tI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 10198, "LastWriteTime": "2025-06-23T03:57:54.9797826+00:00"}, "fx0MCREV0SslUb28FAhTy7vZEkeLHDqnwvnuN3ozbq4=": {"Identity": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jd9uben2k1", "Integrity": "910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 63943, "LastWriteTime": "2025-06-23T03:57:54.9934611+00:00"}, "ddwLtg4tyxBPlTcb1K70uBiIgA0O/gRLz5UHg54oX00=": {"Identity": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "khv3u5hwcm", "Integrity": "2BubgNUPlQSF/0wLFcRXQ/Yjzk9vsUbDAeK2QM+h+yo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 107823, "LastWriteTime": "2025-06-23T03:57:54.9934611+00:00"}, "EloVVQOEGZPzp3pK8xquRkr/KfLrjpoByVtdyOQkpoc=": {"Identity": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "r4e9w2rdcm", "Integrity": "Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 267535, "LastWriteTime": "2025-06-23T03:57:55.0027335+00:00"}, "EMxwpcoSsKSsgep4mE67zkJ75EssmfqxZrsKlGI4Ahk=": {"Identity": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "lcd1t2u6c8", "Integrity": "KyE9xbKO9CuYx0HXpIKgsWIvXkAfITtiQ172j26wmRs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 85352, "LastWriteTime": "2025-06-23T03:57:55.0113379+00:00"}, "i0C8tG8HNUzJ1gWA9URHc/wEdzei+nucXNYZCt1WrDQ=": {"Identity": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2oey78nd0", "Integrity": "rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 180381, "LastWriteTime": "2025-06-23T03:57:54.5467509+00:00"}, "o9kFqCf7DB0BdHLOmXVq7OuNEdVWXikCLZEc5RYw8IE=": {"Identity": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "tdbxkamptv", "Integrity": "H6wkBbSwjua2veJoThJo4uy161jp+DOiZTloUlcZ6qQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 107691, "LastWriteTime": "2025-06-23T03:57:54.5550819+00:00"}, "F9LJEYVkTFyfBpBCgeoUz2zXECryktCMr7NnR3L2jBs=": {"Identity": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "j5mq2jizvt", "Integrity": "p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 267476, "LastWriteTime": "2025-06-23T03:57:54.5550819+00:00"}, "chOousCLyLxrT7GbPM+el3zq520aDHqPKWRpQKkp3H0=": {"Identity": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "06098lyss8", "Integrity": "GAUum6FjwQ8HrXGaoFRnHTqQQLpljXGavT7mBX8E9qU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 85281, "LastWriteTime": "2025-06-23T03:57:54.5550819+00:00"}, "ehe6HTR9qLvbyeMq6yBsi1po2KgkhDvLCWwE/KZbisA=": {"Identity": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "nvvlpmu67g", "Integrity": "o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 180217, "LastWriteTime": "2025-06-23T03:57:54.5717726+00:00"}, "+sQA04p2RJMUj/33kLUT44d7LKfBp24CzMkNbtYxRwU=": {"Identity": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "s35ty4nyc5", "Integrity": "GKEF18s44B5e0MolXAkpkqLiEbOVlKf6VyYr/G/E6pw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 281046, "LastWriteTime": "2025-06-23T03:57:54.5863535+00:00"}, "oJjQoLPqvbogsKU2hoLcmvr0ykkbSseUIoF1TZ3zhns=": {"Identity": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pj5nd1wqec", "Integrity": "KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 679755, "LastWriteTime": "2025-06-23T03:57:54.6202265+00:00"}, "B1vr2ozRCkwnZRxQuB51EUl1vI5Z86yAi0/j68DGezM=": {"Identity": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "46ein0sx1k", "Integrity": "PI8n5gCcz9cQqQXm3PEtDuPG8qx9oFsFctPg0S5zb8g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 232803, "LastWriteTime": "2025-06-23T03:57:54.6202265+00:00"}, "8/++6PgJLZBlM9FH3zIDKHVyNZKIGaLdQIvMLo6rGpY=": {"Identity": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "v0zj4ognzu", "Integrity": "8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 589892, "LastWriteTime": "2025-06-23T03:57:54.6345268+00:00"}, "suU56kwbZfFjYmmlRcUCSsYg8vm4VVt39GIFZ8RdI3w=": {"Identity": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "37tfw0ft22", "Integrity": "j5E4XIj1p1kNnDi0x1teX9RXoh1/FNlPvCML9YmRh2Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 280259, "LastWriteTime": "2025-06-23T03:57:54.6502555+00:00"}, "ab5pVOGaxPIRwDheDCkXgf9fBczTRWQeZdC0+mLbsEA=": {"Identity": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "hrwsygsryq", "Integrity": "3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 679615, "LastWriteTime": "2025-06-23T03:57:54.6663234+00:00"}, "Sy3C3aVZc1ByPq39q+7G31OP2OlLN2nENu7lOee8SOg=": {"Identity": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pk9g2wxc8p", "Integrity": "h5lE7Nm8SkeIpBHHYxN99spP3VuGFKl5NZgsocil7zk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 232911, "LastWriteTime": "2025-06-23T03:57:54.7003584+00:00"}, "GeagCKKesjCIZLg79mchmx+e2vSVRNPVsS9hQtf6iLk=": {"Identity": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ft3s53vfgj", "Integrity": "rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 589087, "LastWriteTime": "2025-06-23T03:57:54.7143625+00:00"}, "VYPTu6DSFgWF7uDMSLW48al8aebNAc+cmyZ2osrhQK0=": {"Identity": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "iww9xi2hp3", "Integrity": "0rd597LggEczRK6N/P9yE09x5p2+2WzX7qECQIDefPY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 207819, "LastWriteTime": "2025-06-23T03:57:54.7302338+00:00"}, "YOv43x6W1pUvod5F/00H1h9/5RKWMOfWPLrQZyTmypc=": {"Identity": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4rfh5s50bz", "Integrity": "ADEXOfEUSkfFzqvEXJXbsbreZ3xy0ekmkkGLa/sOi6E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 444579, "LastWriteTime": "2025-06-23T03:57:54.7463017+00:00"}, "potNpqaBR35dCezmaoIJlhKYyQOHDxlgcUx70VWr+5s=": {"Identity": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "493y06b0oq", "Integrity": "CDOy6cOibCWEdsRiZuaHf8dSGGJRYuBGC+mjoJimHGw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 80721, "LastWriteTime": "2025-06-23T03:57:54.7463017+00:00"}, "BENbDSt8A9Rr5B9y4Bj9qtG4YQYvCvQkqLtUw5G6QWg=": {"Identity": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "kqgha8t6s6", "Integrity": "BNd/F4Qb9/XaW+dQmPRe0fLDby7OTtEAscQDM8NEYk4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 332090, "LastWriteTime": "2025-06-23T03:57:54.7463017+00:00"}, "m27d6w5DHjBAJZ/Mpb5uenyx98IZJJ/9cJ5vMiCNguU=": {"Identity": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pg7rvgc6ra", "Integrity": "d6L6JpoIfBLG8Xv2vmBJa2NEU16Gso67W/SqMDF+Kys=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 135829, "LastWriteTime": "2025-06-23T03:57:54.7620055+00:00"}, "4bVyb2uAFDmWuASi2sXzNvdOzMzkZuYBypD500XbMqU=": {"Identity": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "7g9sx6x8f9", "Integrity": "18dnhM3mfIc7+jiqyWcWiOlM484c3PScI/MOUkSFia8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 305438, "LastWriteTime": "2025-06-23T03:57:54.7620055+00:00"}, "X25wiPiWxYDasx1jvvQFgccixTUUW57Thi+Y6rntxCg=": {"Identity": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jj8uyg4cgr", "Integrity": "QZdFT1ZNdly4rmgUBtXmXFS9BU1FTa+sPe6h794sFRQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 73935, "LastWriteTime": "2025-06-23T03:57:54.7779794+00:00"}, "/crdcb4qldm3mSZDgGkaZHxBluk6HZzP/Z+uK7wr+jg=": {"Identity": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "yxdfmmpmcr", "Integrity": "qg29M9L0ub+gVWVoPryMeby6EdeAOakemc3clA+Ton4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 222455, "LastWriteTime": "2025-06-23T03:57:54.7900034+00:00"}, "4uIzQmCuGO7TYLTsAKN7GGQmCSbHYihGRPzc3bl1iAs=": {"Identity": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "43bjlk9u9j", "Integrity": "4NMEqcb27voAXjqqR268iGtj8ZHYBCI7yeHrJO6lDWE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 145401, "LastWriteTime": "2025-06-23T03:57:54.795757+00:00"}, "RA/SIcJ8HYH6MHNK78EmtHQr+LpK+MaM1gmUs+0L4D4=": {"Identity": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "xl1csbwgsv", "Integrity": "JTtZaKPrZS15ryrBGdSkDG5Xh/u1j9w4Mt8zPv/de8I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 306606, "LastWriteTime": "2025-06-23T03:57:54.8099254+00:00"}, "rh7N5Gzo6GMPNhGyW/Qy0DhltSBvDyg5RIo4mb/UyNI=": {"Identity": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "63fj8s7r0e", "Integrity": "3gQJhtmj7YnV1fmtbVcnAV6eI4ws0Tr48bVZCThtCGQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 60635, "LastWriteTime": "2025-06-23T03:57:54.8099254+00:00"}, "flmDTT8oEIsZDrYLhFgdlQHsdeLdsDvuwn3NZz26Xn8=": {"Identity": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "hvy20onkgh", "Integrity": "De0sF9esziQNdfl0pAVTCpHrulCYvCjhgupkFEMbDCc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 220561, "LastWriteTime": "2025-06-23T03:57:54.8256116+00:00"}, "+OkjGYexhp2BLytr/Cq5xEpEgCbMhggWz2xdTgZ/qYk=": {"Identity": "D:\\Baytara\\Baytara.Client\\wwwroot\\sample-data\\weather.json", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "sample-data/weather#[.{fingerprint}]?.json", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "iag0ou56lh", "Integrity": "enKgCMkYmCpfEcmg6Annbmc40VZ/A6aYYSQjZfVn2cU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\sample-data\\weather.json", "FileLength": 453, "LastWriteTime": "2025-06-23T03:57:54.8416494+00:00"}}, "CachedCopyCandidates": {}}