﻿<div class="top-row ps-3 navbar navbar-dark">
    <div class="container-fluid">
        <a class="navbar-brand" href="">بيطره</a>
        <button title="Navigation menu" class="navbar-toggler" @onclick="ToggleNavMenu">
            <span class="navbar-toggler-icon"></span>
        </button>
    </div>
</div>

<div class="@NavMenuCssClass nav-scrollable" @onclick="ToggleNavMenu">
    <nav class="nav flex-column">
        <div class="nav-item px-3">
            <NavLink class="nav-link" href="" Match="NavLinkMatch.All">
                <span class="bi bi-house-door-fill-nav-menu" aria-hidden="true"></span> الرئيسية
            </NavLink>
        </div>
        <div class="nav-item px-3">
            <NavLink class="nav-link" href="breeders">
                <span class="bi bi-people-fill" aria-hidden="true"></span> المربين
            </NavLink>
        </div>
        <div class="nav-item px-3">
            <NavLink class="nav-link" href="medicines">
                <span class="bi bi-capsule" aria-hidden="true"></span> الأدوية
            </NavLink>
        </div>
        <div class="nav-item px-3">
            <NavLink class="nav-link" href="treatments">
                <span class="bi bi-heart-pulse" aria-hidden="true"></span> العلاجات
            </NavLink>
        </div>
        <div class="nav-item px-3">
            <NavLink class="nav-link" href="reports">
                <span class="bi bi-graph-up" aria-hidden="true"></span> التقارير
            </NavLink>
        </div>
    </nav>
</div>

@code {
    private bool collapseNavMenu = true;

    private string? NavMenuCssClass => collapseNavMenu ? "collapse" : null;

    private void ToggleNavMenu()
    {
        collapseNavMenu = !collapseNavMenu;
    }
}
