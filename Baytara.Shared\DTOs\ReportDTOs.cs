namespace Baytara.Shared.DTOs
{
    public class TreatmentsByDateDto
    {
        public DateTime Date { get; set; }
        public int Count { get; set; }
        public decimal Revenue { get; set; }
    }

    public class TreatmentsByAnimalTypeDto
    {
        public string AnimalTypeName { get; set; } = string.Empty;
        public int Count { get; set; }
        public decimal Revenue { get; set; }
    }

    public class BreederStatisticsDto
    {
        public string BreederName { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public int AnimalsCount { get; set; }
        public int TreatmentsCount { get; set; }
        public decimal TotalSpent { get; set; }
        public DateTime LastVisit { get; set; }
        public decimal AverageSpentPerTreatment { get; set; }
        public string MostTreatedAnimalType { get; set; } = string.Empty;
    }

    public class RecentTreatmentDto
    {
        public int Id { get; set; }
        public string AnimalName { get; set; } = string.Empty;
        public string AnimalTypeName { get; set; } = string.Empty;
        public string BreederName { get; set; } = string.Empty;
        public string DoctorName { get; set; } = string.Empty;
        public decimal TotalCost { get; set; }
        public DateTime TreatmentDate { get; set; }
        public string Status { get; set; } = string.Empty;
    }

    public class DashboardDto
    {
        public decimal TotalRevenue { get; set; }
        public int TotalTreatments { get; set; }
        public int TotalAnimals { get; set; }
        public int TotalBreeders { get; set; }
        public int TotalMedicines { get; set; }
        public int PendingTreatments { get; set; }
        public int CompletedTreatments { get; set; }
        public int TotalLabTests { get; set; }
        public int PendingLabTests { get; set; }
        public int LowStockMedicines { get; set; }
        public int ExpiringMedicines { get; set; }
        public decimal MonthlyRevenue { get; set; }
        public List<TreatmentsByDateDto> TreatmentsByDate { get; set; } = new();
        public List<TreatmentsByAnimalTypeDto> TreatmentsByAnimalType { get; set; } = new();
        public List<RecentTreatmentDto> RecentTreatments { get; set; } = new();
        public List<MedicineAlertDto> MedicineAlerts { get; set; } = new();
    }

    public class MedicineAlertDto
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public int Quantity { get; set; }
        public int MinimumStock { get; set; }
        public DateTime? ExpiryDate { get; set; }
        public string AlertType { get; set; } = string.Empty; // LowStock, Expiring, Expired
        public string BranchName { get; set; } = string.Empty;
    }

    public class MedicineUsageDto
    {
        public int MedicineId { get; set; }
        public string MedicineName { get; set; } = string.Empty;
        public int TotalUsed { get; set; }
        public decimal TotalValue { get; set; }
        public int TreatmentsCount { get; set; }
        public decimal AveragePerTreatment { get; set; }
        public DateTime LastUsed { get; set; }
    }

    public class FinancialReportDto
    {
        public decimal TotalRevenue { get; set; }
        public decimal TotalCosts { get; set; }
        public decimal NetProfit { get; set; }
        public decimal AverageTreatmentCost { get; set; }
        public decimal AverageDailyRevenue { get; set; }
        public int TotalTreatments { get; set; }
        public int TotalLabTests { get; set; }
        public decimal LabTestsRevenue { get; set; }
        public decimal TreatmentsRevenue { get; set; }
        public List<MonthlyRevenueDto> MonthlyRevenue { get; set; } = new();
    }

    public class MonthlyRevenueDto
    {
        public int Year { get; set; }
        public int Month { get; set; }
        public string MonthName { get; set; } = string.Empty;
        public decimal Revenue { get; set; }
        public int TreatmentsCount { get; set; }
        public int LabTestsCount { get; set; }
    }

    public class AnimalTypeStatisticsDto
    {
        public int AnimalTypeId { get; set; }
        public string AnimalTypeName { get; set; } = string.Empty;
        public int AnimalsCount { get; set; }
        public int TreatmentsCount { get; set; }
        public decimal TotalRevenue { get; set; }
        public decimal AverageTreatmentCost { get; set; }
        public string MostCommonDisease { get; set; } = string.Empty;
        public int LabTestsCount { get; set; }
    }

    public class BranchPerformanceDto
    {
        public int BranchId { get; set; }
        public string BranchName { get; set; } = string.Empty;
        public decimal TotalRevenue { get; set; }
        public int TreatmentsCount { get; set; }
        public int LabTestsCount { get; set; }
        public int ActiveBreeders { get; set; }
        public int TotalAnimals { get; set; }
        public decimal AverageRevenuePerTreatment { get; set; }
        public int StaffCount { get; set; }
    }

    public class DetailedReportRequestDto
    {
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public int? BranchId { get; set; }
        public int? AnimalTypeId { get; set; }
        public int? BreederId { get; set; }
        public string ReportType { get; set; } = string.Empty; // "treatments", "labtests", "financial", "medicines"
        public bool IncludeCharts { get; set; } = true;
        public string ExportFormat { get; set; } = "json"; // "json", "csv", "pdf"
    }
}
