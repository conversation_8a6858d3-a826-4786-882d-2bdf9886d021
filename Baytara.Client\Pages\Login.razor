@page "/login"
@using Baytara.Shared.DTOs
@using Baytara.Client.Services
@inject IAuthService AuthService
@inject NavigationManager Navigation
@inject IJSRuntime JSRuntime

<div class="login-container">
    <div class="login-background">
        <div class="background-pattern"></div>

    </div>

    <div class="login-card">
        <div class="login-header">
            <div class="logo-section">
                <div class="logo-container">
                    <i class="fas fa-paw logo-icon"></i>
                </div>
                <h1 class="app-title">🐾 بيطرة</h1>
                <p class="app-subtitle">نظام إدارة العيادات البيطرية الشامل</p>
            </div>
        </div>

        <div class="welcome-message">
            <h2>مرحباً بك</h2>
            <p>يرجى تسجيل الدخول للوصول إلى النظام</p>
        </div>

        <div class="login-form">
            <EditForm Model="@loginModel" OnValidSubmit="@HandleLogin">
                <DataAnnotationsValidator />

                @if (!string.IsNullOrEmpty(errorMessage))
                {
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i>
                        <span>@errorMessage</span>
                    </div>
                }

                <div class="form-group">
                    <label for="username">
                        <i class="fas fa-user"></i>
                        اسم المستخدم
                    </label>
                    <div class="input-wrapper">
                        <InputText id="username" @bind-Value="loginModel.Username" class="form-control" placeholder="admin" autocomplete="username" />
                        <div class="input-icon">
                            <i class="fas fa-user"></i>
                        </div>
                    </div>
                    <ValidationMessage For="@(() => loginModel.Username)" class="validation-message" />
                </div>

                <div class="form-group">
                    <label for="password">
                        <i class="fas fa-lock"></i>
                        كلمة المرور
                    </label>
                    <div class="input-wrapper">
                        <InputText id="password" @bind-Value="loginModel.Password" type="password" class="form-control" placeholder="admin123" autocomplete="current-password" />
                        <div class="input-icon">
                            <i class="fas fa-lock"></i>
                        </div>
                    </div>
                    <ValidationMessage For="@(() => loginModel.Password)" class="validation-message" />
                </div>

                <div class="demo-credentials">
                    <div class="demo-info">
                        <i class="fas fa-info-circle"></i>
                        <span>بيانات تجريبية:</span>
                    </div>
                    <div class="demo-details">
                        <div class="demo-item">
                            <strong>المستخدم:</strong> admin
                        </div>
                        <div class="demo-item">
                            <strong>كلمة المرور:</strong> admin123
                        </div>
                    </div>
                </div>

                <button type="submit" class="btn btn-login" disabled="@isLoading">
                    @if (isLoading)
                    {
                        <div class="loading-content">
                            <i class="fas fa-spinner fa-spin"></i>
                            <span>جاري تسجيل الدخول...</span>
                        </div>
                    }
                    else
                    {
                        <div class="login-content">
                            <i class="fas fa-sign-in-alt"></i>
                            <span>تسجيل الدخول</span>
                        </div>
                    }
                </button>
            </EditForm>
        </div>

        <div class="login-footer">
            <div class="footer-content">
                <p>&copy; 2024 نظام بيطرة - جميع الحقوق محفوظة</p>
                <div class="footer-links">
                    <span>نسخة 1.0</span>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .login-container {
        min-height: 100vh;
        width: 100vw;
        display: flex;
        align-items: center;
        justify-content: center;
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        overflow: hidden;
        direction: ltr;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        padding: 0;
        margin: 0;
        box-sizing: border-box;
    }

    .login-background {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        z-index: -1;
    }

    .background-pattern {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-image:
            radial-gradient(circle at 25% 25%, rgba(0,123,255,0.05) 2px, transparent 2px),
            radial-gradient(circle at 75% 75%, rgba(0,123,255,0.03) 1px, transparent 1px);
        background-size: 50px 50px, 30px 30px;
    }





    .login-card {
        background: white;
        border-radius: 15px;
        border: 2px solid #dee2e6;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        padding: 40px;
        width: 420px;
        height: auto;
        margin: 0;
        position: relative;
        color: #000;
        text-align: center;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
    }

    .login-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #667eea, #764ba2, #f093fb);
        border-radius: 25px 25px 0 0;
    }

    .login-header {
        text-align: center;
        margin-bottom: 35px;
    }

    .logo-section {
        margin-bottom: 25px;
    }

    .logo-container {
        width: 80px;
        height: 80px;
        margin: 0 auto 20px;
        background: linear-gradient(135deg, #007bff, #0056b3);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
        position: relative;
        overflow: hidden;
    }



    .logo-icon {
        font-size: 2.5rem;
        color: white;
        z-index: 1;
    }

    .app-title {
        font-size: 2.5rem;
        font-weight: 700;
        color: #000;
        margin-bottom: 10px;
        letter-spacing: -1px;
    }

    .app-subtitle {
        font-size: 1.1rem;
        color: #333;
        margin-bottom: 0;
        font-weight: 500;
    }

    .welcome-message {
        text-align: center;
        margin-bottom: 30px;
        padding: 20px;
        background: #f8f9fa;
        border-radius: 12px;
        border: 1px solid #dee2e6;
    }

    .welcome-message h2 {
        font-size: 1.6rem;
        color: #000;
        margin-bottom: 8px;
        font-weight: 600;
    }

    .welcome-message p {
        color: #333;
        margin-bottom: 0;
        font-size: 1rem;
        margin-bottom: 0;
        font-size: 1rem;
    }

    .login-form {
        margin-bottom: 25px;
    }

    .form-group {
        margin-bottom: 25px;
    }

    .form-group label {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        gap: 8px;
        margin-bottom: 10px;
        color: #000;
        font-weight: 600;
        font-size: 1rem;
        text-align: left;
    }

    .form-group label i {
        color: #007bff;
        font-size: 0.9rem;
    }

    .input-wrapper {
        position: relative;
        display: flex;
        align-items: center;
    }

    .form-control {
        width: 100%;
        padding: 15px 20px 15px 50px;
        border: 2px solid #dee2e6;
        border-radius: 8px;
        background: white;
        color: #000;
        font-size: 1rem;
        transition: all 0.3s ease;
        direction: ltr;
        text-align: left;
    }

    .form-control::placeholder {
        color: #6c757d;
        font-style: normal;
    }

    .form-control:focus {
        outline: none;
        border-color: #007bff;
        background: white;
        box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
        transform: translateY(-1px);
    }

    .input-icon {
        position: absolute;
        left: 15px;
        color: #6c757d;
        font-size: 1.1rem;
        transition: color 0.3s ease;
        pointer-events: none;
    }

    .form-control:focus + .input-icon {
        color: #007bff;
    }

    .demo-credentials {
        background: #e7f3ff;
        border: 1px solid #b3d9ff;
        border-radius: 10px;
        padding: 15px;
        margin-bottom: 25px;
        text-align: center;
    }

    .demo-info {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        margin-bottom: 10px;
        color: #0066cc;
        font-weight: 600;
        font-size: 0.95rem;
    }

    .demo-details {
        display: flex;
        justify-content: space-around;
        gap: 20px;
    }

    .demo-item {
        font-size: 0.9rem;
        color: #000;
    }

    .demo-item strong {
        color: #000;
        font-weight: 700;
    }

    .btn-login {
        width: 100%;
        padding: 15px 25px;
        background: #007bff;
        border: none;
        border-radius: 8px;
        color: white;
        font-size: 1.1rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
    }

    .btn-login:hover:not(:disabled) {
        background: #0056b3;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 123, 255, 0.4);
    }

    .btn-login:active:not(:disabled) {
        transform: translateY(-1px);
    }

    .btn-login:disabled {
        opacity: 0.7;
        cursor: not-allowed;
        transform: none;
    }

    .login-content,
    .loading-content {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 10px;
        position: relative;
        z-index: 1;
    }

    .alert {
        padding: 15px 20px;
        border-radius: 12px;
        margin-bottom: 25px;
        display: flex;
        align-items: center;
        gap: 12px;
        animation: slideIn 0.3s ease-out;
    }

    @@keyframes slideIn {
        from {
            opacity: 0;
            transform: translateY(-10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .alert-danger {
        background: linear-gradient(135deg, rgba(220, 53, 69, 0.1), rgba(255, 107, 122, 0.1));
        border: 1px solid rgba(220, 53, 69, 0.3);
        color: #dc3545;
    }

    .alert-danger i {
        color: #dc3545;
        font-size: 1.1rem;
    }

    .login-footer {
        text-align: center;
        margin-top: 30px;
        padding-top: 25px;
        border-top: 1px solid #e9ecef;
    }

    .footer-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 10px;
    }

    .footer-content p {
        color: #333;
        margin: 0;
        font-size: 0.9rem;
        font-weight: 500;
    }

    .footer-links {
        display: flex;
        align-items: center;
        gap: 15px;
        color: #666;
        font-size: 0.85rem;
    }

    .validation-message {
        color: #dc3545;
        font-size: 0.85rem;
        margin-top: 8px;
        display: block;
        text-align: right;
        font-weight: 500;
        animation: shake 0.3s ease-in-out;
    }

    @@keyframes shake {
        0%, 100% { transform: translateX(0); }
        25% { transform: translateX(-5px); }
        75% { transform: translateX(5px); }
    }

    /* Responsive Design */
    @@media (max-width: 768px) {
        .login-card {
            margin: 15px;
            padding: 40px 25px;
            max-width: 95%;
        }

        .logo-container {
            width: 70px;
            height: 70px;
        }

        .logo-icon {
            font-size: 2rem;
        }

        .app-title {
            font-size: 2.2rem;
        }

        .app-subtitle {
            font-size: 1rem;
        }

        .welcome-message {
            padding: 15px;
        }

        .welcome-message h2 {
            font-size: 1.5rem;
        }

        .form-control {
            padding: 12px 45px 12px 15px;
            font-size: 16px; /* Prevents zoom on iOS */
        }

        .btn-login {
            padding: 15px 20px;
            font-size: 1rem;
        }

        .demo-details {
            flex-direction: column;
            gap: 8px;
        }


    }

    @@media (max-width: 480px) {
        .login-card {
            margin: 10px;
            padding: 30px 20px;
        }

        .app-title {
            font-size: 1.8rem;
        }

        .welcome-message h2 {
            font-size: 1.3rem;
        }

        .form-group {
            margin-bottom: 20px;
        }
    }
</style>

@code {
    private LoginDto loginModel = new();
    private string errorMessage = string.Empty;
    private bool isLoading = false;

    protected override async Task OnInitializedAsync()
    {
        // Check if user is already authenticated
        if (await AuthService.IsAuthenticatedAsync())
        {
            Navigation.NavigateTo("/");
        }
    }

    private async Task HandleLogin()
    {
        try
        {
            isLoading = true;
            errorMessage = string.Empty;
            StateHasChanged();

            var result = await AuthService.LoginAsync(loginModel);

            if (result.Success)
            {
                Navigation.NavigateTo("/");
            }
            else
            {
                errorMessage = result.Message;
            }
        }
        catch (Exception ex)
        {
            errorMessage = "حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.";
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }
}
