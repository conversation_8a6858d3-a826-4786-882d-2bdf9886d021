using System.ComponentModel.DataAnnotations;

namespace Baytara.Shared.Models
{
    public class Animal
    {
        public int Id { get; set; }
        
        [StringLength(50)]
        public string? Name { get; set; }
        
        public int AnimalTypeId { get; set; }
        public AnimalType AnimalType { get; set; } = null!;
        
        public int BreederId { get; set; }
        public Breeder Breeder { get; set; } = null!;
        
        public Gender Gender { get; set; }
        
        public int? Age { get; set; }
        
        [StringLength(50)]
        public string? Color { get; set; }
        
        [StringLength(100)]
        public string? Breed { get; set; }
        
        public decimal? Weight { get; set; }
        
        [StringLength(500)]
        public string? Notes { get; set; }
        
        public bool IsActive { get; set; } = true;
        
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime? UpdatedAt { get; set; }
        
        // Navigation properties
        public ICollection<Treatment> Treatments { get; set; } = new List<Treatment>();
        public ICollection<LabTest> LabTests { get; set; } = new List<LabTest>();
    }
    
    public enum Gender
    {
        Male = 1,
        Female = 2
    }
}
