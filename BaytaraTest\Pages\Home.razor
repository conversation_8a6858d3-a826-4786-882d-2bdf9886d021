﻿@page "/"

<PageTitle>الرئيسية - بيطره</PageTitle>

<div class="home-page">
    <div class="welcome-section">
        <h1>مرحباً بك في نظام بيطره</h1>
        <p>نظام شامل لإدارة العيادات البيطرية</p>
    </div>

    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-users"></i>
            </div>
            <div class="stat-content">
                <h3>25</h3>
                <p>إجمالي المربين</p>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-paw"></i>
            </div>
            <div class="stat-content">
                <h3>150</h3>
                <p>إجمالي الحيوانات</p>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-pills"></i>
            </div>
            <div class="stat-content">
                <h3>45</h3>
                <p>إجمالي الأدوية</p>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-stethoscope"></i>
            </div>
            <div class="stat-content">
                <h3>89</h3>
                <p>إجمالي العلاجات</p>
            </div>
        </div>
    </div>
</div>

<style>
    .home-page {
        padding: 20px;
    }

    .welcome-section {
        text-align: center;
        margin-bottom: 40px;
        padding: 40px 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
    }

    .welcome-section h1 {
        font-size: 2.5rem;
        margin-bottom: 10px;
    }

    .welcome-section p {
        font-size: 1.2rem;
        opacity: 0.9;
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
    }

    .stat-card {
        background: white;
        padding: 25px;
        border-radius: 15px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        display: flex;
        align-items: center;
        gap: 20px;
        transition: transform 0.3s ease;
    }

    .stat-card:hover {
        transform: translateY(-5px);
    }

    .stat-icon {
        width: 60px;
        height: 60px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.5rem;
    }

    .stat-content h3 {
        font-size: 2rem;
        font-weight: 700;
        color: #333;
        margin-bottom: 5px;
    }

    .stat-content p {
        color: #666;
        margin-bottom: 0;
    }
</style>
