using Baytara.Shared.Models;
using System.ComponentModel.DataAnnotations;

namespace Baytara.Shared.DTOs
{
    public class TreatmentDto
    {
        public int Id { get; set; }
        public int AnimalId { get; set; }
        public string AnimalName { get; set; } = string.Empty;
        public string AnimalTypeName { get; set; } = string.Empty;
        public int BreederId { get; set; }
        public string BreederName { get; set; } = string.Empty;
        public int DoctorId { get; set; }
        public string DoctorName { get; set; } = string.Empty;
        public int BranchId { get; set; }
        public string BranchName { get; set; } = string.Empty;
        public DateTime TreatmentDate { get; set; }
        public string? Symptoms { get; set; }
        public string? Diagnosis { get; set; }
        public string? TreatmentPlan { get; set; }
        public string? Notes { get; set; }
        public decimal TotalCost { get; set; }
        public TreatmentStatus Status { get; set; }
        public List<TreatmentMedicineDto> Medicines { get; set; } = new();
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }

    public class CreateTreatmentDto
    {
        [Required]
        public int AnimalId { get; set; }
        
        [Required]
        public int BreederId { get; set; }
        
        [Required]
        public int DoctorId { get; set; }
        
        [Required]
        public int BranchId { get; set; }
        
        public string? Symptoms { get; set; }
        public string? Diagnosis { get; set; }
        public string? TreatmentPlan { get; set; }
        public string? Notes { get; set; }
        public decimal TotalCost { get; set; }
        public List<TreatmentMedicineCreateDto> Medicines { get; set; } = new();
    }

    public class UpdateTreatmentDto
    {
        public string? Symptoms { get; set; }
        public string? Diagnosis { get; set; }
        public string? TreatmentPlan { get; set; }
        public string? Notes { get; set; }
        public decimal TotalCost { get; set; }
        public TreatmentStatus Status { get; set; }
        public List<TreatmentMedicineCreateDto> Medicines { get; set; } = new();
    }

    public class TreatmentMedicineDto
    {
        public int Id { get; set; }
        public int MedicineId { get; set; }
        public string MedicineName { get; set; } = string.Empty;
        public int Quantity { get; set; }
        public decimal UnitPrice { get; set; }
        public decimal TotalPrice => Quantity * UnitPrice;
    }

    public class TreatmentMedicineCreateDto
    {
        public int MedicineId { get; set; }
        public int Quantity { get; set; }
        public decimal UnitPrice { get; set; }
        public decimal TotalPrice => Quantity * UnitPrice;
    }
}
