@page "/settings"
@using Baytara.Client.Services
@using Baytara.Shared.DTOs
@using Baytara.Shared.Models
@inject IApiService ApiService
@inject IAuthService AuthService
@inject IJSRuntime JSRuntime

<PageTitle>الإعدادات - بيطره</PageTitle>

<div class="settings-page">
    <div class="page-header">
        <div class="header-content">
            <h1 class="page-title">
                <i class="fas fa-cog"></i>
                الإعدادات
            </h1>
            <p class="page-subtitle">إدارة إعدادات النظام والمستخدمين</p>
        </div>
    </div>

    <div class="settings-container">
        <!-- Settings Navigation -->
        <div class="settings-nav">
            <button class="nav-item @(activeTab == "users" ? "active" : "")" @onclick="@(() => SetActiveTab("users"))">
                <i class="fas fa-users"></i>
                <span>المستخدمين</span>
            </button>
            <button class="nav-item @(activeTab == "branches" ? "active" : "")" @onclick="@(() => SetActiveTab("branches"))">
                <i class="fas fa-building"></i>
                <span>الفروع</span>
            </button>
            <button class="nav-item @(activeTab == "animal-types" ? "active" : "")" @onclick="@(() => SetActiveTab("animal-types"))">
                <i class="fas fa-paw"></i>
                <span>أنواع الحيوانات</span>
            </button>
            <button class="nav-item @(activeTab == "diseases" ? "active" : "")" @onclick="@(() => SetActiveTab("diseases"))">
                <i class="fas fa-virus"></i>
                <span>الأمراض</span>
            </button>
            <button class="nav-item @(activeTab == "system" ? "active" : "")" @onclick="@(() => SetActiveTab("system"))">
                <i class="fas fa-server"></i>
                <span>النظام</span>
            </button>
        </div>

        <!-- Settings Content -->
        <div class="settings-content">
            @if (activeTab == "users")
            {
                <div class="tab-content">
                    <div class="tab-header">
                        <h2>إدارة المستخدمين</h2>
                        <button class="btn btn-primary" @onclick="ShowAddUserModal">
                            <i class="fas fa-plus"></i>
                            إضافة مستخدم
                        </button>
                    </div>
                    
                    @if (isLoading)
                    {
                        <div class="loading">
                            <i class="fas fa-spinner fa-spin"></i>
                            جاري التحميل...
                        </div>
                    }
                    else if (users.Any())
                    {
                        <div class="users-grid">
                            @foreach (var user in users)
                            {
                                <div class="user-card">
                                    <div class="user-avatar">
                                        <i class="fas fa-user"></i>
                                    </div>
                                    <div class="user-info">
                                        <h3>@user.FullName</h3>
                                        <p class="username">@user.Username</p>
                                        <p class="role">@GetRoleText(user.Role)</p>
                                        <p class="branch">@user.BranchName</p>
                                    </div>
                                    <div class="user-actions">
                                        <button class="btn btn-sm btn-warning" @onclick="() => ShowEditUserModal(user)">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-sm btn-danger" @onclick="() => DeleteUser(user.Id)">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                    <div class="user-status @(user.IsActive ? "active" : "inactive")">
                                        @(user.IsActive ? "نشط" : "غير نشط")
                                    </div>
                                </div>
                            }
                        </div>
                    }
                    else
                    {
                        <div class="empty-state">
                            <i class="fas fa-users"></i>
                            <p>لا يوجد مستخدمين</p>
                        </div>
                    }
                </div>
            }
            else if (activeTab == "branches")
            {
                <div class="tab-content">
                    <div class="tab-header">
                        <h2>إدارة الفروع</h2>
                        <button class="btn btn-primary" @onclick="ShowAddBranchModal">
                            <i class="fas fa-plus"></i>
                            إضافة فرع
                        </button>
                    </div>
                    
                    @if (isLoading)
                    {
                        <div class="loading">
                            <i class="fas fa-spinner fa-spin"></i>
                            جاري التحميل...
                        </div>
                    }
                    else if (branches.Any())
                    {
                        <div class="branches-grid">
                            @foreach (var branch in branches)
                            {
                                <div class="branch-card">
                                    <div class="branch-header">
                                        <h3>@branch.Name</h3>
                                        <div class="branch-actions">
                                            <button class="btn btn-sm btn-warning" @onclick="() => ShowEditBranchModal(branch)">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-sm btn-danger" @onclick="() => DeleteBranch(branch.Id)">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="branch-info">
                                        @if (!string.IsNullOrEmpty(branch.Address))
                                        {
                                            <p><i class="fas fa-map-marker-alt"></i> @branch.Address</p>
                                        }

                                        @if (!string.IsNullOrEmpty(branch.Email))
                                        {
                                            <p><i class="fas fa-envelope"></i> @branch.Email</p>
                                        }
                                    </div>
                                    <div class="branch-stats">
                                        <div class="stat">
                                            <span class="value">@branch.UsersCount</span>
                                            <span class="label">مستخدم</span>
                                        </div>
                                        <div class="stat">
                                            <span class="value">@branch.BreedersCount</span>
                                            <span class="label">مربي</span>
                                        </div>
                                        <div class="stat">
                                            <span class="value">@branch.TreatmentsCount</span>
                                            <span class="label">علاج</span>
                                        </div>
                                    </div>
                                </div>
                            }
                        </div>
                    }
                    else
                    {
                        <div class="empty-state">
                            <i class="fas fa-building"></i>
                            <p>لا يوجد فروع</p>
                        </div>
                    }
                </div>
            }
            else if (activeTab == "animal-types")
            {
                <div class="tab-content">
                    <div class="tab-header">
                        <h2>أنواع الحيوانات</h2>
                        <button class="btn btn-primary" @onclick="ShowAddAnimalTypeModal">
                            <i class="fas fa-plus"></i>
                            إضافة نوع
                        </button>
                    </div>
                    
                    @if (isLoading)
                    {
                        <div class="loading">
                            <i class="fas fa-spinner fa-spin"></i>
                            جاري التحميل...
                        </div>
                    }
                    else if (animalTypes.Any())
                    {
                        <div class="animal-types-grid">
                            @foreach (var animalType in animalTypes)
                            {
                                <div class="animal-type-card">
                                    <div class="type-header">
                                        <h3>@animalType.Name</h3>
                                        <div class="type-actions">
                                            <button class="btn btn-sm btn-warning" @onclick="() => ShowEditAnimalTypeModal(animalType)">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-sm btn-danger" @onclick="() => DeleteAnimalType(animalType.Id)">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="type-info">
                                        <div class="category @GetCategoryClass(animalType.Category)">
                                            @GetCategoryText(animalType.Category)
                                        </div>
                                        <div class="price">
                                            سعر العلاج: @animalType.TreatmentPrice.ToString("N2") ر.س
                                        </div>
                                        <div class="animals-count">
                                            عدد الحيوانات: @animalType.AnimalsCount
                                        </div>
                                    </div>
                                </div>
                            }
                        </div>
                    }
                    else
                    {
                        <div class="empty-state">
                            <i class="fas fa-paw"></i>
                            <p>لا يوجد أنواع حيوانات</p>
                        </div>
                    }
                </div>
            }
            else if (activeTab == "diseases")
            {
                <div class="tab-content">
                    <div class="tab-header">
                        <h2>الأمراض</h2>
                        <button class="btn btn-primary" @onclick="ShowAddDiseaseModal">
                            <i class="fas fa-plus"></i>
                            إضافة مرض
                        </button>
                    </div>
                    
                    <div class="diseases-list">
                        <div class="disease-item">
                            <h4>الحمى القلاعية</h4>
                            <p>مرض فيروسي يصيب الحيوانات ذات الحافر المشقوق</p>
                            <div class="disease-actions">
                                <button class="btn btn-sm btn-warning">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-sm btn-danger">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                        <!-- More diseases would be loaded here -->
                    </div>
                </div>
            }
            else if (activeTab == "system")
            {
                <div class="tab-content">
                    <div class="tab-header">
                        <h2>إعدادات النظام</h2>
                    </div>
                    
                    <div class="system-settings">
                        <div class="setting-group">
                            <h3>النسخ الاحتياطي</h3>
                            <p>إنشاء نسخة احتياطية من قاعدة البيانات</p>
                            <button class="btn btn-primary">
                                <i class="fas fa-download"></i>
                                إنشاء نسخة احتياطية
                            </button>
                        </div>
                        
                        <div class="setting-group">
                            <h3>تصدير البيانات</h3>
                            <p>تصدير جميع البيانات بصيغة Excel</p>
                            <button class="btn btn-success">
                                <i class="fas fa-file-excel"></i>
                                تصدير البيانات
                            </button>
                        </div>
                        
                        <div class="setting-group">
                            <h3>إعادة تعيين النظام</h3>
                            <p>حذف جميع البيانات وإعادة تعيين النظام</p>
                            <button class="btn btn-danger">
                                <i class="fas fa-exclamation-triangle"></i>
                                إعادة تعيين
                            </button>
                        </div>
                    </div>
                </div>
            }
        </div>
    </div>
</div>

<!-- Modals would go here -->

<style>
    .settings-page {
        max-width: 1400px;
        margin: 0 auto;
    }

    .page-header {
        margin-bottom: 30px;
        padding-bottom: 20px;
        border-bottom: 2px solid #eee;
    }

    .header-content h1 {
        font-size: 2.2rem;
        color: #333;
        margin-bottom: 5px;
        display: flex;
        align-items: center;
        gap: 15px;
    }

    .page-subtitle {
        font-size: 1.1rem;
        color: #666;
        margin-bottom: 0;
    }

    .settings-container {
        display: grid;
        grid-template-columns: 250px 1fr;
        gap: 30px;
        min-height: 600px;
    }

    /* Settings Navigation */
    .settings-nav {
        background: white;
        border-radius: 15px;
        padding: 20px 0;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        height: fit-content;
    }

    .nav-item {
        width: 100%;
        padding: 15px 20px;
        border: none;
        background: none;
        text-align: right;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 12px;
        font-size: 1rem;
        color: #666;
        border-right: 3px solid transparent;
    }

    .nav-item:hover {
        background: #f8f9fa;
        color: #667eea;
    }

    .nav-item.active {
        background: rgba(102, 126, 234, 0.1);
        color: #667eea;
        border-right-color: #667eea;
        font-weight: 600;
    }

    .nav-item i {
        font-size: 1.1rem;
        width: 20px;
    }

    /* Settings Content */
    .settings-content {
        background: white;
        border-radius: 15px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }

    .tab-content {
        padding: 30px;
    }

    .tab-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 30px;
        padding-bottom: 20px;
        border-bottom: 1px solid #eee;
    }

    .tab-header h2 {
        font-size: 1.5rem;
        color: #333;
        margin-bottom: 0;
    }

    .btn {
        padding: 10px 20px;
        border: none;
        border-radius: 8px;
        cursor: pointer;
        font-size: 0.9rem;
        font-weight: 500;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 8px;
        text-decoration: none;
    }

    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
    }

    .btn-success { background: #28a745; color: white; }
    .btn-warning { background: #ffc107; color: white; }
    .btn-danger { background: #dc3545; color: white; }

    .btn-sm {
        padding: 6px 12px;
        font-size: 0.8rem;
    }

    .loading {
        text-align: center;
        padding: 60px 20px;
        color: #666;
        font-size: 1.1rem;
    }

    .loading i {
        font-size: 2rem;
        margin-left: 10px;
        color: #667eea;
    }

    /* Users Grid */
    .users-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 20px;
    }

    .user-card {
        background: #f8f9fa;
        border-radius: 12px;
        padding: 20px;
        position: relative;
        border: 2px solid #e9ecef;
        transition: all 0.3s ease;
    }

    .user-card:hover {
        border-color: #667eea;
        transform: translateY(-2px);
    }

    .user-avatar {
        width: 60px;
        height: 60px;
        background: #667eea;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.5rem;
        margin-bottom: 15px;
    }

    .user-info h3 {
        font-size: 1.2rem;
        color: #333;
        margin-bottom: 5px;
    }

    .user-info p {
        font-size: 0.9rem;
        color: #666;
        margin-bottom: 3px;
    }

    .user-info .username {
        color: #667eea;
        font-weight: 500;
    }

    .user-actions {
        position: absolute;
        top: 15px;
        left: 15px;
        display: flex;
        gap: 5px;
    }

    .user-status {
        position: absolute;
        top: 15px;
        right: 15px;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: 500;
    }

    .user-status.active {
        background: rgba(40, 167, 69, 0.2);
        color: #155724;
    }

    .user-status.inactive {
        background: rgba(220, 53, 69, 0.2);
        color: #721c24;
    }

    /* Branches Grid */
    .branches-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
        gap: 20px;
    }

    .branch-card {
        background: #f8f9fa;
        border-radius: 12px;
        padding: 20px;
        border: 2px solid #e9ecef;
        transition: all 0.3s ease;
    }

    .branch-card:hover {
        border-color: #667eea;
        transform: translateY(-2px);
    }

    .branch-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
        padding-bottom: 10px;
        border-bottom: 1px solid #dee2e6;
    }

    .branch-header h3 {
        font-size: 1.2rem;
        color: #333;
        margin-bottom: 0;
    }

    .branch-actions {
        display: flex;
        gap: 5px;
    }

    .branch-info p {
        font-size: 0.9rem;
        color: #666;
        margin-bottom: 8px;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .branch-info i {
        color: #667eea;
        width: 16px;
    }

    .branch-stats {
        display: flex;
        justify-content: space-around;
        margin-top: 15px;
        padding-top: 15px;
        border-top: 1px solid #dee2e6;
    }

    .stat {
        text-align: center;
    }

    .stat .value {
        display: block;
        font-size: 1.5rem;
        font-weight: 700;
        color: #667eea;
    }

    .stat .label {
        font-size: 0.8rem;
        color: #666;
    }

    /* Animal Types Grid */
    .animal-types-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 20px;
    }

    .animal-type-card {
        background: #f8f9fa;
        border-radius: 12px;
        padding: 20px;
        border: 2px solid #e9ecef;
        transition: all 0.3s ease;
    }

    .animal-type-card:hover {
        border-color: #667eea;
        transform: translateY(-2px);
    }

    .type-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
        padding-bottom: 10px;
        border-bottom: 1px solid #dee2e6;
    }

    .type-header h3 {
        font-size: 1.2rem;
        color: #333;
        margin-bottom: 0;
    }

    .type-actions {
        display: flex;
        gap: 5px;
    }

    .type-info {
        display: flex;
        flex-direction: column;
        gap: 10px;
    }

    .category {
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 500;
        text-align: center;
        width: fit-content;
    }

    .category.free {
        background: rgba(40, 167, 69, 0.2);
        color: #155724;
    }

    .category.economic {
        background: rgba(255, 193, 7, 0.2);
        color: #856404;
    }

    .category.non-economic {
        background: rgba(23, 162, 184, 0.2);
        color: #0c5460;
    }

    .price {
        font-size: 0.9rem;
        color: #28a745;
        font-weight: 600;
    }

    .animals-count {
        font-size: 0.9rem;
        color: #666;
    }

    /* Diseases List */
    .diseases-list {
        display: flex;
        flex-direction: column;
        gap: 15px;
    }

    .disease-item {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        border: 2px solid #e9ecef;
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        transition: all 0.3s ease;
    }

    .disease-item:hover {
        border-color: #667eea;
    }

    .disease-item h4 {
        font-size: 1.1rem;
        color: #333;
        margin-bottom: 5px;
    }

    .disease-item p {
        font-size: 0.9rem;
        color: #666;
        margin-bottom: 0;
    }

    .disease-actions {
        display: flex;
        gap: 5px;
    }

    /* System Settings */
    .system-settings {
        display: flex;
        flex-direction: column;
        gap: 30px;
    }

    .setting-group {
        padding: 25px;
        background: #f8f9fa;
        border-radius: 12px;
        border: 2px solid #e9ecef;
    }

    .setting-group h3 {
        font-size: 1.2rem;
        color: #333;
        margin-bottom: 10px;
    }

    .setting-group p {
        font-size: 0.9rem;
        color: #666;
        margin-bottom: 20px;
    }

    /* Empty State */
    .empty-state {
        text-align: center;
        padding: 60px 20px;
        color: #999;
    }

    .empty-state i {
        font-size: 3rem;
        margin-bottom: 15px;
        opacity: 0.5;
    }

    .empty-state p {
        font-size: 1rem;
        margin-bottom: 0;
    }

    /* Responsive Design */
    @@media (max-width: 768px) {
        .settings-container {
            grid-template-columns: 1fr;
            gap: 20px;
        }

        .settings-nav {
            display: flex;
            overflow-x: auto;
            padding: 10px;
        }

        .nav-item {
            min-width: 120px;
            justify-content: center;
            padding: 10px 15px;
        }

        .nav-item span {
            display: none;
        }

        .users-grid,
        .branches-grid,
        .animal-types-grid {
            grid-template-columns: 1fr;
        }

        .tab-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 15px;
        }

        .user-actions,
        .branch-actions,
        .type-actions {
            position: static;
            margin-top: 15px;
            justify-content: center;
        }

        .user-status {
            position: static;
            margin-top: 10px;
            width: fit-content;
        }
    }
</style>

@code {
    private string activeTab = "users";
    private bool isLoading = false;

    // Data lists
    private List<UserDto> users = new();
    private List<BranchDto> branches = new();
    private List<AnimalTypeDto> animalTypes = new();

    protected override async Task OnInitializedAsync()
    {
        await LoadTabData();
    }

    private void SetActiveTab(string tab)
    {
        activeTab = tab;
        LoadTabData();
    }

    private async Task LoadTabData()
    {
        try
        {
            isLoading = true;
            StateHasChanged();

            switch (activeTab)
            {
                case "users":
                    // Load users - this would need to be implemented in ApiService
                    users = new List<UserDto>();
                    break;
                case "branches":
                    // Load branches - this would need to be implemented in ApiService
                    branches = new List<BranchDto>();
                    break;
                case "animal-types":
                    animalTypes = (await ApiService.GetAnimalTypesAsync()).ToList();
                    break;
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", "حدث خطأ في تحميل البيانات");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private string GetRoleText(UserRole role)
    {
        return role switch
        {
            UserRole.SystemAdmin => "مدير النظام",
            UserRole.BranchManager => "مدير فرع",
            UserRole.Doctor => "طبيب بيطري",
            UserRole.Technician => "فني مختبر",
            UserRole.Receptionist => "موظف استقبال",
            _ => "غير محدد"
        };
    }

    private string GetCategoryText(AnimalCategory category)
    {
        return category switch
        {
            AnimalCategory.Free => "مجاني",
            AnimalCategory.Economic => "اقتصادي",
            AnimalCategory.NonEconomic => "غير اقتصادي",
            _ => "غير محدد"
        };
    }

    private string GetCategoryClass(AnimalCategory category)
    {
        return category switch
        {
            AnimalCategory.Free => "free",
            AnimalCategory.Economic => "economic",
            AnimalCategory.NonEconomic => "non-economic",
            _ => ""
        };
    }

    // Modal functions - these would show modals for adding/editing
    private void ShowAddUserModal() => JSRuntime.InvokeVoidAsync("alert", "سيتم فتح نموذج إضافة مستخدم");
    private void ShowEditUserModal(UserDto user) => JSRuntime.InvokeVoidAsync("alert", $"سيتم فتح نموذج تعديل المستخدم: {user.FullName}");
    private void ShowAddBranchModal() => JSRuntime.InvokeVoidAsync("alert", "سيتم فتح نموذج إضافة فرع");
    private void ShowEditBranchModal(BranchDto branch) => JSRuntime.InvokeVoidAsync("alert", $"سيتم فتح نموذج تعديل الفرع: {branch.Name}");
    private void ShowAddAnimalTypeModal() => JSRuntime.InvokeVoidAsync("alert", "سيتم فتح نموذج إضافة نوع حيوان");
    private void ShowEditAnimalTypeModal(AnimalTypeDto animalType) => JSRuntime.InvokeVoidAsync("alert", $"سيتم فتح نموذج تعديل نوع الحيوان: {animalType.Name}");
    private void ShowAddDiseaseModal() => JSRuntime.InvokeVoidAsync("alert", "سيتم فتح نموذج إضافة مرض");

    // Delete functions
    private async Task DeleteUser(int userId)
    {
        var confirmed = await JSRuntime.InvokeAsync<bool>("confirm", "هل أنت متأكد من حذف هذا المستخدم؟");
        if (confirmed)
        {
            await JSRuntime.InvokeVoidAsync("alert", "سيتم حذف المستخدم");
        }
    }

    private async Task DeleteBranch(int branchId)
    {
        var confirmed = await JSRuntime.InvokeAsync<bool>("confirm", "هل أنت متأكد من حذف هذا الفرع؟");
        if (confirmed)
        {
            await JSRuntime.InvokeVoidAsync("alert", "سيتم حذف الفرع");
        }
    }

    private async Task DeleteAnimalType(int animalTypeId)
    {
        var confirmed = await JSRuntime.InvokeAsync<bool>("confirm", "هل أنت متأكد من حذف نوع الحيوان؟");
        if (confirmed)
        {
            try
            {
                var success = await ApiService.DeleteAnimalTypeAsync(animalTypeId);
                if (success)
                {
                    animalTypes.RemoveAll(at => at.Id == animalTypeId);
                    StateHasChanged();
                    await JSRuntime.InvokeVoidAsync("alert", "تم حذف نوع الحيوان بنجاح");
                }
                else
                {
                    await JSRuntime.InvokeVoidAsync("alert", "حدث خطأ في حذف نوع الحيوان");
                }
            }
            catch (Exception ex)
            {
                await JSRuntime.InvokeVoidAsync("alert", "حدث خطأ غير متوقع");
            }
        }
    }
}
