using System.ComponentModel.DataAnnotations;

namespace Baytara.Shared.DTOs
{
    public class BranchDto
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string? Address { get; set; }

        public string? Email { get; set; }
        public bool IsActive { get; set; }
        public int UsersCount { get; set; }
        public int BreedersCount { get; set; }
        public int TreatmentsCount { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }

    public class CreateBranchDto
    {
        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;
        
        [StringLength(500)]
        public string? Address { get; set; }
        

        
        [EmailAddress]
        [StringLength(100)]
        public string? Email { get; set; }
    }

    public class UpdateBranchDto
    {
        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;
        
        [StringLength(500)]
        public string? Address { get; set; }
        

        
        [EmailAddress]
        [StringLength(100)]
        public string? Email { get; set; }
        
        public bool IsActive { get; set; }
    }
}
