{"GlobalPropertiesHash": "2ilJ2M8+ZdH0swl4cXFj9Ji8kay0R08ISE/fEc+OL0o=", "FingerprintPatternsHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["Xj8C96NR/d0a2E106/74gwiJp5zPSYfu///ADCGFnGU=", "QmnK2ZeyIhUF2mPznBCvhf3vZOmTMiWJYHtfq4yC1Ns=", "WRpMsp6pcfkl2yYLY6jj1hnjldnej3eAPIAKcC8B6l0=", "D0wPx2kO9rU/MPMNP/UEGkZ2G6dm9MFuTnRvVZkcg3U=", "AzUafVbPceY2/mJQzj1IFzyM7PMtmuuviaiRym7Z2pk=", "WW36acrvy1mo7FZzYbcTj9JisZQfHlhko/aqwC/cAbw=", "oZUBJ68KplnhwftkMQs2t8gMXP4nAo4SQt0iUiOY+rY=", "bBPjN5SkVUZoGeLennS3t0rAGPCEDMzNI1TP3gt9m0M=", "RS9aO7A1IxCtDXn6v9g27rmQfhG0xBiLgZbOfJUJtBQ=", "TtNbEDLYh5U+k8hvtnejGKmi75LYz5XJng5+ECf7ZRM=", "1QXIkjdOjxTCOW7gzEPeQzUGUU/vW5mvCbmQMOBLzm0=", "IQtXx9w7znx4BcUKUIFj1/U6waCQo5biaJi0HPn/zhA=", "d2CLDO/ia33nha8d1eIPQNf6m3knfl4cDN7PYLoRR+g=", "1xUV+tVlvBihQEK6FjrOWaehkIMgoShtx5AQSrDrMYw=", "Dqvwn5rhNzoXMFeVTfL/oPL07rf2ze4Ald22tsFxMXY=", "dxbXI5Z3SKZScP0Esgei3K6rz2Yj2cN2nXKNEqg/d7k=", "Ji2wO4Ldrwxj+g9Dj0+wHHpEOr4hh9fkLNGsV4W3l2Q=", "ED7QARQKHnkJamhxLARMvBgnH7aP2x7u6YyLfAWY6XM=", "LKvFlTOzzaPJdTBilRbMUbj4AzwA865VVBWALjrEHAk=", "4TAbcHKuE05rHFANwoqDFjNTtqW8wpMyEBOvZeLcm6k=", "lHMwEFPpxW8E6n0px8Ur8BwiVDFEVDAXgWjmTV1RzdE=", "8CXbPTreiQKIqpCMY7n32h10SQJ4Ptdg6kvr13Jt0xo=", "vsrONsnPNJ9JpEapb187ARe2o6IvFfcqZkBtDAt4NCY=", "ua40J18/NKOsHFC2GjiKE+wY9uid2E7DlTmYrziaQH8=", "eIa5qa3lvhZGz6d1exluJTGHoLkS/ItNQ0XQHs8+bJY=", "8zLMw0jCg3jMeFkIId6Wl8qZ6D9zV3lO2IluALbWdnw=", "oD25mJWPq5r+om/HqXX0pHv2bgaNCVoRLyc6p9XOr7c=", "YEWoNhSHKb5gdNqUtTj74wKUIAV7lvAzGRljU5Qn5NI=", "ZbocjrTMieWWSRaP0VHVmfrY/k+EieHcQHkiSqBW/xo=", "GS+ZnXvaW3H1jASiesjFiTM2G4F+DFTOIUc7ZybfbhU=", "ZAS/VwBDE2dtoi1LiWI9GPCQ5fK6vCPcc+QHmHuDEJc=", "Pc0KJv/NUIGo7PI0HixA321QTFg8LDqavirrPUDqf5A=", "W1IefpYJHlbz+JiAxlz+zAGcAxby4KpCai/lZ/OcGpg=", "0IDPmZ8K2MQ+a5SXz7f4JrcKVWazHrtO16Orjbs5L3w=", "ypIbCBcfsBvmO4Clvbx6xtT1LEunke/8Td1020OiLDM=", "f3ZXeabtsqzV1JW/JnOqr/xkiBGaqa78eHX3e8AntYM=", "twOjZ0X8X+jOEaIJ/jR6Sykl9ynHOZIvlnMacp5JFSk=", "Dfgzr0Ixu1qwGS+mgyhbrh06vd+U8kHeiQB9dBhwOo8=", "d3ub2ETiOwMSjvHd8OQB6vWJcllbKOrnCSXH2BNnY9A=", "kEGXeSjL5OYTdJFKvho0vyOm0TuBmlFt/v8oulSWYxw=", "e6yzTmGD4PWTJpXxSWgk0i1+hm//tAxx9Ti4bCCrHBc=", "gcgrPWE4oLw+u8Uilt//djwKCynXyC0OpAqXg1PBkgM=", "NbmvFwiwuEXjnDK6tXewTeF6x6KFbgi3SIFVn94SezQ=", "mNVUmpEjfbRmuvaDmIbq7kwOIKIwFi0X/i/DAW5vjDs=", "AonwFlzOnJe2/FaqX5Pu/N6wXf7lTtuEj/rS2hJO3F0=", "EwEeXLgyA7z8MxiJiiNXnjteltBKlAbN6Ef6w/39pbI=", "SBiGxug5JrtUnv24OBu/SRhvgZNBBxX0i8b+gGAXJZE=", "idHRG5Wx6bRWMN4NDLFvfVNUt4zSUFeP8hi8Z2D7Y8k=", "I7/x6rJO6tsjU7Qk+JDed/8UzbbrL8H+F13mnJ+r3Gg=", "qjOFuuFC9bBY8ciKsbL/G0j53xxZ/DCLh/8OVvDOkwU=", "jLp1LYOEcwVEIjQ6+UoiuguBeTqoy1Qz6ZPbVq0oI/s=", "ap4TxBB6Jxb+2rc04Hum4b1d5zYm9BXERKRREW1nTrE=", "BtgFwW4c34GQKwjjIfdU/xVpqaUaEjS057vHRILtC2s=", "Hcbxl33PbMSzfNw3kwHqSdnJC9Pm8urYblCUCe0zyAc=", "yYBT7G7k7IIJgLmtHJArvDWer7Sbh4nex+w/f/7u6j0=", "UUynjsPgjhkoj6DPcPjWrdw1shsnO39hfcT89w0uhoQ=", "EnzBs2LT0hJhhod8eDYg3nBpEN6IU+hOLGgQt/IkJLs=", "E9czMEGxOQX+Wl8c09exwiyfL1x33hXT8NDVOJvDYoA=", "3yBNdGwcOY4krKroywIlmPBZ/cO2X12jFfkThS0jzNg=", "9nvsfw09qKG4OKT+oZn5t4oqZ7i90dTLHA6Ru0RmFfU=", "dW2cQoHpej7Gl2X/vM2rNqzfe2NZE0FwIXURhcAMP0g=", "qv31vOiWFFVOmkPowNeCnppl52Nbv7JWMbqc4FC94sY=", "NlQ9APmKWKnNUBsl6MakQdVIoB5rktlRc+Lf/Ij54tI=", "gU2najVYHmpVb+V5nuAGmM6OK7kH4JbAu0EiGDhQugo=", "Yo+BLKcyunHmx+CHlYVU1OObUmcaE+DvkDINZYJ3xYg=", "LDgGzs65Wq+FrPgsbX5Jjc3f8dQmydqUXdju8vcQrNY=", "x3cmWiSgQ/dOaLbL+KOtdeqlL5tJPXOkZGCzwXnXnGE=", "LlcxpyGQDVswPtxM0nL0Y8RtRVJ8tMWSU7EJO8cR78M=", "ITppAKnl8UZTgcHx2MZeQ3etvW26sMe3UXMq9bz/oZw=", "UlyJFoucmckMBaOXVTlltQs8zSe8pyCLk1s+V8U+Kgs=", "i3/1C4LMbWlmmF81vYojNCO5AADyQrCtCERqBvvOQ5o=", "E+HHSH/m9+MXxd2G6bqy6wOMUzdZbKGfNEm4WNM2Eis=", "jCTtIvZiACSQ/ac92kB3v9IzdGWaPo4FSHrTwViPJhQ=", "M89KssnjRJr0NOF4akwoReoSn52OwMeU4O3bfsviiJo=", "9jhfDdLqF/UtNJfTe17I3kOqqWhU3BtnxEYX3s8Us/Q=", "4uLd6p4H3qSVyVueI3YV8P5blimF3ePuxiCbCKlrwqQ=", "AW/jdcmDIYWhG1ITXHMwb141jA+TUZ6E6KSRgUrGFc0=", "YmDrM/TgtBzOQtba4fEL9KSZuIT7972kMfeQWw7maJA=", "VizBzXWclzgdqAv717Lv/TmdbrN2riWNbnGaQYoLhqU=", "8ynlRjMUVhs4XJ8Q8Os9KSglbF1foOQYjHgHAbmFpEc=", "3itLM3Y5DGw0/6mfeXgGBLBWoPSNOz735VvdtojIA2Q=", "UIDZ3fbuM5kJ6SZT4EpEs4rXN01SLwTY2pcZ+aU9VZM=", "qOd6OI5ZfG0namjO2g/nZpsEEHzESwR7GjPhv0yBfrY=", "JKtNff9rOSfpkZdFB2ws/KGsSQixfnbYUAWPt3XhHW8=", "gWkMhDdngUr7p/uN4eNp2mAmJFipssndrwH1sXn+fVE=", "3sZNcua0P1SlwfsI7GrSzChvle2WWAYc0jdSV4yLLy4=", "Ij6ruZrKsmRRxt3DGima9gIZFddmg2ikldObmXO37yg=", "cMn6O0839rFgwkc2CDNbGpJgK0e6eYYbPR+MZdIgt7k=", "gQQ/vnJNf4QLB6nNV0qXJ4sCCARNC0iTIIIwn5/0OQ0=", "mAU7zrOBfy/vkLPDWCm7PsFG23s7+voR0MJEbmGFax4=", "1Xb6VhFAxHQDRV8PNDWgG1U+KuxjS7mkc9UXXEfB/Ew=", "tML3s3ambg2p6o/fIdMnhzGlMX+zt5CExLLuhel8Alk=", "drT4PMKO87OtSLJ0mAT3u0Xc88ut+OdkznRjiz4NQy0=", "LKsiNsU/JhiqE7rExpN0Ert2QuFwFE4H6iq6rjgDqgg=", "jrzRICbTpDas6WUiZj4XyTMqdz0+yVLtgSPXp2Cj6lM=", "Uc30ggP9ZIyEbusMYRS11b1s3kVIATom60Od76zAXPQ=", "o85mkRYhhvB+SzvVUOGuL4Zgg4hniFe3yPQZYALeIBg=", "dzLtqRl06sQj02SQQ0OqbyUVZfoqf4oX4HSfAa5/ZPA=", "/qFj7ZKESalI3zgFsRlUo6YcR17DOY5L0FQvzz8o/24=", "qu7Q/axo3iTQo6x1OHN0hdIUyNrv4MRAkEWC6ptMGsk=", "5JsXpt9e9Fj5tGb+P43UXGu+mbltrUJmJY2TI2auSu0=", "EFK05tw5CVtNDZU02uiA393oRqr5uGVxyjiVccWzvWw=", "KQyFGRr2IEfiDFd+EyFP7kBxYHR1xCr/6lwaoQQFRPs=", "TDARLNvWzDqUAIk751+uZt14JadIV83BhZIiDHQjvtg=", "6oaOlIARRC9rrbW52AvbogjEX62WyUjRD+sSO7MrffM=", "9u30KFfG0FFmn+Tt/laF5DW3vXzDFsBr8z4n9hAA8LA=", "VQWUcRYL55D3DWF1Zv1Uo28WBFoTio+SiGCBKflKMH8=", "rn92Dt49VRaQ8aysS0wJ+eX8IkaMqli80a5byjTpSJA=", "XppBnLXDv5OGgJMC8IWUQHJaqq7mrQHw+Pw194Lurrg=", "kzNU0ZeM79EoEijZ6N1/UHe/fxToH9H29SQJQWaULvM=", "H5DKxtgvXTM1uz6FxVRAiUhOeU7FiM7CtumBsGfeHkc=", "cmTS27oy7F+uRhp9IBd0QXneidCPyTJ3ZhAnW6seTBQ=", "+TTobrln5RDfN0POZwW8LIXrAYiyGmGSn+IiwMS90N0=", "ftQcKfrgOTjCj3Xc3QxYk9/96CDlcqIm19tMzmx/9xs=", "1u1rTXPyeRfh4yfBJPNPK/KJM+breaKw7zsu9n3hpMI=", "F/OVX3KF8ASVG1wL4uE82dQI7iWs0hyalyf+AxNaXi0=", "fWqFFVvBto3EliyUWZGI9EDF0RsseRUOdULQ8jBQVQs=", "3g2xmPswtD7vFLYKOFNXYScfAkZfCcExVwZknaqNRKA=", "D59lzpmequoGTeEWUTbhQneHdKRRNbDBAeAKvQTqmWY=", "CkRDQU0kKtGz39xtl2CO/5w+2Cdzk1twUwChmc05Knw=", "oppGS2pNelYcQ121Jzzd//+HIY4nZSULtdSsvWevHKk=", "wbFZ5sjUs/gxGTkqgZWY0Z966mnO6Po90SonBVnI4xk=", "ARPPSK4hzWCmf9WtjlfKHsmuZi/CnGAWm16t/Cp8z3k=", "WZDZQQY0NP1WmXSpYsowjR42IvaufaGOGWMr8U4Ca98=", "sEwo0Z4vVRCc4VI6ynoTvwaW7J1zOZzXo/+fu22tTEU=", "3h+XF8ZvNWFWonqwQJ7ZuvSNp1SX1XZC+vGv+dwhTDo=", "m5bxLRHQHf0SB+caDQ4Y7geVkoYsbwdHtAEL8FPo6Fc=", "XcR+/lsWCLMIpvNLvgYeYGl5TpabDWsb47sxu691GH8=", "b+WPh6aKlAhUojkgA3nCsdwkfbLnH8sYd2rkGYzTjjo=", "AkuVabBtzDuaSDTdPgpnivbBCxrmOYDlNYvwOgk5NEc=", "/EORn8XbgQvcxjkfHq7xoCmt2AuNoTDb0fQ7Md2ZwZc=", "SnbvVevlG/FSRn66lwxg+NAEiociKF8rEN31dmPdguc=", "0cbua7u4gN2tdN+Y9n0e8llR13cRSOR2Bms5jDSS6c8=", "dQ7SVvPH09IcNe6iRaUHJWzLsei/yF9lPJKPYJP7FMA=", "OX8UylQySZ7xqDs9PhiIS7UyCsizcrCl1QHvMkC0ftY=", "sEn5U6/gO/UhjY1SSUdl/6qJekjdkSJvgmqJnN8dtGA=", "/r0j7eGMLBf5NSEMX4O1jw1Se46i7CCYOKZ7I4heyqA=", "3Kj2TxctGf+P8UDgSuFIz680ZAVOJdV6eNvXMBk6u94=", "0+Yt5DU90zhAO4D5Ggqg6qXI5t8K9xXKvU6K6IJLLKk=", "j4OlOYTh58ThMBCP/z81dtAhYnWj92E0KbyatGbrdtU=", "gW//ThPU28yBjhvHOejTt84rLdMM9l045Ra8i0yqkKk=", "KpV8BiV2+zrfhw1F5gGO5dIDsAN39nnBkPWUQltLW/c=", "cD+giGx0FmRfHUDFXtnOmk+QWjabnjUu3oxjC9gT9tw=", "/2XFpxSp9B7M7AFsErjW/UYnYEYHatn9krVTkmDYHkc=", "kD3KDx9PARV1exO+OHMtSmjnZEoS1pDbfN+Kty6nD4o=", "HLqvG//CJiRw8Qm7s/3QN0h5BB7/t31wsJR3Z8aVtw0=", "hXpAsyYoz8+CjHiU97WVRUUQKQx8oNZjEjRNySpQx00=", "poddOD8fSIAZpeiX0rbLegyDxNPQTnKdbvKBp3ZGiYc=", "uE877kn0+qDrQvM2T2WHagwK5EiLljjUdMgxNpC5Uuc=", "0hkA2oo2l3KoO34W2bKoCREBSZSvsfrbsLkYKAH6pJw=", "6XI1dJ33/pKS7r6P8CmUIVboeUqJz7Eb+dxXFLC3mQ8=", "ZEZVU75fSWgaKKO5VToBbOI73zqV9wjxwa16hBnS9js=", "Ct00eUXj9XNVv2bzG+P5DsEE3N+lAEf1jpqaP1XAACY=", "kmDUr0xiskIhHZAYX7zFGIorGzDx+10RozH9meyO4i4=", "KYYKj/9YWMbIj7ZBJNbE+WBvneMjize/P9mmM8IgAhY=", "dCVFksy44IfSj5onlNA6mhvWNMMCQ0LHbXEVjhLUV8M=", "hBmgTmmkP60h5VaSD5EUfpl+ZsPV6X4tdGpXmtBKXaI=", "4PFFLxC/X8RajXU7D776WylLtIoYWDJLBVIhs2veMFA=", "qcDjwnjp+GqicXaJ7xmvyCPTk5klPOXmO5Z/Ho0iwus=", "A5k+f/+/HDWMafoFVOGoP4ODVnhHWFWU+CmUgTaAF+A=", "yQZE5Fd2eiTy0CndFRGnmi7dW5L1mhdPMV1vDRw3z5s=", "aubdlT4Wrb67H3RYY+7UaYrhbP2STKQ6GJRXT2ZEQ8s=", "fhNWjm3JrflEfqGNLmR/HvPXED7CizIMz7q4sBCfetA=", "ley+OlhMErDIfjSxDvmfhldXtIu91ltvwwyfIzgWkPU=", "8MbLVJRVibeYtJXV+b3dR4772LCdC7y62bz4olc8hJI=", "J4xDTW35ST3yHs73T5jVlK2GUcE+z4j1+pXeWn9Pus0=", "nCu2tFUxGCG9BwEQ/160Lb2tnbBKVQmwAjt3HVliFfo=", "a3as++f06miTQBCSOvOxaXza6aUiU2c0cojzh45/bBI=", "5YCeTH4cj0K9EwlZ8M20y5/PidGdcpeERcnxZtCErEg=", "NX6jfz15S7PlIsHb+FfEZH8+AQJXBM+qM11vmLw8Myk=", "9CRSkoqO3poFHIzKMeysRozJVs94CKRd64e94GUV1TU=", "ETwMamOo5SwUAAF80Y/oW9dXdD0Gqo1AvgRfRL9GlwE=", "H1TaaSJwNghS1d2khn8wmsvHJ7kwd1S4esFNNAFji+0=", "rjKF8Q5RXiitQMtt9j0IzZvIq11fx4Ydz+gT+VM0l7s=", "DhSQOgCgoHbw38k+t6NjZdifU/6oBrc2dpvEk2L1bWM=", "FPE8lkeMtzh+x8MSdJ0D3DjZj/pOU/EnemdLsRhhM48=", "Z+q9kjLtMp8ebefWbPnN+J2yrWA5o+oYH56qvowOBn0=", "EzGJ/h3SN4x/qeYFsIHWpEkDAigUsVg2gMmZIqwq3uY=", "Ga9CScmsFEK3d89oGz2BCmWaaqGLuqDAk/EP0T50UtI=", "iY/SaC0Qga2nbuwZvNG0FvyNrPZtjzPekodl/OAv3Ns=", "N+QlYtSC8COS4eE9lLCKpPTY1kBf9lEwrBdZs/6+U4M=", "fMW6KGdIkmlZHhtPdFgDMNBGRY/RD0nC0PGRNpGaxqk=", "v/MzgK7Fxa/zCVXb7fr9pD76cqT+rUMIeVDo/hk6mUg=", "iSlVOuM4u7d3EZEnMNO5I1nny3Mgs1A86b4anUISjaM=", "FajMYnq/L73MZmlhH9ByuecTEO9wkm+AyQys6MV0uUg=", "l/BZFFi+nbfTjSnybkL1mJsj0oDqVr+e6EWdT6KlWAM=", "etyi5p+qcOVKh1z6bx2LpvQhHwwGFdXYObz+MLoMAi4=", "BL8jSydZnNEfTsiqUnLNfn4qnBTNZws+srEN4VBqb2A=", "hmYOwiLsffbD8wII44JS+J6JkBPkSfN6EYG9dmJ7Cc8=", "TFPxJ4lJ/bOK3EKwuh7lL/DcwzE6kh/villgMbAWywI=", "4N5RMIQZ512xsE3mFNMRsPJtptjpzaIvpMuoBOrJ24M=", "eurpMS7wXEG7luVbdZmplE0Ma+5pHPpZv73jzApYego=", "i9dUbqcVZyU40fqbGYwAFcaAmaqD3XRAir+/HmKf6xw=", "ilfM6fgaBuyriQZ68MhD351v8HRukMxLf+5Ls8fuWjw=", "o306Qc+MQ7lds8L5Pak80PWaHSCtvatgJfVwy6tAv94=", "cuOxhpsKjcAnLPSZ35K3LtR/WJU1T69e6i6g9jOJxoc=", "bQN1HkLMvrG4cZsT6BlaZRc9WjmTGqcDr60c+nlDLX8=", "G92TYbfTfj1gnOyyKyUDlKge0652tq3BlVud31HFi6w=", "W5mxNBJCjRQAc6WdFOpdgfETPPNu1p/Uts1ivlrg/7I=", "V5jGMvtlIXXCFduY1hai7TWCrj6/G2yNlboNOyLIjQI=", "eKJndRn39RRpGJAou1NtqH+FpoaJ9bLmQMb/FWZG3kQ=", "A0IB5rVSXIe9ULfXMtGhDdinPYs0r2hM5dbZiz9sMSc=", "F7gtaRY/+G84pdkMqsDZ7x2Kzlj3Huit8tEOgJnX3nE=", "B50NyEHEQcJxMpXjYGQg/WSscSfTQi7vv7ryVQwwLc8=", "WuWAtVQGkD2SyHO1yMv3hyHfmnEREmVxWW/6HDO22Yg=", "8wvSNcCPbeSF5awmUeEoB4MmqNcbyXUyt5+jUEmDFpQ=", "7RuP9F+XCspTTUgMvu4BgbOthys/JgWMp1YHQgZYz4c=", "SHsmRzbx8GDPCJ1wXlXcfwh/CWUsrRqMj00oApIHss0=", "IAw2qzel7bcQVK3KULVAoCQ+7wTOP9omDF1kXMtWIck=", "7g8znL96eajlen0ffjvgShjK4ytwc39g/FiB1WxeUHg=", "gs8kBo8jdLzreXpMz8OvaDJjlcEqnLUUOJUiNCec2Ek=", "w7UjViHDQRpPnq/FrghDw+ZpJpNPlzOBKaUoQlHuKgo=", "+lQl40ryQL1yzm0Lf8h8EES+Zz9DRFGt+o1trDSLgFU=", "ARztHb86TDsJeAswv9kLoNsMeF7mHb2xPe3FTbI+7X4=", "DBNjNvQ6HtjEmk5UM3eB/4YumFbXh4fZVlSrcN2zIjg=", "/oeJcdGgczVVcq0MmXeK4xxyL+lR1yUOeYnXxTO6Ulo=", "PwjO20bAv450aIvu+FnoE8D9O2bfN/eBu3exXJU/Og0=", "5N0K9H2NvRdViHcuP0UdC2nxsTgr6SYTWH0blUvVwlY=", "L9DawE6Thwy0rbNXKvb2eyDKP4eMYyqYKMxleJgfDjM=", "sQpp76gpR5JgVT1jBO6VsCaa95Tuuoxzi+51GFDcqVQ=", "zcD51Z7f6R7Q6stS6F+9eQKpzHeCUMurKiU1EUdr9W4=", "/28jA1c2CwDtLkroYJziGqd3apCS46zQOA3Qq7Wpl6c=", "krcsxcsh9uDZi1lwObaHQPCMXJNOv1cP7V+4p5IvdVk=", "AcR7GyRwmXqtMirDZCfAdVOwgdNjLfggq3aqKogbD3M=", "ccIQAE9R/RwyMWovGsnAcBn/knFotenArwF5OnroJmQ=", "vWYafcd7IGByQ6FIsU5LmvTtvsl+FTHEAhDFj1sY9cY=", "xpk8+fo/zXdJ8jOKfnBdJgg9Xh6+nOuQoDNaR+wO0PE=", "VCxsgLQxNPqYz+LpODRw1/rw/HFgfAIl1d18O7kQx2U=", "S3hjEmT428sHfTFbxPCElfTb5JETnMaCTeGvwY3rNpE=", "CPeY2LXc/b2lPinEgwX8vgkT7Pu5WlYgUgNhW6qYgXI=", "wppyCTj+25W29OJ8WwKe5eJIgrAdCz6eAZPFtXhG5Rk=", "vdmAPWqrkEQRh/YaUsgf1NLl+SXKP5U/QfvYmucTTmA=", "+KAt4d9YVekqhJR8+vMQmtasUmbD8TpttUS1s5KZt7M=", "SNVsrt5q1EYNH+pNlbJftq+O7ONP+56kkeOdVS4GbPU=", "0Jv5XaPLGsNf0z//ZlxDfnA7s+nDLcG3mYQibTpVo+c=", "sxIa5VSxQDcaBiQNBLk3nV5hjT1esqzUqizGek9brYs=", "I5T1KcjAdJ8XWJDgMOEWa8RMb3B5FctlIMZAMdRAQ/w=", "mAqkO5gxchiGKQQOF6ofWyDXgYyLCM8eMYcBwUOvaJE=", "kp4Ut10xSlzvvInh/ZsDzNPFucz3RxyKyVuLKc1PurA=", "gm8pifucYdI6zgG0RzZvVrxC6o+Ndi3qPgPAUr4+aFo=", "g5L68nDlNM6+S7L9XUC//9Qs1j0Df3i8QUkjI4/A4RA=", "BJJylXHzLx993LZbbAU3Wv3N8mwIbVBuRykSBDFYzwo=", "ptcjW5qO7sAJIZHjQnMPWm90nJq8oxRJ7SDTZUBXD5A=", "L+Xs+6LAUaLxVYkaKvR0XLrME/V3Uc3R9QPkY1E6kxc=", "J7MUbavpruL4C17ZXU85sm7L7G3WjOa2IBQWTw59NBo=", "Hgqrw3FZFMiJ7CpegkwXTxgiLb2xkDTCMH/tlcW3MII=", "Si3/QZa/yS12kK8owR5bj3jXUFTCTQHJ1rpbNWvsKGI=", "hrFCTZxXcpz3HaX3qVqznPAY2772pi8B6+vp8CS0Oyk=", "j5Awyi2kufZyhj4tNXkSvRqaSBaINvDkSVY40jOAvwI=", "v6rke7SKaW3olIooLs85MTznFU2Enm9/ekmrL0B6zOQ=", "CTLmrchRTqNIT2GYQikvtzY52Rkt5GgJY67xZF6STXU=", "6GYHdn2IE4e7Lx+atekLV1WZH6Wt4UMFOYrL9qKKECs=", "xJPARuimQOjNto/Zdx5ls4iEH6KCmEeZoGF4k8PgS00=", "2n6j/7s1CMpdl2nIuuGL7te1j3MGhH0m8bh/aesHu4w=", "JvjmNEbzcRb4meBPCRDloHZuLqHk8HrUwdV/6MfzFow=", "1jg6yhv7YhtouytZyv9ttPA/BQ7TM8NEHFcLjXS41Mc=", "u8eH/kBW7wBXoJCa7q83dHxwzjO3Kb4slfAfeARmF8g=", "plPNMGRbU6ZMiVwe65xzGYmyzp2ysZ/t6SSPSXXO8fQ=", "qjk2GElCqNdsUtu5ytM77r2pskBN5tDsGrZvZVihP7c="], "CachedAssets": {"Xj8C96NR/d0a2E106/74gwiJp5zPSYfu///ADCGFnGU=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\f32kn295nz-gcvjxldlff.gz", "SourceId": "Microsoft.AspNetCore.Components.WebAssembly.Authentication", "SourceType": "Package", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.AspNetCore.Components.WebAssembly.Authentication", "RelativePath": "AuthenticationService.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.components.webassembly.authentication\\9.0.6\\staticwebassets\\AuthenticationService.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "max6bozg23", "Integrity": "Dmk/HbaCkisGr8Vgk5M4ZS0yeQNNs/uoWecA7Wrysbs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.components.webassembly.authentication\\9.0.6\\staticwebassets\\AuthenticationService.js", "FileLength": 75085, "LastWriteTime": "2025-06-23T06:00:51.4249153+00:00"}, "QmnK2ZeyIhUF2mPznBCvhf3vZOmTMiWJYHtfq4yC1Ns=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\gvea8o5ayi-6rjnawrlwh.gz", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "css/app#[.{fingerprint=6rjnawrlwh}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\wwwroot\\css\\app.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1czl51krt0", "Integrity": "IW/V8sK1kUp37H/J20+E2BSlR6hYHpjRiuu1+qa3yDk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\wwwroot\\css\\app.css", "FileLength": 2053, "LastWriteTime": "2025-06-23T06:00:51.3757848+00:00"}, "WRpMsp6pcfkl2yYLY6jj1hnjldnej3eAPIAKcC8B6l0=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\aknrcftlnl-wxv5hxfkk4.gz", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "images/logo#[.{fingerprint=wxv5hxfkk4}]?.svg.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\wwwroot\\images\\logo.svg", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "loxz1eaozz", "Integrity": "Rc4jy5U9UMFa2za1byadoA4xRHLqocAaf3twcOGA85U=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\wwwroot\\images\\logo.svg", "FileLength": 551, "LastWriteTime": "2025-06-23T06:00:51.3777927+00:00"}, "D0wPx2kO9rU/MPMNP/UEGkZ2G6dm9MFuTnRvVZkcg3U=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\pedj6js7rk-46c9877kjo.gz", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "index#[.{fingerprint=46c9877kjo}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\wwwroot\\index.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2xntu6uzdz", "Integrity": "VnQVEbHmSYcjSomhcv+bJ660P10rX9n9rjH5vmBc14g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\wwwroot\\index.html", "FileLength": 846, "LastWriteTime": "2025-06-23T06:00:51.3838152+00:00"}, "AzUafVbPceY2/mJQzj1IFzyM7PMtmuuviaiRym7Z2pk=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\pkrlppxero-bqjiyaj88i.gz", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint=bqjiyaj88i}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yf1pcrzlgs", "Integrity": "jhvPrvWZn8BbeR49W+r+MLNcnTeFyaSXxry9n1ctwy4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 6745, "LastWriteTime": "2025-06-23T06:00:51.3858225+00:00"}, "WW36acrvy1mo7FZzYbcTj9JisZQfHlhko/aqwC/cAbw=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\mozzebg29h-c2jlpeoesf.gz", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint=c2jlpeoesf}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "goh6yfn9uv", "Integrity": "ALPf6qsZMu+Ui8l8jPJJF3MhTcq6uwrQhRWeWJL4ixU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 32794, "LastWriteTime": "2025-06-23T06:00:51.3972902+00:00"}, "oZUBJ68KplnhwftkMQs2t8gMXP4nAo4SQt0iUiOY+rY=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\ypv0x2z36g-erw9l3u2r3.gz", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint=erw9l3u2r3}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ff2i3b225l", "Integrity": "y2vSlIdcL+ImKFhcBMT5ujdAP1cyOZlHZK434Aiu0KA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 5969, "LastWriteTime": "2025-06-23T06:00:51.4013041+00:00"}, "bBPjN5SkVUZoGeLennS3t0rAGPCEDMzNI1TP3gt9m0M=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\308uiji20w-aexeepp0ev.gz", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint=aexeepp0ev}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p2onhk81wj", "Integrity": "oRq8VZWOZX9mMbVVZBzw8rSxg8D8d6u0L0zM8MMIvaE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 13807, "LastWriteTime": "2025-06-23T06:00:51.4094326+00:00"}, "RS9aO7A1IxCtDXn6v9g27rmQfhG0xBiLgZbOfJUJtBQ=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\w6br6pgqsq-d7shbmvgxk.gz", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint=d7shbmvgxk}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "td9xh3ux7u", "Integrity": "P5V7Xl3ceCLw6wDeOyAezE6gOa9re3B7gTUN/H/cDsY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 6749, "LastWriteTime": "2025-06-23T06:00:51.4289306+00:00"}, "TtNbEDLYh5U+k8hvtnejGKmi75LYz5XJng5+ECf7ZRM=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\hgocfi3yyg-ausgxo2sd3.gz", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint=ausgxo2sd3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vo1c50q1ou", "Integrity": "cWfRgogdfOCr54Ae/lxY515FP4TJyUwc4Ae6uejBPI0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 32793, "LastWriteTime": "2025-06-23T06:00:51.4094326+00:00"}, "1QXIkjdOjxTCOW7gzEPeQzUGUU/vW5mvCbmQMOBLzm0=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\4wtrc6f4dx-k8d9w2qqmf.gz", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint=k8d9w2qqmf}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yj3hsx47l", "Integrity": "ujhFfu7SIw4cL8FWI0ezmD29C7bGSesJvlEcySm5beY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 5971, "LastWriteTime": "2025-06-23T06:00:51.4134478+00:00"}, "IQtXx9w7znx4BcUKUIFj1/U6waCQo5biaJi0HPn/zhA=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\e71c4gxx5a-cosvhxvwiu.gz", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint=cosvhxvwiu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d7q4cn5biw", "Integrity": "V0pKRwbw4DysvYMPCNK3s+wSdDLvMWJFO+hKrptop7A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 13815, "LastWriteTime": "2025-06-23T06:00:51.4174618+00:00"}, "d2CLDO/ia33nha8d1eIPQNf6m3knfl4cDN7PYLoRR+g=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\6axn0ny9bk-ub07r2b239.gz", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint=ub07r2b239}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4tzbrq9c6f", "Integrity": "+EIaQ03ZHgfVopnrJFjz7ZgQSAO9GeMOK+bzccTIQyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 3380, "LastWriteTime": "2025-06-23T06:00:51.4192053+00:00"}, "1xUV+tVlvBihQEK6FjrOWaehkIMgoShtx5AQSrDrMYw=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\4ai3x35njl-fvhpjtyr6v.gz", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint=fvhpjtyr6v}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yedk7y2ovv", "Integrity": "FjCFey27wknG6tewZOhPfnDgvIw+sTBly7wTSXKSd8M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 25821, "LastWriteTime": "2025-06-23T06:00:51.4229064+00:00"}, "Dqvwn5rhNzoXMFeVTfL/oPL07rf2ze4Ald22tsFxMXY=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\4j6jw32ruj-b7pk76d08c.gz", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint=b7pk76d08c}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "r79n6nskqp", "Integrity": "Z9B/f6Ax/2JeBbNo3F1oaFvmOzRvs3yS0nnykt272Wc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 3213, "LastWriteTime": "2025-06-23T06:00:51.4249153+00:00"}, "dxbXI5Z3SKZScP0Esgei3K6rz2Yj2cN2nXKNEqg/d7k=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\drhhamuxfi-fsbi9cje9m.gz", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint=fsbi9cje9m}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2wjbbjit2u", "Integrity": "crByaO07mpJWuBndE5BbjmmKh3fj/Y0m8CmJDe+c3UQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 12587, "LastWriteTime": "2025-06-23T06:00:51.4289306+00:00"}, "Ji2wO4Ldrwxj+g9Dj0+wHHpEOr4hh9fkLNGsV4W3l2Q=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\n3e2fc27c5-rzd6atqjts.gz", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint=rzd6atqjts}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ukcr8jbv0r", "Integrity": "bhwmNaLC/7dOUfZxpXsnreiNsp2lbllRXrZLXndYFgA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 3367, "LastWriteTime": "2025-06-23T06:00:51.4289306+00:00"}, "ED7QARQKHnkJamhxLARMvBgnH7aP2x7u6YyLfAWY6XM=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\e2xp6ck32p-ee0r1s7dh0.gz", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint=ee0r1s7dh0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bxpwye2e51", "Integrity": "wbodRxtgYaqKj+oSPNLIcKGMPziM41to/lFFylYjPBc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 25833, "LastWriteTime": "2025-06-23T06:00:51.4154548+00:00"}, "LKvFlTOzzaPJdTBilRbMUbj4AzwA865VVBWALjrEHAk=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\0glb43rxjj-dxx9fxp4il.gz", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint=dxx9fxp4il}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cv8bd5rjwi", "Integrity": "qNJnRAEMymFdtmi8gvc2VbVtDDk/UjeSnp+VQ10+cl0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 3246, "LastWriteTime": "2025-06-23T06:00:51.4174618+00:00"}, "4TAbcHKuE05rHFANwoqDFjNTtqW8wpMyEBOvZeLcm6k=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\qbege0tfrh-jd9uben2k1.gz", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint=jd9uben2k1}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "l17cqhtmgf", "Integrity": "V8PtmOQL2VKj+/TynmuZNdDDyU9qG1QJ7FFnxVcN6Y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 15054, "LastWriteTime": "2025-06-23T06:00:51.4249153+00:00"}, "lHMwEFPpxW8E6n0px8Ur8BwiVDFEVDAXgWjmTV1RzdE=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\17n5vvvhyw-khv3u5hwcm.gz", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint=khv3u5hwcm}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bub42mguo1", "Integrity": "8+5oxr92QYcYeV3zAk8RS4XmZo6Y5i3ZmbiJXj9KVQo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 11991, "LastWriteTime": "2025-06-23T06:00:51.4269236+00:00"}, "8CXbPTreiQKIqpCMY7n32h10SQJ4Ptdg6kvr13Jt0xo=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\nx75zsy2d3-r4e9w2rdcm.gz", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint=r4e9w2rdcm}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mns2a2zywm", "Integrity": "kj9zDkFgjDpUKwWasvv6a42LMVfqKjl/Ji5Z5LCNoRE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 44123, "LastWriteTime": "2025-06-23T06:00:51.3579977+00:00"}, "vsrONsnPNJ9JpEapb187ARe2o6IvFfcqZkBtDAt4NCY=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\jb9z4ej1p7-lcd1t2u6c8.gz", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint=lcd1t2u6c8}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iqwesyid6h", "Integrity": "xp5LPZ0vlqmxQrG+KjPm7ijhhJ+gD7VeH35lOUwBTWM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 11063, "LastWriteTime": "2025-06-23T06:00:51.3517144+00:00"}, "ua40J18/NKOsHFC2GjiKE+wY9uid2E7DlTmYrziaQH8=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\zuwccb0bud-c2oey78nd0.gz", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint=c2oey78nd0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "q71g5sacw1", "Integrity": "Jh/LtOdwAMNcT5vpbDEGsxe6Xv18LR1JqK4h/+hvs5g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 24341, "LastWriteTime": "2025-06-23T06:00:51.3687634+00:00"}, "eIa5qa3lvhZGz6d1exluJTGHoLkS/ItNQ0XQHs8+bJY=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\u4x2ymkaf1-tdbxkamptv.gz", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint=tdbxkamptv}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "c9nucnnawf", "Integrity": "QAnDcxiLhrclwEVeKtd/GREdZNbXO2rZP5agorcS5EM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 11933, "LastWriteTime": "2025-06-23T06:00:51.4329453+00:00"}, "8zLMw0jCg3jMeFkIId6Wl8qZ6D9zV3lO2IluALbWdnw=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\rq51h9zu4u-j5mq2jizvt.gz", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint=j5mq2jizvt}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nd4sjw69va", "Integrity": "KxjxxNhsaUlb9m0XwKNiMkNh6OuNbjdGXY0bmR5CTyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 44095, "LastWriteTime": "2025-06-23T06:00:51.3667563+00:00"}, "oD25mJWPq5r+om/HqXX0pHv2bgaNCVoRLyc6p9XOr7c=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\s1an1alosy-06098lyss8.gz", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint=06098lyss8}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1dromj56xs", "Integrity": "hXLxKxNQS6hkMWOc1Po5uxTK8ovzNg0xvRC9wMKOZiM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 11046, "LastWriteTime": "2025-06-23T06:00:51.3707711+00:00"}, "YEWoNhSHKb5gdNqUtTj74wKUIAV7lvAzGRljU5Qn5NI=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\mc608celym-nvvlpmu67g.gz", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint=nvvlpmu67g}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oaf6lwhfh1", "Integrity": "8BfOknNd4oMU2u3DUY0C/Sjhoh3NGtdqE8kxApMdM2w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 24293, "LastWriteTime": "2025-06-23T06:00:51.3798001+00:00"}, "ZbocjrTMieWWSRaP0VHVmfrY/k+EieHcQHkiSqBW/xo=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\4rsjzjn3pv-s35ty4nyc5.gz", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint=s35ty4nyc5}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jw1qs72mo9", "Integrity": "I0QuKxdK89NxyamT6EeIfl/MyifdDw+D8cUjkiXwoOU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 33251, "LastWriteTime": "2025-06-23T06:00:51.391266+00:00"}, "GS+ZnXvaW3H1jASiesjFiTM2G4F+DFTOIUc7ZybfbhU=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\48zxvtlmkd-pj5nd1wqec.gz", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint=pj5nd1wqec}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1jikaxfu3u", "Integrity": "M4d5aODk+LnhCUggc/Xb6RX+Jh4E7X4KN58JXJR757I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 115009, "LastWriteTime": "2025-06-23T06:00:51.4329453+00:00"}, "ZAS/VwBDE2dtoi1LiWI9GPCQ5fK6vCPcc+QHmHuDEJc=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\sep0zbrkwk-46ein0sx1k.gz", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint=46ein0sx1k}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9buzyrzsnb", "Integrity": "NWIxwejcHtJ5yvljTypwFQBimL4GY/TpmkwWCoiPk+o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 30963, "LastWriteTime": "2025-06-23T06:00:51.4401973+00:00"}, "Pc0KJv/NUIGo7PI0HixA321QTFg8LDqavirrPUDqf5A=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\wufgq07dkb-v0zj4ognzu.gz", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint=v0zj4ognzu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "we7ylowkap", "Integrity": "+BDBQp6fX0jhehydJj3yEmXwPsq4ccmpRwJadVX8HUA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 91807, "LastWriteTime": "2025-06-23T06:00:51.4369593+00:00"}, "W1IefpYJHlbz+JiAxlz+zAGcAxby4KpCai/lZ/OcGpg=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\ud3udwn460-37tfw0ft22.gz", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint=37tfw0ft22}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qhts6dsckw", "Integrity": "Tl7d+IXzMoFjiGRivA39XpNjGWA6jMfITy87ywcah6c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 33101, "LastWriteTime": "2025-06-23T06:00:51.445024+00:00"}, "0IDPmZ8K2MQ+a5SXz7f4JrcKVWazHrtO16Orjbs5L3w=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\i4ycg8hsoy-hrwsygsryq.gz", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint=hrwsygsryq}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hack831yxc", "Integrity": "xwBA3wRtW8i96gsexkmrLvL85Ad0ueCN6i7I23oFCMU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 114953, "LastWriteTime": "2025-06-23T06:00:51.4641871+00:00"}, "ypIbCBcfsBvmO4Clvbx6xtT1LEunke/8Td1020OiLDM=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\mrho5uk1dg-pk9g2wxc8p.gz", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint=pk9g2wxc8p}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ih1ajc97pa", "Integrity": "Bhl6D0ngVAgx68NwXp2DEDO390PSrA5dlFHCQXY4WgM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 30986, "LastWriteTime": "2025-06-23T06:00:51.4708142+00:00"}, "f3ZXeabtsqzV1JW/JnOqr/xkiBGaqa78eHX3e8AntYM=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\ztz9b8zmne-ft3s53vfgj.gz", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint=ft3s53vfgj}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j9d5qn8h15", "Integrity": "mVddgYoZfee39UGvBjujrPfkX4g9o5fJQgtcRjDKhDc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 91702, "LastWriteTime": "2025-06-23T06:00:51.4735664+00:00"}, "twOjZ0X8X+jOEaIJ/jR6Sykl9ynHOZIvlnMacp5JFSk=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\qoozf79qw1-iww9xi2hp3.gz", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint=iww9xi2hp3}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2pk93qhe2f", "Integrity": "BuwN80tsWOUYLzwDZSOHdjVKHZ4MqK5VJvaYU4KcWyQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 44357, "LastWriteTime": "2025-06-23T06:00:51.4903645+00:00"}, "Dfgzr0Ixu1qwGS+mgyhbrh06vd+U8kHeiQB9dBhwOo8=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\blxozgl06o-4rfh5s50bz.gz", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint=4rfh5s50bz}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qxhytaovj3", "Integrity": "TnQcMJtyBE0nqk2w83V64rOd7c45DGXQ7uMM7F6CbuM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 92054, "LastWriteTime": "2025-06-23T06:00:51.5059424+00:00"}, "d3ub2ETiOwMSjvHd8OQB6vWJcllbKOrnCSXH2BNnY9A=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\620b52je60-493y06b0oq.gz", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint=493y06b0oq}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9t6fi9687k", "Integrity": "PUb7rj1jLHgIo7Hwm3lvukBcGKDry7n7W2fa1xrz+zY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 23984, "LastWriteTime": "2025-06-23T06:00:51.506981+00:00"}, "kEGXeSjL5OYTdJFKvho0vyOm0TuBmlFt/v8oulSWYxw=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\nrbdr8ikve-kqgha8t6s6.gz", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint=kqgha8t6s6}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9zdc5v4tgv", "Integrity": "O62XQM94YKj6aq0wPmNKY4cvyDgfiuyecaRG6z7nshI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 86959, "LastWriteTime": "2025-06-23T06:00:51.5185138+00:00"}, "e6yzTmGD4PWTJpXxSWgk0i1+hm//tAxx9Ti4bCCrHBc=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\o4bwk4zsoe-pg7rvgc6ra.gz", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint=pg7rvgc6ra}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "u6y7f2prh4", "Integrity": "KlC6+wL8fPPSPiBJGP2ph116hYZzE/EOuD4BPT/M56M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 28855, "LastWriteTime": "2025-06-23T06:00:51.5222491+00:00"}, "gcgrPWE4oLw+u8Uilt//djwKCynXyC0OpAqXg1PBkgM=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\jjcy7qpafr-7g9sx6x8f9.gz", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint=7g9sx6x8f9}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j67i0hklkp", "Integrity": "JacdvFWXBzzaa/De1eA7ZbqI+tuPXLT3SIvf074usm0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 64127, "LastWriteTime": "2025-06-23T06:00:51.533073+00:00"}, "NbmvFwiwuEXjnDK6tXewTeF6x6KFbgi3SIFVn94SezQ=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\vpeqkkohp4-jj8uyg4cgr.gz", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint=jj8uyg4cgr}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "erkthljg5u", "Integrity": "WmNnoBgejwchZUYVA3o03QOAGOuMRS778DeXvhP6suo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 18635, "LastWriteTime": "2025-06-23T06:00:51.3735805+00:00"}, "mNVUmpEjfbRmuvaDmIbq7kwOIKIwFi0X/i/DAW5vjDs=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\xebldw98hj-yxdfmmpmcr.gz", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint=yxdfmmpmcr}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3h28mcium1", "Integrity": "eRuiifQ5nSKj+pNzPiENis6cn/ixRmSwRPd8v/dnn40=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 56671, "LastWriteTime": "2025-06-23T06:00:51.3597418+00:00"}, "AonwFlzOnJe2/FaqX5Pu/N6wXf7lTtuEj/rS2hJO3F0=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\e80p743m1k-43bjlk9u9j.gz", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint=43bjlk9u9j}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "326co4pqop", "Integrity": "q2HBqdGeLL416FK49pM3qNmrggcVfEbAzAqWfAD+Z/E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 29571, "LastWriteTime": "2025-06-23T06:00:51.3647483+00:00"}, "EwEeXLgyA7z8MxiJiiNXnjteltBKlAbN6Ef6w/39pbI=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\d7ilm089y9-xl1csbwgsv.gz", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint=xl1csbwgsv}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ignlds132n", "Integrity": "Alfed5re6+FIFxhMHNyWvRylJ7m89IejBb+3Wclhi6E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 64429, "LastWriteTime": "2025-06-23T06:00:51.3818074+00:00"}, "SBiGxug5JrtUnv24OBu/SRhvgZNBBxX0i8b+gGAXJZE=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\2l892qe3oo-63fj8s7r0e.gz", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint=63fj8s7r0e}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "on09t9pmch", "Integrity": "bIPgOT88ycSklHWxEzFQVPvNsgNbss3QQbyTqHho4PA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 16636, "LastWriteTime": "2025-06-23T06:00:51.3818074+00:00"}, "idHRG5Wx6bRWMN4NDLFvfVNUt4zSUFeP8hi8Z2D7Y8k=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\d671g2e4t6-hvy20onkgh.gz", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint=hvy20onkgh}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rq<PERSON><PERSON><PERSON>", "Integrity": "Ux8iVTMLvlF/p7oaSFGzpS1UkehGXhp6SB45aKrxvX0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 55851, "LastWriteTime": "2025-06-23T06:00:51.395283+00:00"}, "I7/x6rJO6tsjU7Qk+JDed/8UzbbrL8H+F13mnJ+r3Gg=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\q9qu3cm8pu-iag0ou56lh.gz", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "sample-data/weather#[.{fingerprint=iag0ou56lh}]?.json.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\wwwroot\\sample-data\\weather.json", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4cbitfuojg", "Integrity": "HD3vAUwurZXW96vdgG5RVLhMjmVeSgBs4iKLSw2+Uwk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\wwwroot\\sample-data\\weather.json", "FileLength": 153, "LastWriteTime": "2025-06-23T06:00:51.3838152+00:00"}, "qjOFuuFC9bBY8ciKsbL/G0j53xxZ/DCLh/8OVvDOkwU=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\822nxsd125-md9yvkcqlf.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/blazor.webassembly.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\blazor.webassembly.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k1uijd3xue", "Integrity": "aODHHN99ZO6xBdRfQYKCKDEdMXpBUI2D7x+JOLJt8MQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\blazor.webassembly.js", "FileLength": 18128, "LastWriteTime": "2025-06-23T06:00:51.3882503+00:00"}, "jLp1LYOEcwVEIjQ6+UoiuguBeTqoy1Qz6ZPbVq0oI/s=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\kz1w4pi8zk-tki07pftty.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "Baytara.Client#[.{fingerprint=tki07pftty}]?.styles.css.gz", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\scopedcss\\bundle\\Baytara.Client.styles.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9u6cbnf9yp", "Integrity": "X3tAZCru3ZXEgDBVx5T+ralRDclYylFQwPeoffx+qIs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\scopedcss\\bundle\\Baytara.Client.styles.css", "FileLength": 1398, "LastWriteTime": "2025-06-23T06:00:51.391266+00:00"}, "ap4TxBB6Jxb+2rc04Hum4b1d5zYm9BXERKRREW1nTrE=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\wtdtswkgzr-tki07pftty.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "Baytara.Client#[.{fingerprint=tki07pftty}]!.bundle.scp.css.gz", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\Baytara.Client.bundle.scp.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9u6cbnf9yp", "Integrity": "X3tAZCru3ZXEgDBVx5T+ralRDclYylFQwPeoffx+qIs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\Baytara.Client.bundle.scp.css", "FileLength": 1398, "LastWriteTime": "2025-06-23T06:00:51.395283+00:00"}, "BtgFwW4c34GQKwjjIfdU/xVpqaUaEjS057vHRILtC2s=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\c7ik5zb56r-phaa9r44xv.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Authorization#[.{fingerprint=phaa9r44xv}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Authorization.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "h1wbebuwn2", "Integrity": "C88wh6eEqbi8Hoc4PKhx8UNyomO12poDTt/rDLYEYJQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Authorization.wasm", "FileLength": 18067, "LastWriteTime": "2025-06-23T06:00:51.3992971+00:00"}, "Hcbxl33PbMSzfNw3kwHqSdnJC9Pm8urYblCUCe0zyAc=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\t25r49r91d-4o2vz6uw5j.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Components#[.{fingerprint=4o2vz6uw5j}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4y0l43e5dt", "Integrity": "9Hev8/0eZcmh+of1wNG8/+TTG0MFt51/61jxeiQnYxw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.wasm", "FileLength": 135110, "LastWriteTime": "2025-06-23T06:00:51.4134478+00:00"}, "yYBT7G7k7IIJgLmtHJArvDWer7Sbh4nex+w/f/7u6j0=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\jsywlqlqvt-e4o7p51zuj.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Components.Authorization#[.{fingerprint=e4o7p51zuj}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.Authorization.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "28y0gfrmqp", "Integrity": "QomIhvl9p58+Ega72idNcl9HxlxOPIQEoYqj/mHcF7E=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.Authorization.wasm", "FileLength": 10078, "LastWriteTime": "2025-06-23T06:00:51.4154548+00:00"}, "UUynjsPgjhkoj6DPcPjWrdw1shsnO39hfcT89w0uhoQ=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\9l6aj725io-1ddspp16i2.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Components.Forms#[.{fingerprint=1ddspp16i2}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.Forms.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0gn2vv6660", "Integrity": "MB6sOe2NWpkRiDSZzxAVoMX/X6NV1/eMJOS66g5Q8Vs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.Forms.wasm", "FileLength": 16717, "LastWriteTime": "2025-06-23T06:00:51.4174618+00:00"}, "EnzBs2LT0hJhhod8eDYg3nBpEN6IU+hOLGgQt/IkJLs=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\px7xpcse41-wjexe30cog.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Components.Web#[.{fingerprint=wjexe30cog}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.Web.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pgta27qa2t", "Integrity": "qZEhkf9TdTZW+klN9B+tuKckh1JvV2CZfAiGQxjg3/8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.Web.wasm", "FileLength": 72582, "LastWriteTime": "2025-06-23T06:00:51.4134478+00:00"}, "E9czMEGxOQX+Wl8c09exwiyfL1x33hXT8NDVOJvDYoA=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\e473lqm2zu-2yt2k81j3x.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Components.WebAssembly#[.{fingerprint=2yt2k81j3x}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.WebAssembly.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "w0mar6tbe4", "Integrity": "AA8FXYqBKD+AeJHcD6nsSs9niqApfhrdTTJMxYziv7k=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.WebAssembly.wasm", "FileLength": 67505, "LastWriteTime": "2025-06-23T06:00:51.4212125+00:00"}, "3yBNdGwcOY4krKroywIlmPBZ/cO2X12jFfkThS0jzNg=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\qimwvi97bq-hlbn62k9y7.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Components.WebAssembly.Authentication#[.{fingerprint=hlbn62k9y7}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.WebAssembly.Authentication.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ypxa7dohkx", "Integrity": "yrv2acjR5my1zkMiCoYOd/nD32ITNcL8NQq/2BUdMeg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.WebAssembly.Authentication.wasm", "FileLength": 39143, "LastWriteTime": "2025-06-23T06:00:51.4269236+00:00"}, "9nvsfw09qKG4OKT+oZn5t4oqZ7i90dTLHA6Ru0RmFfU=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\0ivkd7hswk-4eagaotj1c.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Metadata#[.{fingerprint=4eagaotj1c}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Metadata.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "r4wlhab6a5", "Integrity": "6952h5QnELAnl07RYQ5YcHd3bmur48JI0qD03I89Iv4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Metadata.wasm", "FileLength": 2425, "LastWriteTime": "2025-06-23T06:00:51.4289306+00:00"}, "dW2cQoHpej7Gl2X/vM2rNqzfe2NZE0FwIXURhcAMP0g=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\4mwk0gdzmo-a20cmtwj3w.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Configuration#[.{fingerprint=a20cmtwj3w}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xn1jd8qxqc", "Integrity": "00P47iSCyJDIt/4Q/ncEuYfTma+v/DXQfcVMtOYdy7U=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.wasm", "FileLength": 15913, "LastWriteTime": "2025-06-23T06:00:51.4289306+00:00"}, "qv31vOiWFFVOmkPowNeCnppl52Nbv7JWMbqc4FC94sY=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\02wz4v1k9d-jdjwdbrxb5.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Configuration.Abstractions#[.{fingerprint=jdjwdbrxb5}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.Abstractions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "y1skf6eqlh", "Integrity": "+o0SlaGN3hTshQrxRJg4gpDLcOm8yCqtvFUtEKt14VE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.Abstractions.wasm", "FileLength": 8462, "LastWriteTime": "2025-06-23T06:00:51.4309376+00:00"}, "NlQ9APmKWKnNUBsl6MakQdVIoB5rktlRc+Lf/Ij54tI=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\0pj57sw62y-y7ybdi8i13.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Configuration.Binder#[.{fingerprint=y7ybdi8i13}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.Binder.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "l67n2vaw3g", "Integrity": "473QSC4QGff/9qmMp6ZYUUo1aOQ2m37xzR9E3A9VxWs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.Binder.wasm", "FileLength": 14894, "LastWriteTime": "2025-06-23T06:00:51.4329453+00:00"}, "gU2najVYHmpVb+V5nuAGmM6OK7kH4JbAu0EiGDhQugo=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\hajxju3w56-6zj77w12m9.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Configuration.FileExtensions#[.{fingerprint=6zj77w12m9}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.FileExtensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "i0s2268p4w", "Integrity": "UtSFma7fZr8jBKW9dSWVRUlV2meJputzxAk6Z/T38/M=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.FileExtensions.wasm", "FileLength": 8399, "LastWriteTime": "2025-06-23T06:00:51.3517144+00:00"}, "Yo+BLKcyunHmx+CHlYVU1OObUmcaE+DvkDINZYJ3xYg=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\q7wkfgcy3s-rzh7ctjkaz.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Configuration.Json#[.{fingerprint=rzh7ctjkaz}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.Json.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sptztho42y", "Integrity": "0N3N00Tn83K8Oz80rQAS7KB9FAf98Swlt+wjujKkB9o=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.Json.wasm", "FileLength": 8205, "LastWriteTime": "2025-06-23T06:00:51.3537223+00:00"}, "LDgGzs65Wq+FrPgsbX5Jjc3f8dQmydqUXdju8vcQrNY=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\cfr88ro2af-tlmqx4gkln.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.DependencyInjection#[.{fingerprint=tlmqx4gkln}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.DependencyInjection.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "zc0d7aia9q", "Integrity": "7U1a0MfPd/PmjfgVLxZ1ocGNsMOKIIBWFaREtGFd24k=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.DependencyInjection.wasm", "FileLength": 36323, "LastWriteTime": "2025-06-23T06:00:51.354919+00:00"}, "x3cmWiSgQ/dOaLbL+KOtdeqlL5tJPXOkZGCzwXnXnGE=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\ivbghkyzlz-lcrc3gl2ab.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.DependencyInjection.Abstractions#[.{fingerprint=lcrc3gl2ab}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.DependencyInjection.Abstractions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qm4hkrzgij", "Integrity": "skE9zViT62EMSHppqKIfhF8Occ2aB9yF6wSoNFNXCkc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.DependencyInjection.Abstractions.wasm", "FileLength": 21988, "LastWriteTime": "2025-06-23T06:00:51.3597418+00:00"}, "LlcxpyGQDVswPtxM0nL0Y8RtRVJ8tMWSU7EJO8cR78M=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\h2lwp66dso-w4n6sx9nop.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.FileProviders.Abstractions#[.{fingerprint=w4n6sx9nop}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.FileProviders.Abstractions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8ys1p7g9rv", "Integrity": "0L00QJp2yU1zol4bh/DkeIMNJ90u11L1BS2Sbh9AFrA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.FileProviders.Abstractions.wasm", "FileLength": 5721, "LastWriteTime": "2025-06-23T06:00:51.3569259+00:00"}, "ITppAKnl8UZTgcHx2MZeQ3etvW26sMe3UXMq9bz/oZw=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\gd2y8h43kg-ily916jl2z.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.FileProviders.Physical#[.{fingerprint=ily916jl2z}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.FileProviders.Physical.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "i6hgjj9558", "Integrity": "BlZPfbzFU3HnDx0NNnxpBFh0mnt3e1ExVc6kpcaktF8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.FileProviders.Physical.wasm", "FileLength": 17352, "LastWriteTime": "2025-06-23T06:00:51.3597418+00:00"}, "UlyJFoucmckMBaOXVTlltQs8zSe8pyCLk1s+V8U+Kgs=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\nv8mj92s2h-sdsdr06lyk.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.FileSystemGlobbing#[.{fingerprint=sdsdr06lyk}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.FileSystemGlobbing.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1bx4nliwij", "Integrity": "Y3OKv/EdzFKbAgS+P37p6INWYQiG55ulhvi9NGDi20M=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.FileSystemGlobbing.wasm", "FileLength": 16770, "LastWriteTime": "2025-06-23T06:00:51.3607285+00:00"}, "i3/1C4LMbWlmmF81vYojNCO5AADyQrCtCERqBvvOQ5o=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\fb2g6j1383-tgyhlz8gnr.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Logging#[.{fingerprint=tgyhlz8gnr}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Logging.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rumzc3hdrf", "Integrity": "cyZN9giYMtRBQ8LjXh/3uDCeD25D2MWtIfdCQ0IKIjI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Logging.wasm", "FileLength": 19451, "LastWriteTime": "2025-06-23T06:00:51.3647483+00:00"}, "E+HHSH/m9+MXxd2G6bqy6wOMUzdZbKGfNEm4WNM2Eis=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\97qa01rzoz-7bglk34tl5.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Logging.Abstractions#[.{fingerprint=7bglk34tl5}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Logging.Abstractions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sd9qjilbhf", "Integrity": "AMMAQlSi0FW+DfVMmShYCW1pConz1W37wB/WKDEyCfU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Logging.Abstractions.wasm", "FileLength": 25074, "LastWriteTime": "2025-06-23T06:00:51.3667563+00:00"}, "jCTtIvZiACSQ/ac92kB3v9IzdGWaPo4FSHrTwViPJhQ=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\089p7jo6sh-bwt6p2r0a3.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Options#[.{fingerprint=bwt6p2r0a3}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Options.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ab9ehcnd8b", "Integrity": "pjdlorHCUQQVRU5TsDiBE+5VjAXF3W6pu6syZd8SlB4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Options.wasm", "FileLength": 24178, "LastWriteTime": "2025-06-23T06:00:51.3757848+00:00"}, "M89KssnjRJr0NOF4akwoReoSn52OwMeU4O3bfsviiJo=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\xiz7uvkaot-vutb1mf5cz.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Primitives#[.{fingerprint=vutb1mf5cz}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "y3i8028kvp", "Integrity": "rtWDssgo0h07fkdnHyitJSHSM4QvZ6WGBfpAfBtx23E=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Primitives.wasm", "FileLength": 15640, "LastWriteTime": "2025-06-23T06:00:51.3627403+00:00"}, "9jhfDdLqF/UtNJfTe17I3kOqqWhU3BtnxEYX3s8Us/Q=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\hikvg494gs-fzkuir7tme.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.JSInterop#[.{fingerprint=fzkuir7tme}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.JSInterop.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "shgbnygtw2", "Integrity": "wFhaikCkYvD54rlnM4bj+s4fK2hfI9vREVLBaCovhwg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.JSInterop.wasm", "FileLength": 24108, "LastWriteTime": "2025-06-23T06:00:51.3687634+00:00"}, "4uLd6p4H3qSVyVueI3YV8P5blimF3ePuxiCbCKlrwqQ=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\9279xo82tl-btoflm7i7s.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.JSInterop.WebAssembly#[.{fingerprint=btoflm7i7s}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.JSInterop.WebAssembly.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ctuqs418nf", "Integrity": "gKUAuG3xlQKyCwpryhYkC3t4qcLVg/y1yZmlgPwcFmQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.JSInterop.WebAssembly.wasm", "FileLength": 5801, "LastWriteTime": "2025-06-23T06:00:51.3735805+00:00"}, "AW/jdcmDIYWhG1ITXHMwb141jA+TUZ6E6KSRgUrGFc0=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\mfe83pckm6-9gws8s7zmg.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.CSharp#[.{fingerprint=9gws8s7zmg}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.CSharp.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "v6tjj<PERSON>hub", "Integrity": "h3FbgFwrL3kXanvRLYavpbgXWAIothfDB/V7dQ708cQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.CSharp.wasm", "FileLength": 132476, "LastWriteTime": "2025-06-23T06:00:51.4013041+00:00"}, "YmDrM/TgtBzOQtba4fEL9KSZuIT7972kMfeQWw7maJA=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\pwo1h2ec3y-hev5t09xbg.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.VisualBasic.Core#[.{fingerprint=hev5t09xbg}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.VisualBasic.Core.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yy0g3heyum", "Integrity": "6o/KKZQCVvhYYDVFI63J5No2TOWU9mfc/BT/1DkDMNo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.VisualBasic.Core.wasm", "FileLength": 171163, "LastWriteTime": "2025-06-23T06:00:51.3902612+00:00"}, "VizBzXWclzgdqAv717Lv/TmdbrN2riWNbnGaQYoLhqU=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\8c77pede38-wy3cb00pkv.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.VisualBasic#[.{fingerprint=wy3cb00pkv}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.VisualBasic.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "t36syn8yrj", "Integrity": "F9QLZFW+PvDJ1nw7qQRupF8/mnBuTqIMqLkJN6pCUi4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.VisualBasic.wasm", "FileLength": 2878, "LastWriteTime": "2025-06-23T06:00:51.395283+00:00"}, "8ynlRjMUVhs4XJ8Q8Os9KSglbF1foOQYjHgHAbmFpEc=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\583es7sec5-nt18748s0w.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Win32.Primitives#[.{fingerprint=nt18748s0w}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Win32.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "x3ckyz3fhx", "Integrity": "s8cdHG+gfT+jcGbRXZUZR28c0xY3HLxvVedg7mFAjAU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Win32.Primitives.wasm", "FileLength": 2195, "LastWriteTime": "2025-06-23T06:00:51.3972902+00:00"}, "3itLM3Y5DGw0/6mfeXgGBLBWoPSNOz735VvdtojIA2Q=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\8mrwtrotlz-ykr6iyjchr.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Win32.Registry#[.{fingerprint=ykr6iyjchr}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Win32.Registry.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xr4e51ylan", "Integrity": "U8e5qCG3mnHvdH4zePBI3tcVZ0MIasz3TwrgNRarMkA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Win32.Registry.wasm", "FileLength": 9275, "LastWriteTime": "2025-06-23T06:00:51.3992971+00:00"}, "UIDZ3fbuM5kJ6SZT4EpEs4rXN01SLwTY2pcZ+aU9VZM=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\blpmfjhzrs-3h1likbfvx.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.AppContext#[.{fingerprint=3h1likbfvx}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.AppContext.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "a5e7izvoan", "Integrity": "og7qwPp31QK8/RTaUZH2+g4QF+BNvuIVHRnFyOYxw/Y=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.AppContext.wasm", "FileLength": 2094, "LastWriteTime": "2025-06-23T06:00:51.4013041+00:00"}, "qOd6OI5ZfG0namjO2g/nZpsEEHzESwR7GjPhv0yBfrY=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\0g898iif7w-wt7n1r1ovk.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Buffers#[.{fingerprint=wt7n1r1ovk}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Buffers.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "r6f7t7ehnf", "Integrity": "Xd4QgqSMM3//nwkXmCGJzTaBydvvNvIf92C6lrRTcjk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Buffers.wasm", "FileLength": 2104, "LastWriteTime": "2025-06-23T06:00:51.407423+00:00"}, "JKtNff9rOSfpkZdFB2ws/KGsSQixfnbYUAWPt3XhHW8=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\047tn4wl9m-65adg6natn.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Collections.Concurrent#[.{fingerprint=65adg6natn}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Collections.Concurrent.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "tzhmjt3k84", "Integrity": "mbu9hjSCtj0PoETpc+agPXIKSX8j42w6h0jVbZza22Y=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Collections.Concurrent.wasm", "FileLength": 34476, "LastWriteTime": "2025-06-23T06:00:51.4134478+00:00"}, "gWkMhDdngUr7p/uN4eNp2mAmJFipssndrwH1sXn+fVE=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\kk45q10za1-dufaq3kp3z.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Collections.Immutable#[.{fingerprint=dufaq3kp3z}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Collections.Immutable.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "51bml0oz3e", "Integrity": "WVaR2xDPST4CgXO3OdT5xeMc5uiAlsH+wVtAhPRqNFE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Collections.Immutable.wasm", "FileLength": 100293, "LastWriteTime": "2025-06-23T06:00:51.3607285+00:00"}, "3sZNcua0P1SlwfsI7GrSzChvle2WWAYc0jdSV4yLLy4=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\khhd7mic44-rxjrzzpp9g.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Collections.NonGeneric#[.{fingerprint=rxjrzzpp9g}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Collections.NonGeneric.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fjmcmjmsnx", "Integrity": "5aAzNQ8ZDjSSAAu9t4e6ds2UfLDe2Vvkxs2KKvrX1PQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Collections.NonGeneric.wasm", "FileLength": 14907, "LastWriteTime": "2025-06-23T06:00:51.3647483+00:00"}, "Ij6ruZrKsmRRxt3DGima9gIZFddmg2ikldObmXO37yg=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\n4nvcw40ck-grj2h3kseq.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Collections.Specialized#[.{fingerprint=grj2h3kseq}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Collections.Specialized.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ngshtamhf5", "Integrity": "qRChNtZxN7W55HtfMsIBBjpapXjReOHsiNrK9hSsUcA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Collections.Specialized.wasm", "FileLength": 16543, "LastWriteTime": "2025-06-23T06:00:51.3647483+00:00"}, "cMn6O0839rFgwkc2CDNbGpJgK0e6eYYbPR+MZdIgt7k=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\43j3ac0dwp-cip8dbnu43.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Collections#[.{fingerprint=cip8dbnu43}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Collections.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2qjzwtvdih", "Integrity": "mjL0X9XwHa06uSx1d+NkRqRjnkVijmpN4XYBqgv5rjQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Collections.wasm", "FileLength": 49322, "LastWriteTime": "2025-06-23T06:00:51.3715728+00:00"}, "gQQ/vnJNf4QLB6nNV0qXJ4sCCARNC0iTIIIwn5/0OQ0=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\xeg4897h0y-6hr3q9fx89.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ComponentModel.Annotations#[.{fingerprint=6hr3q9fx89}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ComponentModel.Annotations.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qng43wbgo3", "Integrity": "qr89TS1VK57/yWuwEqPd3AhDawr335Eqy/7PdT+PuRs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ComponentModel.Annotations.wasm", "FileLength": 36234, "LastWriteTime": "2025-06-23T06:00:51.407423+00:00"}, "mAU7zrOBfy/vkLPDWCm7PsFG23s7+voR0MJEbmGFax4=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\6xfibseaa1-k6p4pn9w0l.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ComponentModel.DataAnnotations#[.{fingerprint=k6p4pn9w0l}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ComponentModel.DataAnnotations.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ojz6ujm605", "Integrity": "dOOEfjsQYKMiMojuJhNWY5AgtLsogJzmlp4SGxPRDp8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ComponentModel.DataAnnotations.wasm", "FileLength": 2568, "LastWriteTime": "2025-06-23T06:00:51.4289306+00:00"}, "1Xb6VhFAxHQDRV8PNDWgG1U+KuxjS7mkc9UXXEfB/Ew=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\gjvo7ls599-p61cj2koso.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ComponentModel.EventBasedAsync#[.{fingerprint=p61cj2koso}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ComponentModel.EventBasedAsync.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k2j2ygq395", "Integrity": "DM9SmylVIa0ABDd6N4cURrMyw35GmGLeoZa4x3LaoFA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ComponentModel.EventBasedAsync.wasm", "FileLength": 6876, "LastWriteTime": "2025-06-23T06:00:51.3777927+00:00"}, "tML3s3ambg2p6o/fIdMnhzGlMX+zt5CExLLuhel8Alk=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\a3rnb2mvqs-fea7hw9xtf.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ComponentModel.Primitives#[.{fingerprint=fea7hw9xtf}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ComponentModel.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "umzqgkzc1r", "Integrity": "5kY5evyFlu5oD/u6tyDKQGQF6EMFhyJsIkYGrLcOrc0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ComponentModel.Primitives.wasm", "FileLength": 13556, "LastWriteTime": "2025-06-23T06:00:51.3858225+00:00"}, "drT4PMKO87OtSLJ0mAT3u0Xc88ut+OdkznRjiz4NQy0=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\ll1mlmmfeq-etd3dkcep2.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ComponentModel.TypeConverter#[.{fingerprint=etd3dkcep2}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ComponentModel.TypeConverter.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oaeumn0mnw", "Integrity": "IEphPSWR/t+qkYdT9h/iFtb98MrE1npIIoUElqr4/74=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ComponentModel.TypeConverter.wasm", "FileLength": 124639, "LastWriteTime": "2025-06-23T06:00:51.4192053+00:00"}, "LKsiNsU/JhiqE7rExpN0Ert2QuFwFE4H6iq6rjgDqgg=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\cwnbn444sk-0lm42x51au.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ComponentModel#[.{fingerprint=0lm42x51au}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ComponentModel.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9ehupmmhqs", "Integrity": "kSQI0YW7tLwMq3TR8YRUm6WPHgZ5RkBQqSfNmedmzfk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ComponentModel.wasm", "FileLength": 2558, "LastWriteTime": "2025-06-23T06:00:51.4229064+00:00"}, "jrzRICbTpDas6WUiZj4XyTMqdz0+yVLtgSPXp2Cj6lM=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\lvj5n6b6ve-ex6vy58iyk.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Configuration#[.{fingerprint=ex6vy58iyk}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Configuration.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "161caa1cwf", "Integrity": "6VL56SWXDcFwHnrmu4YOmvosUAJxzMfvAKctu9qmQxQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Configuration.wasm", "FileLength": 3125, "LastWriteTime": "2025-06-23T06:00:51.4049116+00:00"}, "Uc30ggP9ZIyEbusMYRS11b1s3kVIATom60Od76zAXPQ=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\uoubio2h9o-s0qgw5psci.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Console#[.{fingerprint=s0qgw5psci}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Console.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rz0hfc8q1n", "Integrity": "ucGbBzPuZQrqYvKyRP7PQUo0z3oFYjiZMOXd2n3s34w=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Console.wasm", "FileLength": 19992, "LastWriteTime": "2025-06-23T06:00:51.4114406+00:00"}, "o85mkRYhhvB+SzvVUOGuL4Zgg4hniFe3yPQZYALeIBg=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\1buw148u2h-zknkrutld3.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Core#[.{fingerprint=zknkrutld3}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Core.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qqei9kh7rf", "Integrity": "ImTTjwMF7dQpY81I7yc8aPn+ZeH1Eo8K5Qf8NxRZKTs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Core.wasm", "FileLength": 4589, "LastWriteTime": "2025-06-23T06:00:51.4154548+00:00"}, "dzLtqRl06sQj02SQQ0OqbyUVZfoqf4oX4HSfAa5/ZPA=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\p9ux0odz6h-lu92ceoi50.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Data.Common#[.{fingerprint=lu92ceoi50}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Data.Common.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qi1rc7r7xc", "Integrity": "crhsAAg9PX4q9OW9EGA3dQo8SUUOISfEtF6jcyfGO7s=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Data.Common.wasm", "FileLength": 378856, "LastWriteTime": "2025-06-23T06:00:51.4641871+00:00"}, "/qFj7ZKESalI3zgFsRlUo6YcR17DOY5L0FQvzz8o/24=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\6wyzm4w8zf-2ddk0zm05l.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Data.DataSetExtensions#[.{fingerprint=2ddk0zm05l}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Data.DataSetExtensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cjoiwnua7r", "Integrity": "SHfEC7ChbrKyyh6PfHr62WC6ycP0oYGXIpPOrOv9J4Y=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Data.DataSetExtensions.wasm", "FileLength": 2056, "LastWriteTime": "2025-06-23T06:00:51.4641871+00:00"}, "qu7Q/axo3iTQo6x1OHN0hdIUyNrv4MRAkEWC6ptMGsk=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\j6k04i6j89-3adg3wr0gn.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Data#[.{fingerprint=3adg3wr0gn}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Data.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "y8tj0c52nf", "Integrity": "0prIMCFH8IgWHw6nctqMm4lcx0MaI7hubWrHw8quhMc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Data.wasm", "FileLength": 5069, "LastWriteTime": "2025-06-23T06:00:51.4641871+00:00"}, "5JsXpt9e9Fj5tGb+P43UXGu+mbltrUJmJY2TI2auSu0=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\n0ttgmg6j2-voyqcmzm7a.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.Contracts#[.{fingerprint=voyqcmzm7a}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.Contracts.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k6w8b89xvf", "Integrity": "7C/uR2dFUhA93iftWCFS629PxEKWZvRt/BZAaMtSF/M=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.Contracts.wasm", "FileLength": 2385, "LastWriteTime": "2025-06-23T06:00:51.4641871+00:00"}, "EFK05tw5CVtNDZU02uiA393oRqr5uGVxyjiVccWzvWw=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\092d8vgdgc-tuw7jnpdtf.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.Debug#[.{fingerprint=tuw7jnpdtf}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.Debug.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rygd58ty2p", "Integrity": "3dNhkuxdSU+EpP7VAaKj6rBakddE8ncWP42q14EcQB0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.Debug.wasm", "FileLength": 2277, "LastWriteTime": "2025-06-23T06:00:51.4641871+00:00"}, "KQyFGRr2IEfiDFd+EyFP7kBxYHR1xCr/6lwaoQQFRPs=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\nxz6o3tjzw-orwvw7tsnw.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.DiagnosticSource#[.{fingerprint=orwvw7tsnw}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.DiagnosticSource.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mhrovf2af9", "Integrity": "KlyLGJDJR+0mx9PwGhLkbgEYbUbG+MEQAwKrqYPMrXw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.DiagnosticSource.wasm", "FileLength": 74360, "LastWriteTime": "2025-06-23T06:00:51.4401973+00:00"}, "TDARLNvWzDqUAIk751+uZt14JadIV83BhZIiDHQjvtg=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\m940o8z8zu-i2nxqnh8ia.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.FileVersionInfo#[.{fingerprint=i2nxqnh8ia}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.FileVersionInfo.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0axe9t43j1", "Integrity": "CCOc1MLJ7eisi3VSu3+oZ3nR98Ye839tReOKUtpeH8c=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.FileVersionInfo.wasm", "FileLength": 5156, "LastWriteTime": "2025-06-23T06:00:51.4416724+00:00"}, "6oaOlIARRC9rrbW52AvbogjEX62WyUjRD+sSO7MrffM=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\znv03lotbq-yj1m2auw1z.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.Process#[.{fingerprint=yj1m2auw1z}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.Process.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "10sf8j26li", "Integrity": "f/AsC9ola8vGwZvfaXCsAmUSfYTg1E8ya/O2sG6DVbI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.Process.wasm", "FileLength": 16541, "LastWriteTime": "2025-06-23T06:00:51.4434167+00:00"}, "9u30KFfG0FFmn+Tt/laF5DW3vXzDFsBr8z4n9hAA8LA=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\5n67cghtjc-9u6hm41m9t.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.StackTrace#[.{fingerprint=9u6hm41m9t}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.StackTrace.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6xgm2t8zkg", "Integrity": "KhWXN8J4lLw4WpkY7Gd1HAal9EKCMKHyS+6vr4Ca9AI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.StackTrace.wasm", "FileLength": 7498, "LastWriteTime": "2025-06-23T06:00:51.3735805+00:00"}, "VQWUcRYL55D3DWF1Zv1Uo28WBFoTio+SiGCBKflKMH8=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\cxsxrp13we-670flx7nki.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.TextWriterTraceListener#[.{fingerprint=670flx7nki}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.TextWriterTraceListener.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "e0tfv27xw9", "Integrity": "YkixRJbYaqyJnUYiAkA0fPAezF2PiukyZIS/3X4vuvc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.TextWriterTraceListener.wasm", "FileLength": 9526, "LastWriteTime": "2025-06-23T06:00:51.3517144+00:00"}, "rn92Dt49VRaQ8aysS0wJ+eX8IkaMqli80a5byjTpSJA=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\znjtbx6a1o-2vqkac8ysr.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.Tools#[.{fingerprint=2vqkac8ysr}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.Tools.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "24y7wsp8ye", "Integrity": "ckjYladl6O5FBWdtYhFRpyUV2ZuYVxfsa6HJusPe74c=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.Tools.wasm", "FileLength": 2173, "LastWriteTime": "2025-06-23T06:00:51.3537223+00:00"}, "XppBnLXDv5OGgJMC8IWUQHJaqq7mrQHw+Pw194Lurrg=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\nbqseuqh9p-n515vmkk2p.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.TraceSource#[.{fingerprint=n515vmkk2p}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.TraceSource.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cz94m8pb8a", "Integrity": "iFHB34NwrXFuyh3wBBf62BiMg1485sCOejBRBCLAt6k=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.TraceSource.wasm", "FileLength": 20409, "LastWriteTime": "2025-06-23T06:00:51.3627403+00:00"}, "kzNU0ZeM79EoEijZ6N1/UHe/fxToH9H29SQJQWaULvM=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\hc59i5rabf-ogliygwa1r.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.Tracing#[.{fingerprint=ogliygwa1r}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.Tracing.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ikt92ild76", "Integrity": "8lc+1jB1s6pXbMzjX/E2qeUZoA032+8OI/H81MVOf4I=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.Tracing.wasm", "FileLength": 2495, "LastWriteTime": "2025-06-23T06:00:51.3647483+00:00"}, "H5DKxtgvXTM1uz6FxVRAiUhOeU7FiM7CtumBsGfeHkc=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\z0yr2iojkc-zk693pwck8.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Drawing.Primitives#[.{fingerprint=zk693pwck8}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Drawing.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Integrity": "p08ECfEknMjloete7JyFbSdA3pXFm1a11VFl2HtFBIo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Drawing.Primitives.wasm", "FileLength": 24538, "LastWriteTime": "2025-06-23T06:00:51.3687634+00:00"}, "cmTS27oy7F+uRhp9IBd0QXneidCPyTJ3ZhAnW6seTBQ=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\2d1rzsvr2y-wxhr0xa5hb.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Drawing#[.{fingerprint=wxhr0xa5hb}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Drawing.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hzcngcw3oq", "Integrity": "g96YTrZ+6Tvlx6oGIVXG+mjQMMsXm5kmT9zhNoZSjk8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Drawing.wasm", "FileLength": 3881, "LastWriteTime": "2025-06-23T06:00:51.3777927+00:00"}, "+TTobrln5RDfN0POZwW8LIXrAYiyGmGSn+IiwMS90N0=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\7pfz9zkwta-ipprcrczgj.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Dynamic.Runtime#[.{fingerprint=ipprcrczgj}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Dynamic.Runtime.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "trdcu1r7x0", "Integrity": "ucIPC6pR96gFThOIsvjCWMDCesYit5akdK4GFqMP2Pk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Dynamic.Runtime.wasm", "FileLength": 2437, "LastWriteTime": "2025-06-23T06:00:51.3798001+00:00"}, "ftQcKfrgOTjCj3Xc3QxYk9/96CDlcqIm19tMzmx/9xs=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\i1ryyyc2b9-okhe897m5z.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Formats.Asn1#[.{fingerprint=okhe897m5z}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Formats.Asn1.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9ahg0qs4kw", "Integrity": "gSPTp+vFVWYkUKKF2mJMgYpjKxEeUX4Ccu+OlD780qA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Formats.Asn1.wasm", "FileLength": 35942, "LastWriteTime": "2025-06-23T06:00:51.3818074+00:00"}, "1u1rTXPyeRfh4yfBJPNPK/KJM+breaKw7zsu9n3hpMI=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\mcgh5g5292-i93u5bq4fn.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Formats.Tar#[.{fingerprint=i93u5bq4fn}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Formats.Tar.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0xmwxg04yc", "Integrity": "/MIly763ny7eEQnXo4Cy4h4MjC28HFhSy6Fwhi0ONrU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Formats.Tar.wasm", "FileLength": 10561, "LastWriteTime": "2025-06-23T06:00:51.3838152+00:00"}, "F/OVX3KF8ASVG1wL4uE82dQI7iWs0hyalyf+AxNaXi0=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\l4x95g7985-x0sb683rhi.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Globalization.Calendars#[.{fingerprint=x0sb683rhi}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Globalization.Calendars.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "b09pqnjr7p", "Integrity": "2/96J+UH0M8S5w5imkG5bdb4gYCddY8hYPFJrNZ1wGc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Globalization.Calendars.wasm", "FileLength": 2282, "LastWriteTime": "2025-06-23T06:00:51.3838152+00:00"}, "fWqFFVvBto3EliyUWZGI9EDF0RsseRUOdULQ8jBQVQs=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\eirmct8v8a-o54lsqobzb.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Globalization.Extensions#[.{fingerprint=o54lsqobzb}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Globalization.Extensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "r9ux1wf8xd", "Integrity": "fyjf11+6g/9R3hLVgYlFCJGJtPCqHmSnAISOJJ0GiA8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Globalization.Extensions.wasm", "FileLength": 2162, "LastWriteTime": "2025-06-23T06:00:51.3902612+00:00"}, "3g2xmPswtD7vFLYKOFNXYScfAkZfCcExVwZknaqNRKA=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\ja2nic4su0-tde8zuw0yw.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Globalization#[.{fingerprint=tde8zuw0yw}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Globalization.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ticidl90z5", "Integrity": "5UDY9F04tR8iE0DoCjavbc9mm4C6tPtDcyTdk65JFek=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Globalization.wasm", "FileLength": 2254, "LastWriteTime": "2025-06-23T06:00:51.395283+00:00"}, "D59lzpmequoGTeEWUTbhQneHdKRRNbDBAeAKvQTqmWY=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\6zclugb5ex-vx3bcge4ol.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.Compression.Brotli#[.{fingerprint=vx3bcge4ol}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Compression.Brotli.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "wx647a6fef", "Integrity": "JOu6LJao8o4ayZQkmGEeaQgEWBNiEM4YH+fC3zMMpJU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Compression.Brotli.wasm", "FileLength": 7041, "LastWriteTime": "2025-06-23T06:00:51.3992971+00:00"}, "CkRDQU0kKtGz39xtl2CO/5w+2Cdzk1twUwChmc05Knw=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\y292lodfv6-yhtj6e0w69.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.Compression.FileSystem#[.{fingerprint=yhtj6e0w69}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Compression.FileSystem.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4xro9lhb94", "Integrity": "qG9CDyiUjSK95cMkSdLGAL5Qa2D2KqJLK8X7DkGjye8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Compression.FileSystem.wasm", "FileLength": 1974, "LastWriteTime": "2025-06-23T06:00:51.4013041+00:00"}, "oppGS2pNelYcQ121Jzzd//+HIY4nZSULtdSsvWevHKk=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\dnpscd8ifx-quahjtap8r.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.Compression.ZipFile#[.{fingerprint=quahjtap8r}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Compression.ZipFile.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "czn0iedi5i", "Integrity": "aBNU6qlxP2cj7WkvjlSHXWcHIRCEzsNGgETttcHFZo0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Compression.ZipFile.wasm", "FileLength": 12714, "LastWriteTime": "2025-06-23T06:00:51.407423+00:00"}, "wbFZ5sjUs/gxGTkqgZWY0Z966mnO6Po90SonBVnI4xk=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\05b0ng3a74-jtaurxkbzi.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.Compression#[.{fingerprint=jtaurxkbzi}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Compression.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sy6t9pb4pg", "Integrity": "fNoiKRaK+kHauQOirNJ/hwOx0U5JbCaTLaRmMyftDvE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Compression.wasm", "FileLength": 43792, "LastWriteTime": "2025-06-23T06:00:51.4154548+00:00"}, "ARPPSK4hzWCmf9WtjlfKHsmuZi/CnGAWm16t/Cp8z3k=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\r0mn7mvsjz-3yfpgyrku1.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.FileSystem.AccessControl#[.{fingerprint=3yfpgyrku1}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.FileSystem.AccessControl.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "wkpchzpsje", "Integrity": "gEZ8LtWz90iB1FdyQdFu6duZLNLLUPSUwQGG1ZqwjIc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.FileSystem.AccessControl.wasm", "FileLength": 8602, "LastWriteTime": "2025-06-23T06:00:51.4174618+00:00"}, "WZDZQQY0NP1WmXSpYsowjR42IvaufaGOGWMr8U4Ca98=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\dy9r3twe3i-8nnv647ull.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.FileSystem.DriveInfo#[.{fingerprint=8nnv647ull}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.FileSystem.DriveInfo.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ovw9kmd9cm", "Integrity": "tMqpLxArg5g4Vc7lfXRGm7wtEIFA8m4dv2ltM6ghbhE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.FileSystem.DriveInfo.wasm", "FileLength": 6070, "LastWriteTime": "2025-06-23T06:00:51.3932746+00:00"}, "sEwo0Z4vVRCc4VI6ynoTvwaW7J1zOZzXo/+fu22tTEU=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\llz7r8eoev-ir5j8vbyan.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.FileSystem.Primitives#[.{fingerprint=ir5j8vbyan}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.FileSystem.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "atyl34fv2q", "Integrity": "fsZMAa/NK5oQ2pgkRPsXh4IOcIRlNbS3KOmsoKGtzmY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.FileSystem.Primitives.wasm", "FileLength": 2169, "LastWriteTime": "2025-06-23T06:00:51.3972902+00:00"}, "3h+XF8ZvNWFWonqwQJ7ZuvSNp1SX1XZC+vGv+dwhTDo=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\4dya8gbl7e-1lxrwwxsho.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.FileSystem.Watcher#[.{fingerprint=1lxrwwxsho}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.FileSystem.Watcher.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "zklz78gk0n", "Integrity": "/SBkDx88vw3qPUSNKhsPhnmoUHLwYohsanGcTcmPiuA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.FileSystem.Watcher.wasm", "FileLength": 8898, "LastWriteTime": "2025-06-23T06:00:51.3992971+00:00"}, "m5bxLRHQHf0SB+caDQ4Y7geVkoYsbwdHtAEL8FPo6Fc=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\ry5ak41fk1-gyxexdekj3.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.FileSystem#[.{fingerprint=gyxexdekj3}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.FileSystem.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1c7fueudid", "Integrity": "Hzdcr3OsbVyzhI1SeoXjVXy32LwG/vtrDAGVU726434=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.FileSystem.wasm", "FileLength": 2295, "LastWriteTime": "2025-06-23T06:00:51.3735805+00:00"}, "XcR+/lsWCLMIpvNLvgYeYGl5TpabDWsb47sxu691GH8=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\3zgx7pexln-tsgf6g1ztd.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.IsolatedStorage#[.{fingerprint=tsgf6g1ztd}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.IsolatedStorage.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mcqtiplk0j", "Integrity": "ptDhHuVd1jAS51ZGnuosQpDI0sUrN7X8oB44f3GB0C0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.IsolatedStorage.wasm", "FileLength": 9499, "LastWriteTime": "2025-06-23T06:00:51.3517144+00:00"}, "b+WPh6aKlAhUojkgA3nCsdwkfbLnH8sYd2rkGYzTjjo=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\qah6ud1v0c-j4sjofqyi5.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.MemoryMappedFiles#[.{fingerprint=j4sjofqyi5}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.MemoryMappedFiles.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ffm2r4izrt", "Integrity": "C0nBSKPvXEw4MKWiTyFaaY9lda/FQFMJIXdSz9Jz1QY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.MemoryMappedFiles.wasm", "FileLength": 16989, "LastWriteTime": "2025-06-23T06:00:51.354919+00:00"}, "AkuVabBtzDuaSDTdPgpnivbBCxrmOYDlNYvwOgk5NEc=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\cnnfgh2tn8-jiaey0kmyh.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.Pipelines#[.{fingerprint=jiaey0kmyh}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Pipelines.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lpicxxeh18", "Integrity": "RxGUijC5cvupdZNNHeF4TjGNICa3CZinpaJ7Em3DS7A=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Pipelines.wasm", "FileLength": 31007, "LastWriteTime": "2025-06-23T06:00:51.3647483+00:00"}, "/EORn8XbgQvcxjkfHq7xoCmt2AuNoTDb0fQ7Md2ZwZc=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\l4fr8wg0hd-qfh40ih8l6.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.Pipes.AccessControl#[.{fingerprint=qfh40ih8l6}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Pipes.AccessControl.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ryu1awleqh", "Integrity": "A+3H7AaNPdMiUVARgceILfWP4VZHQyH4zzb4rB3/CVc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Pipes.AccessControl.wasm", "FileLength": 5643, "LastWriteTime": "2025-06-23T06:00:51.3667563+00:00"}, "SnbvVevlG/FSRn66lwxg+NAEiociKF8rEN31dmPdguc=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\cd240ipm6e-al6w1uowde.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.Pipes#[.{fingerprint=al6w1uowde}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Pipes.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0lvixrqynr", "Integrity": "nGIWKQX284ya25NttDOPsDCyOD66aJ/K6w74zNATLQU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Pipes.wasm", "FileLength": 11575, "LastWriteTime": "2025-06-23T06:00:51.3707711+00:00"}, "0cbua7u4gN2tdN+Y9n0e8llR13cRSOR2Bms5jDSS6c8=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\ul5ywabm60-d0g45p3x9u.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.UnmanagedMemoryStream#[.{fingerprint=d0g45p3x9u}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.UnmanagedMemoryStream.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "i6gslhk1ow", "Integrity": "6tmWhu6Ysd1waq9WcuLOmJVFll3DY+iQB3GjlTcp6Ek=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.UnmanagedMemoryStream.wasm", "FileLength": 2198, "LastWriteTime": "2025-06-23T06:00:51.3735805+00:00"}, "dQ7SVvPH09IcNe6iRaUHJWzLsei/yF9lPJKPYJP7FMA=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\c9did8j2ns-2zge8rv4ra.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO#[.{fingerprint=2zge8rv4ra}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "df01jor508", "Integrity": "lwS76UQUVLTwsq9siQ5IG7h43tyvHNPw/WwO2qdIT8g=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.wasm", "FileLength": 2253, "LastWriteTime": "2025-06-23T06:00:51.3777927+00:00"}, "OX8UylQySZ7xqDs9PhiIS7UyCsizcrCl1QHvMkC0ftY=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\tq1j3a2kz1-2lw1u6ymmp.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Linq.Expressions#[.{fingerprint=2lw1u6ymmp}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Linq.Expressions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1ohh1siyxu", "Integrity": "saQsl6KCY5hEzCOx21HJo+aAtG1ZO98HUiS9uimYo3Q=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Linq.Expressions.wasm", "FileLength": 217763, "LastWriteTime": "2025-06-23T06:00:51.3992971+00:00"}, "sEn5U6/gO/UhjY1SSUdl/6qJekjdkSJvgmqJnN8dtGA=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\a2k68937xi-1jvfownmci.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Linq.Parallel#[.{fingerprint=1jvfownmci}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Linq.Parallel.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jmh3uz7npq", "Integrity": "BQpmqXAMrcabLPdF8uD/fTJNER32caQYEmiJDLGbvPQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Linq.Parallel.wasm", "FileLength": 88000, "LastWriteTime": "2025-06-23T06:00:51.4289306+00:00"}, "/r0j7eGMLBf5NSEMX4O1jw1Se46i7CCYOKZ7I4heyqA=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\9lo1ab5h68-mv4cb7fqwu.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Linq.Queryable#[.{fingerprint=mv4cb7fqwu}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Linq.Queryable.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "npkxpdocax", "Integrity": "5YvqHvO21zbsqu1VOfz8a6HqFY66WsPRM2yZ2d2slmY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Linq.Queryable.wasm", "FileLength": 21318, "LastWriteTime": "2025-06-23T06:00:51.3858225+00:00"}, "3Kj2TxctGf+P8UDgSuFIz680ZAVOJdV6eNvXMBk6u94=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\92kk2br7n0-3djr1lshgb.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Linq#[.{fingerprint=3djr1lshgb}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Linq.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0i5ihx2j71", "Integrity": "WKHXXdh+Dtdjo1f0q2cSrA3rPuh9sS6rG0iWsTVk1+0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Linq.wasm", "FileLength": 56608, "LastWriteTime": "2025-06-23T06:00:51.395283+00:00"}, "0+Yt5DU90zhAO4D5Ggqg6qXI5t8K9xXKvU6K6IJLLKk=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\zlroalg7g1-ub9sra6ubv.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Memory#[.{fingerprint=ub9sra6ubv}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Memory.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3fp9prlp73", "Integrity": "EYIVlPtgWW7kfC97Gju5QH4J5P6sY0eTs8ges6jTVM8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Memory.wasm", "FileLength": 21074, "LastWriteTime": "2025-06-23T06:00:51.3992971+00:00"}, "j4OlOYTh58ThMBCP/z81dtAhYnWj92E0KbyatGbrdtU=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\7qdb0ner4l-346n69ja1w.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Http.Json#[.{fingerprint=346n69ja1w}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Http.Json.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "be9q0v4p3b", "Integrity": "7fGT2ZKIhV3OljAQgQ9Yrs8aszQGIWwj5qDi8Rpm9y8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Http.Json.wasm", "FileLength": 19903, "LastWriteTime": "2025-06-23T06:00:51.4033114+00:00"}, "gW//ThPU28yBjhvHOejTt84rLdMM9l045Ra8i0yqkKk=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\gvepi159hm-eupgag7vx5.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Http#[.{fingerprint=eupgag7vx5}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Http.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1lxk9mv4kt", "Integrity": "+2fs60sb0YnF2AGTnq4JslmlK5yHryy4qIwiK0G06js=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Http.wasm", "FileLength": 115942, "LastWriteTime": "2025-06-23T06:00:51.4174618+00:00"}, "KpV8BiV2+zrfhw1F5gGO5dIDsAN39nnBkPWUQltLW/c=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\oc45k8bbiy-m0tberhw26.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.HttpListener#[.{fingerprint=m0tberhw26}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.HttpListener.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pcla<PERSON><PERSON>", "Integrity": "magIrSS4jHVs/HwBQYok5WsEYavuHhrNk12oXpPgT7w=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.HttpListener.wasm", "FileLength": 16311, "LastWriteTime": "2025-06-23T06:00:51.4212125+00:00"}, "cD+giGx0FmRfHUDFXtnOmk+QWjabnjUu3oxjC9gT9tw=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\m9paoljv5l-7wmkfq1voo.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Mail#[.{fingerprint=7wmkfq1voo}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Mail.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ayj7kwl1ly", "Integrity": "PvsdEMzBpJqIDM3tfzojVZDqSWvdKrVUZDkYr/HiuC8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Mail.wasm", "FileLength": 42447, "LastWriteTime": "2025-06-23T06:00:51.4269236+00:00"}, "/2XFpxSp9B7M7AFsErjW/UYnYEYHatn9krVTkmDYHkc=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\c3i3mokybs-ee8vwc4vcc.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.NameResolution#[.{fingerprint=ee8vwc4vcc}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.NameResolution.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "etmoa1k240", "Integrity": "gje60v116S8HAqXlBYYsmtuMnqcgv9e5REny+X8amxI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.NameResolution.wasm", "FileLength": 5986, "LastWriteTime": "2025-06-23T06:00:51.4289306+00:00"}, "kD3KDx9PARV1exO+OHMtSmjnZEoS1pDbfN+Kty6nD4o=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\1w7yl2316b-h1hduhi84u.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.NetworkInformation#[.{fingerprint=h1hduhi84u}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.NetworkInformation.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "f9bljqqp7j", "Integrity": "Q6e5/VF0sDIbIH3P6ResDbsGzZWa85zG6cghh68KcRM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.NetworkInformation.wasm", "FileLength": 13038, "LastWriteTime": "2025-06-23T06:00:51.4049116+00:00"}, "HLqvG//CJiRw8Qm7s/3QN0h5BB7/t31wsJR3Z8aVtw0=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\mbg54xmxzh-y4g427qvfa.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Ping#[.{fingerprint=y4g427qvfa}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Ping.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j1gyre08o6", "Integrity": "SYfs44pK+EqsM+eCOtJoEThY6sulnpTb4oyua/yklgE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Ping.wasm", "FileLength": 7663, "LastWriteTime": "2025-06-23T06:00:51.4094326+00:00"}, "hXpAsyYoz8+CjHiU97WVRUUQKQx8oNZjEjRNySpQx00=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\86i6bmgmf5-zv1ut64ban.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Primitives#[.{fingerprint=zv1ut64ban}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "a6vs5bag0f", "Integrity": "LqKPpaGjIQKOwe41JryYnyXgogRjvHRBDiGmNLcxKBI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Primitives.wasm", "FileLength": 46557, "LastWriteTime": "2025-06-23T06:00:51.4174618+00:00"}, "poddOD8fSIAZpeiX0rbLegyDxNPQTnKdbvKBp3ZGiYc=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\wdsn0rxyrl-lnozeoe9re.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Quic#[.{fingerprint=lnozeoe9re}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Quic.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "wfeibltbo9", "Integrity": "nAhE7/bNkukzXN2aJ+sETkztBrcoXbpc7btP9LPI6GI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Quic.wasm", "FileLength": 11091, "LastWriteTime": "2025-06-23T06:00:51.3517144+00:00"}, "uE877kn0+qDrQvM2T2WHagwK5EiLljjUdMgxNpC5Uuc=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\4ybzo0ts61-omoxxcqo90.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Requests#[.{fingerprint=omoxxcqo90}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Requests.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "f3cqkzok6g", "Integrity": "uz/kbnyucLQQXeX6eLTH4sum4ER8SWUr424WdSZOcNQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Requests.wasm", "FileLength": 20744, "LastWriteTime": "2025-06-23T06:00:51.3757848+00:00"}, "0hkA2oo2l3KoO34W2bKoCREBSZSvsfrbsLkYKAH6pJw=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\7e73evga9t-t3a07csu2b.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Security#[.{fingerprint=t3a07csu2b}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Security.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rkd68dufdk", "Integrity": "xXkt4vnA8kP53fuAuK1atqQWSKyBicGTDo7fX/8NSi0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Security.wasm", "FileLength": 33472, "LastWriteTime": "2025-06-23T06:00:51.3579977+00:00"}, "6XI1dJ33/pKS7r6P8CmUIVboeUqJz7Eb+dxXFLC3mQ8=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\31hdh8z2uy-5v95sh5c67.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.ServicePoint#[.{fingerprint=5v95sh5c67}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.ServicePoint.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8nnw350zec", "Integrity": "tBZ6lTKEIPEf354p6RQHJ7JBKjnnqOlf2ZOhYYBNchs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.ServicePoint.wasm", "FileLength": 2159, "LastWriteTime": "2025-06-23T06:00:51.3607285+00:00"}, "ZEZVU75fSWgaKKO5VToBbOI73zqV9wjxwa16hBnS9js=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\wf6oo5oepj-ww3h8yu74p.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Sockets#[.{fingerprint=ww3h8yu74p}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Sockets.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rlg7eryezk", "Integrity": "zwmmBlqrESIa1CHNAOPvMOY40CPZKA5g/WTA47VY6Io=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Sockets.wasm", "FileLength": 23478, "LastWriteTime": "2025-06-23T06:00:51.3647483+00:00"}, "Ct00eUXj9XNVv2bzG+P5DsEE3N+lAEf1jpqaP1XAACY=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\vc43hpgb73-345793p9fr.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.WebClient#[.{fingerprint=345793p9fr}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.WebClient.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vsgt99ha8q", "Integrity": "sQj2Rs7Lmh6pH8LQzvxVhE9WQ4MD6/Ec8EVUzo9U68I=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.WebClient.wasm", "FileLength": 14614, "LastWriteTime": "2025-06-23T06:00:51.3667563+00:00"}, "kmDUr0xiskIhHZAYX7zFGIorGzDx+10RozH9meyO4i4=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\64kbjrldre-odv41wuu54.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.WebHeaderCollection#[.{fingerprint=odv41wuu54}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.WebHeaderCollection.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "08adncjoc5", "Integrity": "ADRVFBitBfvJW/qLgSapAlDshYrLZYhKJ8XLeVrWkwo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.WebHeaderCollection.wasm", "FileLength": 10375, "LastWriteTime": "2025-06-23T06:00:51.3735805+00:00"}, "KYYKj/9YWMbIj7ZBJNbE+WBvneMjize/P9mmM8IgAhY=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\f50xrqu6yt-ksx7w94zni.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.WebProxy#[.{fingerprint=ksx7w94zni}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.WebProxy.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yi67r2s07g", "Integrity": "NuAk/mOUZhjoZ2dMQ+Z7c30rn03fp2+0p7w6CkzCrvg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.WebProxy.wasm", "FileLength": 5661, "LastWriteTime": "2025-06-23T06:00:51.3777927+00:00"}, "dCVFksy44IfSj5onlNA6mhvWNMMCQ0LHbXEVjhLUV8M=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\dgtu894v03-b37svw0y4i.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.WebSockets.Client#[.{fingerprint=b37svw0y4i}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.WebSockets.Client.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ks02kds5ez", "Integrity": "hFiD6A4vT4RlCIKTPe3NJQi/i7rIMigUdXK54pZhY7I=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.WebSockets.Client.wasm", "FileLength": 17384, "LastWriteTime": "2025-06-23T06:00:51.3798001+00:00"}, "hBmgTmmkP60h5VaSD5EUfpl+ZsPV6X4tdGpXmtBKXaI=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\2lzjr8pk90-9fasahbeiq.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.WebSockets#[.{fingerprint=9fasahbeiq}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.WebSockets.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cqkvjudxxv", "Integrity": "TEoczHzZquDVkTWugG/H8EneY6VmoaAndKJ6/D6Wz2g=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.WebSockets.wasm", "FileLength": 39051, "LastWriteTime": "2025-06-23T06:00:51.3875663+00:00"}, "4PFFLxC/X8RajXU7D776WylLtIoYWDJLBVIhs2veMFA=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\lqec3rw4j4-qt5fpja9tg.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net#[.{fingerprint=qt5fpja9tg}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vmrcq80u9o", "Integrity": "5xBtqJkMtzUkZ7BFaaaRNaR0xKCmzrQEVSlUC5ukKxQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.wasm", "FileLength": 2746, "LastWriteTime": "2025-06-23T06:00:51.3875663+00:00"}, "qcDjwnjp+GqicXaJ7xmvyCPTk5klPOXmO5Z/Ho0iwus=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\guk7dcvv1w-i6kirq3og4.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Numerics.Vectors#[.{fingerprint=i6kirq3og4}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Numerics.Vectors.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rbvtkhkzzk", "Integrity": "17tNiDQJJqALQ9hxdVlW/EFMkZisfnLbgl2jxA0OPJQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Numerics.Vectors.wasm", "FileLength": 2256, "LastWriteTime": "2025-06-23T06:00:51.395283+00:00"}, "A5k+f/+/HDWMafoFVOGoP4ODVnhHWFWU+CmUgTaAF+A=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\eb09opds16-497r8m9pev.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Numerics#[.{fingerprint=497r8m9pev}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Numerics.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vkn8yyoqll", "Integrity": "oayTBh7+E5+fD6nO51MB31eCGPeW8/Wjp03bCpP4Cgk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Numerics.wasm", "FileLength": 2019, "LastWriteTime": "2025-06-23T06:00:51.3972902+00:00"}, "yQZE5Fd2eiTy0CndFRGnmi7dW5L1mhdPMV1vDRw3z5s=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\bipwzzp8rz-k9az0iuxjb.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ObjectModel#[.{fingerprint=k9az0iuxjb}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ObjectModel.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "b4a28c0t3c", "Integrity": "RWhB2O0xoMbp+OVzALiHQO3SJxdthMZQnioHj3SRBMo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ObjectModel.wasm", "FileLength": 13583, "LastWriteTime": "2025-06-23T06:00:51.4013041+00:00"}, "aubdlT4Wrb67H3RYY+7UaYrhbP2STKQ6GJRXT2ZEQ8s=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\t4lnufzrn4-mnc7tnpegn.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Private.DataContractSerialization#[.{fingerprint=mnc7tnpegn}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Private.DataContractSerialization.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "gwfjrw7x86", "Integrity": "QALWD9Ku6IgKc7U6oZFwaC0ydPCCXFiYJrhy7gUtXbc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Private.DataContractSerialization.wasm", "FileLength": 304574, "LastWriteTime": "2025-06-23T06:00:51.445024+00:00"}, "fhNWjm3JrflEfqGNLmR/HvPXED7CizIMz7q4sBCfetA=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\czk4id43yr-58q5onb7r6.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Private.Uri#[.{fingerprint=58q5onb7r6}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Private.Uri.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ss818p49ge", "Integrity": "NJHTBTQamCCxtXTIPXSxsQe1+XKmyinODiTV1e75npw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Private.Uri.wasm", "FileLength": 42184, "LastWriteTime": "2025-06-23T06:00:51.4492953+00:00"}, "ley+OlhMErDIfjSxDvmfhldXtIu91ltvwwyfIzgWkPU=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\abzlu62u6m-35ud51k85s.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Private.Xml.Linq#[.{fingerprint=35ud51k85s}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Private.Xml.Linq.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "y4j9krw9sf", "Integrity": "IukwXbkF31cR4AObCQy726I029y+g55ByDDIZ0wNGyk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Private.Xml.Linq.wasm", "FileLength": 59684, "LastWriteTime": "2025-06-23T06:00:51.4557137+00:00"}, "8MbLVJRVibeYtJXV+b3dR4772LCdC7y62bz4olc8hJI=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\10stvju5nj-ygkocwikl4.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Private.Xml#[.{fingerprint=ygkocwikl4}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Private.Xml.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "838nx5q8zw", "Integrity": "tB1QsY92OH0AZR2peqj8R1X7+pylNAkMkR3W4rBFzbQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Private.Xml.wasm", "FileLength": 1069742, "LastWriteTime": "2025-06-23T06:00:51.5559724+00:00"}, "J4xDTW35ST3yHs73T5jVlK2GUcE+z4j1+pXeWn9Pus0=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\6ywu70lv1w-13d6e679le.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.DispatchProxy#[.{fingerprint=13d6e679le}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.DispatchProxy.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "e0hth2pwrk", "Integrity": "onvGLQfaVR+HNRAdSRHr22tA4PaJv1fYJXk+GpfwXig=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.DispatchProxy.wasm", "FileLength": 13131, "LastWriteTime": "2025-06-23T06:00:51.395283+00:00"}, "nCu2tFUxGCG9BwEQ/160Lb2tnbBKVQmwAjt3HVliFfo=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\4pwia0m2a0-tnlqh325q4.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.Emit.ILGeneration#[.{fingerprint=tnlqh325q4}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.Emit.ILGeneration.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "csk9ppffhe", "Integrity": "fD7CLVLKiyVy/osi1hIzvcpYkFW1kN77anE48h8yq/8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.Emit.ILGeneration.wasm", "FileLength": 2268, "LastWriteTime": "2025-06-23T06:00:51.3972902+00:00"}, "a3as++f06miTQBCSOvOxaXza6aUiU2c0cojzh45/bBI=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\hvgkomj5p4-6pezgz31ve.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.Emit.Lightweight#[.{fingerprint=6pezgz31ve}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.Emit.Lightweight.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0h355faxxz", "Integrity": "LL7Sm1n53cbE8zF3WmZCFEi+TLSmAoO8MBfi0MyHX94=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.Emit.Lightweight.wasm", "FileLength": 2217, "LastWriteTime": "2025-06-23T06:00:51.3992971+00:00"}, "5YCeTH4cj0K9EwlZ8M20y5/PidGdcpeERcnxZtCErEg=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\f3yil2sd26-1sfjh9emmw.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.Emit#[.{fingerprint=1sfjh9emmw}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.Emit.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5cidbmcpe1", "Integrity": "6163fQjpazHzBRW4H7AWNdlHgGS0+FB1vIfFmc791J4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.Emit.wasm", "FileLength": 52818, "LastWriteTime": "2025-06-23T06:00:51.354919+00:00"}, "NX6jfz15S7PlIsHb+FfEZH8+AQJXBM+qM11vmLw8Myk=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\1u5nf91b1b-o3fapkxyot.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.Extensions#[.{fingerprint=o3fapkxyot}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.Extensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "z3t2vqrng5", "Integrity": "35YDB4eGpHJ95LTvaoehiNmLjkSwVDDti/iwDFKfgb8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.Extensions.wasm", "FileLength": 2136, "LastWriteTime": "2025-06-23T06:00:51.3647483+00:00"}, "9CRSkoqO3poFHIzKMeysRozJVs94CKRd64e94GUV1TU=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\ztobwnunug-tx83z6ho7l.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.Metadata#[.{fingerprint=tx83z6ho7l}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.Metadata.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "gxxc4ke464", "Integrity": "xGqWgtj5ydVw2ZtVAW0Mg3FspmDYWlvKghXB9k6ldcA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.Metadata.wasm", "FileLength": 195553, "LastWriteTime": "2025-06-23T06:00:51.3777927+00:00"}, "ETwMamOo5SwUAAF80Y/oW9dXdD0Gqo1AvgRfRL9GlwE=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\onh7od9mfc-z6035msxdy.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.Primitives#[.{fingerprint=z6035msxdy}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rswt4l55nv", "Integrity": "tSzP0Irs8TW4dRrHh4sLZYEUaGJITHI/IhpcJ/TgVNI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.Primitives.wasm", "FileLength": 2359, "LastWriteTime": "2025-06-23T06:00:51.3858225+00:00"}, "H1TaaSJwNghS1d2khn8wmsvHJ7kwd1S4esFNNAFji+0=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\gpyh0hbuw1-xqbpbwu9vz.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.TypeExtensions#[.{fingerprint=xqbpbwu9vz}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.TypeExtensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6lmrv0yvu7", "Integrity": "Ggoi1qpg1yFvHN1ioFnCjsga4g0Gh2wiyvflol7BrFA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.TypeExtensions.wasm", "FileLength": 5721, "LastWriteTime": "2025-06-23T06:00:51.3667563+00:00"}, "rjKF8Q5RXiitQMtt9j0IzZvIq11fx4Ydz+gT+VM0l7s=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\5e1o11zuut-1kaq8volf4.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection#[.{fingerprint=1kaq8volf4}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "c1qg3au3j3", "Integrity": "MC2f919LZk6jBH+OVLTlu7SnJeaY/edmHsAhLnw8bdY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.wasm", "FileLength": 2448, "LastWriteTime": "2025-06-23T06:00:51.3707711+00:00"}, "DhSQOgCgoHbw38k+t6NjZdifU/6oBrc2dpvEk2L1bWM=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\yum1iez11r-3d1gwadcaj.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Resources.Reader#[.{fingerprint=3d1gwadcaj}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Resources.Reader.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "gb9qigays7", "Integrity": "rGNt2PU+erB+akk3TyoYPZX5zVyIgV7j/3g+iiYCEUY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Resources.Reader.wasm", "FileLength": 2109, "LastWriteTime": "2025-06-23T06:00:51.3715728+00:00"}, "FPE8lkeMtzh+x8MSdJ0D3DjZj/pOU/EnemdLsRhhM48=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\es97lu0ofc-pdb0cwov9g.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Resources.ResourceManager#[.{fingerprint=pdb0cwov9g}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Resources.ResourceManager.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "v037ityzo6", "Integrity": "D9GpO60c8ldX4Hiu6tJpzctK4mxAq+jDSqpgxL8Kz4o=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Resources.ResourceManager.wasm", "FileLength": 2233, "LastWriteTime": "2025-06-23T06:00:51.3777927+00:00"}, "Z+q9kjLtMp8ebefWbPnN+J2yrWA5o+oYH56qvowOBn0=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\vmefll79a3-wfwt17t25p.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Resources.Writer#[.{fingerprint=wfwt17t25p}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Resources.Writer.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "34sp6w4qs6", "Integrity": "bZ8stjLg53ebSTM15Et8OGa/y32APeRn8qhm8ma8uz8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Resources.Writer.wasm", "FileLength": 7733, "LastWriteTime": "2025-06-23T06:00:51.3882503+00:00"}, "EzGJ/h3SN4x/qeYFsIHWpEkDAigUsVg2gMmZIqwq3uY=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\2lmyqeiivu-rt5a291rko.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.CompilerServices.Unsafe#[.{fingerprint=rt5a291rko}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.CompilerServices.Unsafe.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mvyuthobt5", "Integrity": "7pL2LLz5OqzqdAK/M1rc67gHKYRwI+Mt+q910KYHcZw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.CompilerServices.Unsafe.wasm", "FileLength": 2113, "LastWriteTime": "2025-06-23T06:00:51.395283+00:00"}, "Ga9CScmsFEK3d89oGz2BCmWaaqGLuqDAk/EP0T50UtI=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\ql4vrvr5xs-gigtt0ldg1.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.CompilerServices.VisualC#[.{fingerprint=gigtt0ldg1}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.CompilerServices.VisualC.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nkhifwy7jq", "Integrity": "iQff77t5uL6vhGYfsFOU1vC3FHfLITDN0w19YkxdEfc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.CompilerServices.VisualC.wasm", "FileLength": 3070, "LastWriteTime": "2025-06-23T06:00:51.3932746+00:00"}, "iY/SaC0Qga2nbuwZvNG0FvyNrPZtjzPekodl/OAv3Ns=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\x8zcqs9shm-kaw15hufc0.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Extensions#[.{fingerprint=kaw15hufc0}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Extensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "wlyzqzr1hp", "Integrity": "DLD9+xmA6GaAeZPdsATM2G1HFtJoZbGmL9W98XtpNR4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Extensions.wasm", "FileLength": 2992, "LastWriteTime": "2025-06-23T06:00:51.3972902+00:00"}, "N+QlYtSC8COS4eE9lLCKpPTY1kBf9lEwrBdZs/6+U4M=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\7ctxoc39mv-7qypx0bvu1.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Handles#[.{fingerprint=7qypx0bvu1}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Handles.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "zj0i4nyg8b", "Integrity": "scwPVG7kXwmmJ3+YW62Z+9/A9GVpiLvCx44EtXtzik0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Handles.wasm", "FileLength": 2193, "LastWriteTime": "2025-06-23T06:00:51.3992971+00:00"}, "fMW6KGdIkmlZHhtPdFgDMNBGRY/RD0nC0PGRNpGaxqk=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\fzpagszpqm-k67jm10rbw.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.InteropServices.JavaScript#[.{fingerprint=k67jm10rbw}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.InteropServices.JavaScript.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jdlhwgd1zl", "Integrity": "d/YDaVXGLKxYot1ip+7a+REVxsB8tnP5+ysRM03Ztz4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.InteropServices.JavaScript.wasm", "FileLength": 31673, "LastWriteTime": "2025-06-23T06:00:51.4033114+00:00"}, "v/MzgK7Fxa/zCVXb7fr9pD76cqT+rUMIeVDo/hk6mUg=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\ejecmpjgbs-uanr5ywdiz.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.InteropServices.RuntimeInformation#[.{fingerprint=uanr5ywdiz}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.InteropServices.RuntimeInformation.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1ze9ycbr99", "Integrity": "88lu31uH9h2xMFILpjvLvRwPNAFzcLey//zoZxeqImA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.InteropServices.RuntimeInformation.wasm", "FileLength": 2135, "LastWriteTime": "2025-06-23T06:00:51.3972902+00:00"}, "iSlVOuM4u7d3EZEnMNO5I1nny3Mgs1A86b4anUISjaM=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\0by6xj74qj-fel5k50x7l.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.InteropServices#[.{fingerprint=fel5k50x7l}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.InteropServices.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7zll2bh1yf", "Integrity": "ff8bsek39Z2vrgQBtSiGxaGvflbpGj8tZQt+tzgzeqI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.InteropServices.wasm", "FileLength": 23803, "LastWriteTime": "2025-06-23T06:00:51.4033114+00:00"}, "FajMYnq/L73MZmlhH9ByuecTEO9wkm+AyQys6MV0uUg=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\3omn4wg7am-eoagj84dsy.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Intrinsics#[.{fingerprint=eoagj84dsy}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Intrinsics.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "wtqtjw6gpd", "Integrity": "7P8zvAT5TW20qwA/9UfVgVJrH/MxbVEZkFIWt50oFEo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Intrinsics.wasm", "FileLength": 2726, "LastWriteTime": "2025-06-23T06:00:51.4049116+00:00"}, "l/BZFFi+nbfTjSnybkL1mJsj0oDqVr+e6EWdT6KlWAM=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\wdj0qkbnmh-7g62ykjls0.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Loader#[.{fingerprint=7g62ykjls0}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Loader.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "c2x1oz8a9f", "Integrity": "pOEYLfLx5/lnCSMAf4uTFAzWdstRZRTRxDUibLELHqQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Loader.wasm", "FileLength": 2310, "LastWriteTime": "2025-06-23T06:00:51.407423+00:00"}, "etyi5p+qcOVKh1z6bx2LpvQhHwwGFdXYObz+MLoMAi4=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\1g9k5ulnya-tp0shtj6gv.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Numerics#[.{fingerprint=tp0shtj6gv}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Numerics.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5ph2wzbi14", "Integrity": "531+Q9qB1pHWDp1Fg41z+vh4pdnO/nJF6cX7MlmnHWA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Numerics.wasm", "FileLength": 53377, "LastWriteTime": "2025-06-23T06:00:51.4174618+00:00"}, "BL8jSydZnNEfTsiqUnLNfn4qnBTNZws+srEN4VBqb2A=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\w2x3293hhu-nvsnsgm1il.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Serialization.Formatters#[.{fingerprint=nvsnsgm1il}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Serialization.Formatters.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rcg3dlejl2", "Integrity": "i7ZNg4n9i4VIVuVSW5/VzUw4cMQ88XPx0grUvnW3/zg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Serialization.Formatters.wasm", "FileLength": 24570, "LastWriteTime": "2025-06-23T06:00:51.4212125+00:00"}, "hmYOwiLsffbD8wII44JS+J6JkBPkSfN6EYG9dmJ7Cc8=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\k94m19iflk-4t62p34f9u.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Serialization.Json#[.{fingerprint=4t62p34f9u}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Serialization.Json.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1n3etz794o", "Integrity": "7WKjoeWfL2oKUX4/wPI+ykVjFPEGYgu8ez2XTdzJ/KE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Serialization.Json.wasm", "FileLength": 2240, "LastWriteTime": "2025-06-23T06:00:51.4229064+00:00"}, "TFPxJ4lJ/bOK3EKwuh7lL/DcwzE6kh/villgMbAWywI=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\jxh59xukja-8mh3k1xubv.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Serialization.Primitives#[.{fingerprint=8mh3k1xubv}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Serialization.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "o<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Integrity": "LNrPc8x9DyxajRmoZrbBk71JCQzrGzQ2cI+l423Z9Nc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Serialization.Primitives.wasm", "FileLength": 5495, "LastWriteTime": "2025-06-23T06:00:51.3517144+00:00"}, "4N5RMIQZ512xsE3mFNMRsPJtptjpzaIvpMuoBOrJ24M=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\yaan75m201-6heyz9oosd.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Serialization.Xml#[.{fingerprint=6heyz9oosd}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Serialization.Xml.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xnndll60qb", "Integrity": "DcKjKFoexelIV2bxQJ+kAwymeWC6jeeO5UCGSJFa9tE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Serialization.Xml.wasm", "FileLength": 2549, "LastWriteTime": "2025-06-23T06:00:51.3537223+00:00"}, "eurpMS7wXEG7luVbdZmplE0Ma+5pHPpZv73jzApYego=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\nqfmgz803a-1oa8jl3amd.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Serialization#[.{fingerprint=1oa8jl3amd}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Serialization.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bcmg3094io", "Integrity": "G0gLgJHuAPt4GEQgxTNBPKWF4afqxpT6kP4nAoy4IEo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Serialization.wasm", "FileLength": 2494, "LastWriteTime": "2025-06-23T06:00:51.3537223+00:00"}, "i9dUbqcVZyU40fqbGYwAFcaAmaqD3XRAir+/HmKf6xw=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\2qwoi9fl35-xqvdvko8po.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime#[.{fingerprint=xqvdvko8po}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3w9md5z1ys", "Integrity": "k6xhAVn2hOgVSjpcJKUwcChqt0oYg1UxPLd03vdz+VA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.wasm", "FileLength": 10714, "LastWriteTime": "2025-06-23T06:00:51.3579977+00:00"}, "ilfM6fgaBuyriQZ68MhD351v8HRukMxLf+5Ls8fuWjw=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\66dqee4i3d-jtnq7vre8d.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.AccessControl#[.{fingerprint=jtnq7vre8d}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.AccessControl.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "v3yey0jx5i", "Integrity": "1Ym8LYop3didT2lrqPCvlrgJjEzGlSlsylOh5uxNYKc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.AccessControl.wasm", "FileLength": 17216, "LastWriteTime": "2025-06-23T06:00:51.3569259+00:00"}, "o306Qc+MQ7lds8L5Pak80PWaHSCtvatgJfVwy6tAv94=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\7d73snp3lk-9fyr8onzdl.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Claims#[.{fingerprint=9fyr8onzdl}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Claims.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1dumalpyqb", "Integrity": "y+78FSqxnOTAkaxgQSrTl513KkoQAJtiHnqzMWHNfUs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Claims.wasm", "FileLength": 16441, "LastWriteTime": "2025-06-23T06:00:51.3607285+00:00"}, "cuOxhpsKjcAnLPSZ35K3LtR/WJU1T69e6i6g9jOJxoc=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\7c46vztfyl-49z3p61zui.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.Algorithms#[.{fingerprint=49z3p61zui}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.Algorithms.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fxni3ae6l2", "Integrity": "XQuGFeix4PHme5ttyIzHz3hf6ZEZx3aAZmtzvIZNO2s=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.Algorithms.wasm", "FileLength": 2711, "LastWriteTime": "2025-06-23T06:00:51.3597418+00:00"}, "bQN1HkLMvrG4cZsT6BlaZRc9WjmTGqcDr60c+nlDLX8=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\ldchrd2ota-01efu89mjc.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.Cng#[.{fingerprint=01efu89mjc}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.Cng.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bl6uopkdqg", "Integrity": "Ied4eBbUoibS/4YvIh/9d2SP5Br+PMElZpwpwYS+SUA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.Cng.wasm", "FileLength": 2465, "LastWriteTime": "2025-06-23T06:00:51.3607285+00:00"}, "G92TYbfTfj1gnOyyKyUDlKge0652tq3BlVud31HFi6w=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\cu1bqe1l66-m6kt5rkphi.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.Csp#[.{fingerprint=m6kt5rkphi}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.Csp.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "l8k76ne4z5", "Integrity": "yzspymtMcrrVsAF06N7A4Li+aqibd0k7d+OOwFlEJps=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.Csp.wasm", "FileLength": 2337, "LastWriteTime": "2025-06-23T06:00:51.3627403+00:00"}, "W5mxNBJCjRQAc6WdFOpdgfETPPNu1p/Uts1ivlrg/7I=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\asx1gpn1le-4j2304etti.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.Encoding#[.{fingerprint=4j2304etti}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.Encoding.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bh55ti10w3", "Integrity": "AYC6S7nA6hG1FzlgKdFL7sSs5myXWo1Afmxd0n+4GLM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.Encoding.wasm", "FileLength": 2267, "LastWriteTime": "2025-06-23T06:00:51.3667563+00:00"}, "V5jGMvtlIXXCFduY1hai7TWCrj6/G2yNlboNOyLIjQI=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\xysbwwi2pa-rgf4gnhaju.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.OpenSsl#[.{fingerprint=rgf4gnhaju}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.OpenSsl.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7h4x28z4fw", "Integrity": "6yZXPBxdIqVTMQqwxuocdBNBY6f20EUIWmLwEydfWN0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.OpenSsl.wasm", "FileLength": 2199, "LastWriteTime": "2025-06-23T06:00:51.3627403+00:00"}, "eKJndRn39RRpGJAou1NtqH+FpoaJ9bLmQMb/FWZG3kQ=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\06q9fio76v-wqi94vu5m0.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.Primitives#[.{fingerprint=wqi94vu5m0}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rgx2r1g9kw", "Integrity": "6/Q1nLCsqlsLH0gMZynIxrzsRYIzrsCfVis8loaZij8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.Primitives.wasm", "FileLength": 2328, "LastWriteTime": "2025-06-23T06:00:51.3667563+00:00"}, "A0IB5rVSXIe9ULfXMtGhDdinPYs0r2hM5dbZiz9sMSc=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\lq3eyvxidg-e4s9csihna.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.X509Certificates#[.{fingerprint=e4s9csihna}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.X509Certificates.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2xmdaof6am", "Integrity": "Rs9CzwX955M6+J5cSE5+UtYp/kd2+k2qwntwNqM5A58=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.X509Certificates.wasm", "FileLength": 2668, "LastWriteTime": "2025-06-23T06:00:51.3687634+00:00"}, "F7gtaRY/+G84pdkMqsDZ7x2Kzlj3Huit8tEOgJnX3nE=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\tz3iutuh8k-z9o6jihhaw.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography#[.{fingerprint=z9o6jihhaw}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xxa6bxtj1c", "Integrity": "G+QteTbO5tnfPdBGQsv0Zr+pUUn1rlL3RPAUUOzmDcw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.wasm", "FileLength": 192141, "LastWriteTime": "2025-06-23T06:00:51.3932746+00:00"}, "B50NyEHEQcJxMpXjYGQg/WSscSfTQi7vv7ryVQwwLc8=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\3tkjo3sq2x-d93pggsupp.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Principal.Windows#[.{fingerprint=d93pggsupp}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Principal.Windows.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4o9pv9buaz", "Integrity": "NHADR5aEs5o28yGOXqSHmsBO9Zy5ri/BkJAE6Teozxk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Principal.Windows.wasm", "FileLength": 11366, "LastWriteTime": "2025-06-23T06:00:51.3687634+00:00"}, "WuWAtVQGkD2SyHO1yMv3hyHfmnEREmVxWW/6HDO22Yg=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\rib056jhl1-sa193kq3m2.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Principal#[.{fingerprint=sa193kq3m2}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Principal.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1qh6t283fw", "Integrity": "OWQzw4Za3nteSzWsPES44yknwXZhgqp3r5KGCde6q/w=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Principal.wasm", "FileLength": 2148, "LastWriteTime": "2025-06-23T06:00:51.3757848+00:00"}, "8wvSNcCPbeSF5awmUeEoB4MmqNcbyXUyt5+jUEmDFpQ=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\kgttb1i5ls-z4ma9duddm.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.SecureString#[.{fingerprint=z4ma9duddm}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.SecureString.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5j82ul3idq", "Integrity": "T2ZLclHRPGrl/GipYza2YeBIG7obvhp8Ok9tH0XtLd8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.SecureString.wasm", "FileLength": 2178, "LastWriteTime": "2025-06-23T06:00:51.3798001+00:00"}, "7RuP9F+XCspTTUgMvu4BgbOthys/JgWMp1YHQgZYz4c=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\ecjnv8uddc-rztf0whns2.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security#[.{fingerprint=rztf0whns2}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6ws3zfk55n", "Integrity": "BtTIpA0y7Hg3Y2L+P/umobP0CpSc+NRMV00qR7mZiQA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.wasm", "FileLength": 2975, "LastWriteTime": "2025-06-23T06:00:51.3858225+00:00"}, "SHsmRzbx8GDPCJ1wXlXcfwh/CWUsrRqMj00oApIHss0=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\po3xh0abvb-bnlcmxi1w6.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ServiceModel.Web#[.{fingerprint=bnlcmxi1w6}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ServiceModel.Web.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qnfhl8a7m8", "Integrity": "hh68E26kzzMIOxfcDDPfs1dZCg9HgEC2jksd69R5u0U=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ServiceModel.Web.wasm", "FileLength": 2536, "LastWriteTime": "2025-06-23T06:00:51.395283+00:00"}, "IAw2qzel7bcQVK3KULVAoCQ+7wTOP9omDF1kXMtWIck=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\radirn3peg-drbdk7bquo.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ServiceProcess#[.{fingerprint=drbdk7bquo}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ServiceProcess.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0o2as0h125", "Integrity": "AOj2B3FNj2/Ixiwa6tfgejJBd5lNZAMe6gJ9WhxYSpk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ServiceProcess.wasm", "FileLength": 2290, "LastWriteTime": "2025-06-23T06:00:51.3972902+00:00"}, "7g8znL96eajlen0ffjvgShjK4ytwc39g/FiB1WxeUHg=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\5kx88tufpw-7iff0d2lb6.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Text.Encoding.CodePages#[.{fingerprint=7iff0d2lb6}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Text.Encoding.CodePages.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2vtkvr7sfd", "Integrity": "Lg/kcZFbiO0FE3/7DOjFHgY1G5tMsCH9J8pdWCkarig=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Text.Encoding.CodePages.wasm", "FileLength": 518369, "LastWriteTime": "2025-06-23T06:00:51.4492953+00:00"}, "gs8kBo8jdLzreXpMz8OvaDJjlcEqnLUUOJUiNCec2Ek=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\zv5xb7pdxi-dc711vstge.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Text.Encoding.Extensions#[.{fingerprint=dc711vstge}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Text.Encoding.Extensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ntap45v1i2", "Integrity": "C+soKeklt2AF1yBlXQyCoynz3Iylfrn3GZ0WrcwifLM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Text.Encoding.Extensions.wasm", "FileLength": 2243, "LastWriteTime": "2025-06-23T06:00:51.3517144+00:00"}, "w7UjViHDQRpPnq/FrghDw+ZpJpNPlzOBKaUoQlHuKgo=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\qd3x16gmyn-3696nx7xrc.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Text.Encoding#[.{fingerprint=3696nx7xrc}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Text.Encoding.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "leeatf83wu", "Integrity": "k3+G/lsFhhCq1B7AAKVCtcPG5UoJDTTZruusOkdNh/o=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Text.Encoding.wasm", "FileLength": 2230, "LastWriteTime": "2025-06-23T06:00:51.3627403+00:00"}, "+lQl40ryQL1yzm0Lf8h8EES+Zz9DRFGt+o1trDSLgFU=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\m2wq4fipdh-ksemyzm5ld.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Text.Encodings.Web#[.{fingerprint=ksemyzm5ld}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Text.Encodings.Web.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "od24wa2zc5", "Integrity": "9LKtbkx+vEj07nUq1jubni5vfAl1tycI6V9Dv/ZbIUk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Text.Encodings.Web.wasm", "FileLength": 23947, "LastWriteTime": "2025-06-23T06:00:51.3569259+00:00"}, "ARztHb86TDsJeAswv9kLoNsMeF7mHb2xPe3FTbI+7X4=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\o29qy48hz7-x92ye0v3y1.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Text.Json#[.{fingerprint=x92ye0v3y1}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Text.Json.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "t701n6znlm", "Integrity": "Wfbxj4fsGd8RBghABUZEBF8YDLhx6YsmfpPSPPJ9zyg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Text.Json.wasm", "FileLength": 221068, "LastWriteTime": "2025-06-23T06:00:51.3818074+00:00"}, "DBNjNvQ6HtjEmk5UM3eB/4YumFbXh4fZVlSrcN2zIjg=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\d5tsrwye6m-dnj9z23s0g.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Text.RegularExpressions#[.{fingerprint=dnj9z23s0g}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Text.RegularExpressions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pm9l8lcd3g", "Integrity": "/1/zoRMl9NOalOaeAp9B3hj5YcGPRuF8bVJfstAyZH0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Text.RegularExpressions.wasm", "FileLength": 156828, "LastWriteTime": "2025-06-23T06:00:51.3777927+00:00"}, "/oeJcdGgczVVcq0MmXeK4xxyL+lR1yUOeYnXxTO6Ulo=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\kfejvvjs6u-tomvzoqfcf.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Channels#[.{fingerprint=tomvzoqfcf}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Channels.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3h7dtb1n99", "Integrity": "6X82iHcnII3dTl2enW4OOzzBU1lv2RgmE09udheqCh8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Channels.wasm", "FileLength": 21003, "LastWriteTime": "2025-06-23T06:00:51.3858225+00:00"}, "PwjO20bAv450aIvu+FnoE8D9O2bfN/eBu3exXJU/Og0=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\2lnccv90rh-0g3k20op8c.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Overlapped#[.{fingerprint=0g3k20op8c}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Overlapped.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jggnz8wud5", "Integrity": "E1aFmjga0iAdsoKdBzoUcehW/CPkfIe0BMJC/IDWCp8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Overlapped.wasm", "FileLength": 2294, "LastWriteTime": "2025-06-23T06:00:51.3858225+00:00"}, "5N0K9H2NvRdViHcuP0UdC2nxsTgr6SYTWH0blUvVwlY=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\hlfs1kkasw-55tewhp7kf.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Tasks.Dataflow#[.{fingerprint=55tewhp7kf}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Tasks.Dataflow.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fnn2z7a742", "Integrity": "xTxs0XlJRWyXMWLzJEjNgm8FIJahGJELz0sOsiCoyvk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Tasks.Dataflow.wasm", "FileLength": 74082, "LastWriteTime": "2025-06-23T06:00:51.4269236+00:00"}, "L9DawE6Thwy0rbNXKvb2eyDKP4eMYyqYKMxleJgfDjM=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\erbawa0wx7-sce61xpslf.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Tasks.Extensions#[.{fingerprint=sce61xpslf}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Tasks.Extensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "l86mmsxm7d", "Integrity": "6+C8ZMjr5MzTX2QkIHFvL7QbodsxysQCokm9JITGTK8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Tasks.Extensions.wasm", "FileLength": 2293, "LastWriteTime": "2025-06-23T06:00:51.4269236+00:00"}, "sQpp76gpR5JgVT1jBO6VsCaa95Tuuoxzi+51GFDcqVQ=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\jettazdogb-548crbk151.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Tasks.Parallel#[.{fingerprint=548crbk151}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Tasks.Parallel.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "otm9gs4gkl", "Integrity": "zBuzsvnFhLJv4jPUinrND4oS4eurGslzTORlmmqlpjg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Tasks.Parallel.wasm", "FileLength": 21510, "LastWriteTime": "2025-06-23T06:00:51.4309376+00:00"}, "zcD51Z7f6R7Q6stS6F+9eQKpzHeCUMurKiU1EUdr9W4=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\i4ve7v6q56-x86n4j91or.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Tasks#[.{fingerprint=x86n4j91or}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Tasks.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "y3o7o8ez1e", "Integrity": "MrJfPBMuv/KdBnPTOOfA8D1AKzPyTYL8sXn88of13gk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Tasks.wasm", "FileLength": 2558, "LastWriteTime": "2025-06-23T06:00:51.391266+00:00"}, "/28jA1c2CwDtLkroYJziGqd3apCS46zQOA3Qq7Wpl6c=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\ibi3o1lmq5-07bttawl88.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Thread#[.{fingerprint=07bttawl88}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Thread.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5x5ytvgqzz", "Integrity": "eevbnNJRnoZurKE4BR8GZIhQGibNr3QdTnAv6ZLHtE8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Thread.wasm", "FileLength": 2326, "LastWriteTime": "2025-06-23T06:00:51.395283+00:00"}, "krcsxcsh9uDZi1lwObaHQPCMXJNOv1cP7V+4p5IvdVk=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\0crezbyjyp-zt447d1n6v.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.ThreadPool#[.{fingerprint=zt447d1n6v}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.ThreadPool.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "a50n3sx6h2", "Integrity": "fsNmThN5cq0Mt/ymgJQg3FfNKjWuRYAe9K5reWHeYCs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.ThreadPool.wasm", "FileLength": 2248, "LastWriteTime": "2025-06-23T06:00:51.3992971+00:00"}, "AcR7GyRwmXqtMirDZCfAdVOwgdNjLfggq3aqKogbD3M=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\syanezeado-r3c1h58f9w.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Timer#[.{fingerprint=r3c1h58f9w}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Timer.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rifr52ofi6", "Integrity": "rctNuwi/MAv5qJpXrGFu5z3r/2b+SDHKKnEc0VH9sWg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Timer.wasm", "FileLength": 2115, "LastWriteTime": "2025-06-23T06:00:51.4013041+00:00"}, "ccIQAE9R/RwyMWovGsnAcBn/knFotenArwF5OnroJmQ=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\h1pclr2djq-4z6mzh73ny.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading#[.{fingerprint=4z6mzh73ny}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "89qmmkhfst", "Integrity": "bKQOk5IGQ34BE/Y/jzJJ/zIr3M1siIbJr9+6C/0mn/o=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.wasm", "FileLength": 14941, "LastWriteTime": "2025-06-23T06:00:51.3972902+00:00"}, "vWYafcd7IGByQ6FIsU5LmvTtvsl+FTHEAhDFj1sY9cY=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\om7kli6l15-pil3cjgvw5.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Transactions.Local#[.{fingerprint=pil3cjgvw5}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Transactions.Local.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vgpahe5lyc", "Integrity": "kSe3I1xWPAufcnWvY7Xq/8YT2iK4rY/NX+MrioJVtkQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Transactions.Local.wasm", "FileLength": 52477, "LastWriteTime": "2025-06-23T06:00:51.4289306+00:00"}, "xpk8+fo/zXdJ8jOKfnBdJgg9Xh6+nOuQoDNaR+wO0PE=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\p4pdpk54sb-g6ni30uafv.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Transactions#[.{fingerprint=g6ni30uafv}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Transactions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "l1gdhuk2kv", "Integrity": "h8TpydIBvtLjBYRqPX1JaCF7peCV9edefjFw8daFXNs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Transactions.wasm", "FileLength": 2361, "LastWriteTime": "2025-06-23T06:00:51.4309376+00:00"}, "VCxsgLQxNPqYz+LpODRw1/rw/HFgfAIl1d18O7kQx2U=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\tb1sbmlzq7-adv6hyw1vi.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ValueTuple#[.{fingerprint=adv6hyw1vi}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ValueTuple.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uk8d9h4xhb", "Integrity": "rnmEasDldWxCjZLOI5Wia0+lB4zr+uHaT+M2BWJ9SpA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ValueTuple.wasm", "FileLength": 2162, "LastWriteTime": "2025-06-23T06:00:51.4309376+00:00"}, "S3hjEmT428sHfTFbxPCElfTb5JETnMaCTeGvwY3rNpE=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\dgxvboka7h-4yi0atwy17.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Web.HttpUtility#[.{fingerprint=4yi0atwy17}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Web.HttpUtility.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7klcrsyr07", "Integrity": "2TxsLC4iLUfX8bRf+rnVHzo6bJtio9FCxMBLPa6OHPA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Web.HttpUtility.wasm", "FileLength": 10064, "LastWriteTime": "2025-06-23T06:00:51.4329453+00:00"}, "CPeY2LXc/b2lPinEgwX8vgkT7Pu5WlYgUgNhW6qYgXI=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\y7gw1kgtnu-8uickrr2w7.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Web#[.{fingerprint=8uickrr2w7}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Web.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9hqzmdmg5d", "Integrity": "MwXwvecCl+SGQrtp/IB8h8FfvevcK89G+0JjywN+C+s=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Web.wasm", "FileLength": 2106, "LastWriteTime": "2025-06-23T06:00:51.4329453+00:00"}, "wppyCTj+25W29OJ8WwKe5eJIgrAdCz6eAZPFtXhG5Rk=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\d4ndgctfln-idlgil0u1u.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Windows#[.{fingerprint=idlgil0u1u}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Windows.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "eekhdpembb", "Integrity": "eu3I5qgLq5W4qWFwLmSmG8Z35hIe9ctCNUeivjXh3WI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Windows.wasm", "FileLength": 2264, "LastWriteTime": "2025-06-23T06:00:51.434952+00:00"}, "vdmAPWqrkEQRh/YaUsgf1NLl+SXKP5U/QfvYmucTTmA=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\fj21328flb-jhjtvo31q0.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.Linq#[.{fingerprint=jhjtvo31q0}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.Linq.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n40nabuvce", "Integrity": "mamYlqSUvXvUlF00FqByjjJHF5WnvlEerUwlNms01JU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.Linq.wasm", "FileLength": 2205, "LastWriteTime": "2025-06-23T06:00:51.3735805+00:00"}, "+KAt4d9YVekqhJR8+vMQmtasUmbD8TpttUS1s5KZt7M=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\lbefjm3fer-pcqwh7wu97.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.ReaderWriter#[.{fingerprint=pcqwh7wu97}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.ReaderWriter.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5twxwprbc2", "Integrity": "p0dyu6/gjEqt7HCPOVYRYLdOpalZPOSWc5PKBuExWA0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.ReaderWriter.wasm", "FileLength": 4022, "LastWriteTime": "2025-06-23T06:00:51.3838152+00:00"}, "SNVsrt5q1EYNH+pNlbJftq+O7ONP+56kkeOdVS4GbPU=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\lticvmvabn-0x6beqi7zp.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.Serialization#[.{fingerprint=0x6beqi7zp}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.Serialization.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mc2pljwaz7", "Integrity": "Jrk1R79xeAFdCbq0Kc1d7QHIDXfuH9kXXf1D3ZaqOWM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.Serialization.wasm", "FileLength": 2234, "LastWriteTime": "2025-06-23T06:00:51.3777927+00:00"}, "0Jv5XaPLGsNf0z//ZlxDfnA7s+nDLcG3mYQibTpVo+c=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\iajbevbx7x-8luigpl137.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.XDocument#[.{fingerprint=8luigpl137}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.XDocument.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fbmrz5dke3", "Integrity": "u7CkA6egzyEHbHc7L8+b7LT9DidsxOaKsR+fpcOj2Ac=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.XDocument.wasm", "FileLength": 2388, "LastWriteTime": "2025-06-23T06:00:51.3798001+00:00"}, "sxIa5VSxQDcaBiQNBLk3nV5hjT1esqzUqizGek9brYs=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\i2snljizbn-rps120mzwr.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.XPath.XDocument#[.{fingerprint=rps120mzwr}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.XPath.XDocument.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "gq3ft88dnn", "Integrity": "WCLUHbMkDvH3esV5A4Gui8Jgdby9rwRWFO8jGCFxgiA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.XPath.XDocument.wasm", "FileLength": 2471, "LastWriteTime": "2025-06-23T06:00:51.3838152+00:00"}, "I5T1KcjAdJ8XWJDgMOEWa8RMb3B5FctlIMZAMdRAQ/w=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\qp5h3espka-nj6o6nhskf.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.XPath#[.{fingerprint=nj6o6nhskf}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.XPath.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bodr2nb7ol", "Integrity": "++uAK+mUEMon+RcnZC7TFIr5HUcae/dnJjaj2G+BOac=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.XPath.wasm", "FileLength": 2304, "LastWriteTime": "2025-06-23T06:00:51.391266+00:00"}, "mAqkO5gxchiGKQQOF6ofWyDXgYyLCM8eMYcBwUOvaJE=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\htuzvqynu1-t7u25q5to4.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.XmlDocument#[.{fingerprint=t7u25q5to4}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.XmlDocument.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7s1grkdd27", "Integrity": "B0gfSt72UNpE20N7SjH/fqE0e9oo4ZFzwPoFgX8esA0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.XmlDocument.wasm", "FileLength": 2345, "LastWriteTime": "2025-06-23T06:00:51.395283+00:00"}, "kp4Ut10xSlzvvInh/ZsDzNPFucz3RxyKyVuLKc1PurA=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\bzzmnifaxi-ig2qir1wep.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.XmlSerializer#[.{fingerprint=ig2qir1wep}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.XmlSerializer.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "is4ytwjijb", "Integrity": "PkZ+EyfQnZrYKPRFQ6MiSkbuJRS8u/bDxht3wk0rkkg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.XmlSerializer.wasm", "FileLength": 2846, "LastWriteTime": "2025-06-23T06:00:51.3972902+00:00"}, "gm8pifucYdI6zgG0RzZvVrxC6o+Ndi3qPgPAUr4+aFo=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\u9czy4sbgq-lnwczuoimm.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml#[.{fingerprint=lnwczuoimm}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "q4ukya97pt", "Integrity": "iu/I+gH6y+kkF3PiDqcN1MfrstHppZAGB7Qtqm76EOQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.wasm", "FileLength": 4234, "LastWriteTime": "2025-06-23T06:00:51.3875663+00:00"}, "g5L68nDlNM6+S7L9XUC//9Qs1j0Df3i8QUkjI4/A4RA=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\pejbvlp1jq-00ls1afmp9.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System#[.{fingerprint=00ls1afmp9}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "frqj36ovfx", "Integrity": "OwkxzLI1iHJHnDG0234yccZ21jqD7BQbgqdaX3KA5lk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.wasm", "FileLength": 11846, "LastWriteTime": "2025-06-23T06:00:51.3932746+00:00"}, "BJJylXHzLx993LZbbAU3Wv3N8mwIbVBuRykSBDFYzwo=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\zsl1wupbag-u25hol0de4.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/WindowsBase#[.{fingerprint=u25hol0de4}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\WindowsBase.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "57i10gl44u", "Integrity": "YaJsYRR7iSBjrEfxtDToxuLFXqdXo0r9g3uDrwA1uSo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\WindowsBase.wasm", "FileLength": 2504, "LastWriteTime": "2025-06-23T06:00:51.395283+00:00"}, "ptcjW5qO7sAJIZHjQnMPWm90nJq8oxRJ7SDTZUBXD5A=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\b6z1yv90xl-brg9pkj3je.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/mscorlib#[.{fingerprint=brg9pkj3je}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\mscorlib.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ypb5slrtpd", "Integrity": "lsphfcbp9xwONswYJJqYCzrQVqvHy0mclKUNzeLpRdY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\mscorlib.wasm", "FileLength": 14878, "LastWriteTime": "2025-06-23T06:00:51.3992971+00:00"}, "L+Xs+6LAUaLxVYkaKvR0XLrME/V3Uc3R9QPkY1E6kxc=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\98whem6pgh-wuzd3f1y6v.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/netstandard#[.{fingerprint=wuzd3f1y6v}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\netstandard.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4l4mji57i8", "Integrity": "SLRIACbRkUXoCjQDdpp9LirIHrdinKXs3lqI3BdrMdo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\netstandard.wasm", "FileLength": 26233, "LastWriteTime": "2025-06-23T06:00:51.407423+00:00"}, "J7MUbavpruL4C17ZXU85sm7L7G3WjOa2IBQWTw59NBo=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\5aylwruku9-05ksnw82w3.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Private.CoreLib#[.{fingerprint=05ksnw82w3}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Private.CoreLib.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rxvosxm41u", "Integrity": "UShkkIOMntK28YIObvxfAAbRh3p9t/miDqz8JmKNPVM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Private.CoreLib.wasm", "FileLength": 1533168, "LastWriteTime": "2025-06-23T06:00:51.5586178+00:00"}, "Hgqrw3FZFMiJ7CpegkwXTxgiLb2xkDTCMH/tlcW3MII=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\7jar0yvv5d-vr46os3pyt.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/dotnet#[.{fingerprint=vr46os3pyt}]?.js.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\dotnet.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "z8vxgrbjrs", "Integrity": "jRffxLcG2kO4KvKQDS4JewXvtth/4aMckdAFsS7Ntr8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\dotnet.js", "FileLength": 12785, "LastWriteTime": "2025-06-23T06:00:51.3992971+00:00"}, "Si3/QZa/yS12kK8owR5bj3jXUFTCTQHJ1rpbNWvsKGI=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\p15yc418pl-es3ekshrlb.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/dotnet.js#[.{fingerprint=es3ekshrlb}]?.map.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\dotnet.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "dazcf3t92r", "Integrity": "W1djn9Nvf/ugFH4BHhvUKv/0tQsE5Mamf0CA8tRLQdw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\dotnet.js.map", "FileLength": 21303, "LastWriteTime": "2025-06-23T06:00:51.4033114+00:00"}, "hrFCTZxXcpz3HaX3qVqznPAY2772pi8B6+vp8CS0Oyk=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\rbkc9ujucj-rtblh4npr3.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/dotnet.native#[.{fingerprint=rtblh4npr3}]!.js.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\dotnet.native.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7hrrqjrs99", "Integrity": "2/kK375OV1gVSakmVUOOLQXTGCPzHIs/YTLd72uF7v4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\dotnet.native.js", "FileLength": 35025, "LastWriteTime": "2025-06-23T06:00:51.4134478+00:00"}, "j5Awyi2kufZyhj4tNXkSvRqaSBaINvDkSVY40jOAvwI=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\we8pxh1ekj-aqhezbunpl.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/dotnet.native#[.{fingerprint=aqhezbunpl}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\dotnet.native.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "912ednjrrt", "Integrity": "zdppwT9yq8U2F6jID4314pv4fL86vFWR6ukNtGDPxYg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\dotnet.native.wasm", "FileLength": 1198967, "LastWriteTime": "2025-06-23T06:00:51.506981+00:00"}, "v6rke7SKaW3olIooLs85MTznFU2Enm9/ekmrL0B6zOQ=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\iyinirezft-d1pzlaz2ez.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/dotnet.runtime#[.{fingerprint=d1pzlaz2ez}]!.js.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\dotnet.runtime.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fmigfovg0n", "Integrity": "R4Eu19IscrhtfS9W/5WbwUHDmFbT7M+2f4QVMNV7NeE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\dotnet.runtime.js", "FileLength": 56236, "LastWriteTime": "2025-06-23T06:00:51.5222491+00:00"}, "CTLmrchRTqNIT2GYQikvtzY52Rkt5GgJY67xZF6STXU=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\j1rnvbyur3-ctf2q9h8m2.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/dotnet.runtime.js#[.{fingerprint=ctf2q9h8m2}]?.map.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\dotnet.runtime.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bzl5x4v6yg", "Integrity": "h78GQCle4nEpvR7UesZohTc9u1Q0FUS8TJK6eQ0GiII=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\dotnet.runtime.js.map", "FileLength": 88605, "LastWriteTime": "2025-06-23T06:00:51.5352579+00:00"}, "6GYHdn2IE4e7Lx+atekLV1WZH6Wt4UMFOYrL9qKKECs=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\bw5jamie8q-tjcz0u77k5.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/icudt_CJK#[.{fingerprint=tjcz0u77k5}]!.dat.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\icudt_CJK.dat", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ffu5aujli6", "Integrity": "Sxt3k51yp5RyINpi7a/YQNnTWexafADiNQYBnBjQrYc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\icudt_CJK.dat", "FileLength": 359724, "LastWriteTime": "2025-06-23T06:00:51.5604085+00:00"}, "xJPARuimQOjNto/Zdx5ls4iEH6KCmEeZoGF4k8PgS00=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\ik2mr4q057-tptq2av103.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/icudt_EFIGS#[.{fingerprint=tptq2av103}]!.dat.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\icudt_EFIGS.dat", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kcp4n5gllp", "Integrity": "rLsEn/DgWXf7nRK2qegv2ARpYrcwixMOaXOoFcVmgjg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\icudt_EFIGS.dat", "FileLength": 220055, "LastWriteTime": "2025-06-23T06:00:51.3687634+00:00"}, "2n6j/7s1CMpdl2nIuuGL7te1j3MGhH0m8bh/aesHu4w=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\0sdup4ez0f-lfu7j35m59.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/icudt_no_CJK#[.{fingerprint=lfu7j35m59}]!.dat.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\icudt_no_CJK.dat", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "m3twp0zama", "Integrity": "UsST+ZWYFgogrz0pZB/4gGQWboPgRkurfdpi/0/ENCA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\icudt_no_CJK.dat", "FileLength": 347023, "LastWriteTime": "2025-06-23T06:00:51.4329453+00:00"}, "JvjmNEbzcRb4meBPCRDloHZuLqHk8HrUwdV/6MfzFow=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\8iiqvwjzvo-bleoxug9jp.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Baytara.Shared#[.{fingerprint=bleoxug9jp}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Baytara.Shared.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "u3xb2dncxe", "Integrity": "WoJEjxNcmIIWMa8nk7c8NM98IrkGF2VuHdtD932U6R4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Baytara.Shared.wasm", "FileLength": 28963, "LastWriteTime": "2025-06-23T06:59:25.1341694+00:00"}, "1jg6yhv7YhtouytZyv9ttPA/BQ7TM8NEHFcLjXS41Mc=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\lt97u3j6w9-df31ktafjd.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Baytara.Shared#[.{fingerprint=df31ktafjd}]!.pdb.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Baytara.Shared.pdb", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qy5knabi1d", "Integrity": "MmqBp3eYW81INJFvI1wfWIeYlUUHkrIV86X6tjh2H40=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Baytara.Shared.pdb", "FileLength": 16618, "LastWriteTime": "2025-06-23T06:59:25.1363223+00:00"}, "u8eH/kBW7wBXoJCa7q83dHxwzjO3Kb4slfAfeARmF8g=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\oka2omkyb2-jfd4jnpl89.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Baytara.Client#[.{fingerprint=jfd4jnpl89}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Baytara.Client.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "or4mikuwy1", "Integrity": "pMNjO7ftuV0PeLjcy+pHfXn5qxsF2Bk77BDQWRQuwPM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Baytara.Client.wasm", "FileLength": 102475, "LastWriteTime": "2025-06-23T17:27:43.7739085+00:00"}, "plPNMGRbU6ZMiVwe65xzGYmyzp2ysZ/t6SSPSXXO8fQ=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\51sg03yyfk-4964yp3j13.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Baytara.Client#[.{fingerprint=4964yp3j13}]!.pdb.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Baytara.Client.pdb", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "aitfp7ggks", "Integrity": "3odQ3Y4vG8ruuFgy0Nzt7tlBcj1o5GgGR6iHvEZGFaA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Baytara.Client.pdb", "FileLength": 113032, "LastWriteTime": "2025-06-23T17:27:43.7739085+00:00"}, "qjk2GElCqNdsUtu5ytM77r2pskBN5tDsGrZvZVihP7c=": {"Identity": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\gme93mt5pd-m35emu79i8.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "D:\\Baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/blazor.boot.json.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\blazor.boot.json", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "332ypqu0st", "Integrity": "D4DhL21VvT58NT/57q3RNELlS3J242t8Y/uL6Jhr5V4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\blazor.boot.json", "FileLength": 12547, "LastWriteTime": "2025-06-23T17:27:43.7739085+00:00"}}, "CachedCopyCandidates": {}}