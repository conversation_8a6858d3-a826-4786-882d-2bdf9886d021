using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using Baytara.API.Data;
using Baytara.Shared.DTOs;
using Baytara.Shared.Models;


namespace Baytara.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class TreatmentsController : ControllerBase
    {
        private readonly ApplicationDbContext _context;

        public TreatmentsController(ApplicationDbContext context)
        {
            _context = context;
        }

        [HttpGet]
        public async Task<ActionResult<IEnumerable<TreatmentDto>>> GetTreatments()
        {
            var treatments = await _context.Treatments
                .Include(t => t.Animal)
                    .ThenInclude(a => a.AnimalType)
                .Include(t => t.Breeder)
                .Include(t => t.Doctor)
                .Include(t => t.Branch)
                .Include(t => t.TreatmentMedicines)
                    .ThenInclude(tm => tm.Medicine)
                .OrderByDescending(t => t.CreatedAt)
                .ToListAsync();

            var treatmentDtos = treatments.Select(t => new TreatmentDto
            {
                Id = t.Id,
                AnimalId = t.AnimalId,
                AnimalName = t.Animal.Name ?? "",
                AnimalTypeName = t.Animal.AnimalType.Name,
                BreederId = t.BreederId,
                BreederName = t.Breeder.Name,
                DoctorId = t.DoctorId,
                DoctorName = t.Doctor.FullName,
                BranchId = t.BranchId,
                BranchName = t.Branch.Name,
                TreatmentDate = t.TreatmentDate,
                Symptoms = t.Symptoms,
                Diagnosis = t.Diagnosis,
                TreatmentPlan = t.TreatmentPlan,
                Notes = t.Notes,
                TotalCost = t.TotalCost,
                Status = t.Status,
                Medicines = t.TreatmentMedicines.Select(tm => new TreatmentMedicineDto
                {
                    Id = tm.TreatmentId,
                    MedicineId = tm.MedicineId,
                    MedicineName = tm.Medicine.Name,
                    Quantity = tm.Quantity,
                    UnitPrice = tm.UnitPrice
                }).ToList(),
                CreatedAt = t.CreatedAt,
                UpdatedAt = t.UpdatedAt
            }).ToList();

            return Ok(treatmentDtos);
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<TreatmentDto>> GetTreatment(int id)
        {
            var treatment = await _context.Treatments
                .Include(t => t.Animal)
                    .ThenInclude(a => a.AnimalType)
                .Include(t => t.Breeder)
                .Include(t => t.Doctor)
                .Include(t => t.Branch)
                .Include(t => t.TreatmentMedicines)
                    .ThenInclude(tm => tm.Medicine)
                .FirstOrDefaultAsync(t => t.Id == id);

            if (treatment == null)
            {
                return NotFound();
            }

            var treatmentDto = new TreatmentDto
            {
                Id = treatment.Id,
                AnimalId = treatment.AnimalId,
                AnimalName = treatment.Animal.Name ?? "",
                AnimalTypeName = treatment.Animal.AnimalType.Name,
                BreederId = treatment.BreederId,
                BreederName = treatment.Breeder.Name,
                DoctorId = treatment.DoctorId,
                DoctorName = treatment.Doctor.FullName,
                BranchId = treatment.BranchId,
                BranchName = treatment.Branch.Name,
                TreatmentDate = treatment.TreatmentDate,
                Symptoms = treatment.Symptoms,
                Diagnosis = treatment.Diagnosis,
                TreatmentPlan = treatment.TreatmentPlan,
                Notes = treatment.Notes,
                TotalCost = treatment.TotalCost,
                Status = treatment.Status,
                Medicines = treatment.TreatmentMedicines.Select(tm => new TreatmentMedicineDto
                {
                    Id = tm.TreatmentId,
                    MedicineId = tm.MedicineId,
                    MedicineName = tm.Medicine.Name,
                    Quantity = tm.Quantity,
                    UnitPrice = tm.UnitPrice
                }).ToList(),
                CreatedAt = treatment.CreatedAt,
                UpdatedAt = treatment.UpdatedAt
            };

            return Ok(treatmentDto);
        }

        [HttpPost]
        public async Task<ActionResult<TreatmentDto>> CreateTreatment(CreateTreatmentDto createTreatmentDto)
        {
            var treatment = new Treatment
            {
                AnimalId = createTreatmentDto.AnimalId,
                BreederId = createTreatmentDto.BreederId,
                DoctorId = createTreatmentDto.DoctorId,
                BranchId = createTreatmentDto.BranchId,
                TreatmentDate = DateTime.UtcNow,
                Symptoms = createTreatmentDto.Symptoms,
                Diagnosis = createTreatmentDto.Diagnosis,
                TreatmentPlan = createTreatmentDto.TreatmentPlan,
                Notes = createTreatmentDto.Notes,
                TotalCost = createTreatmentDto.TotalCost,
                Status = TreatmentStatus.Pending,
                CreatedAt = DateTime.UtcNow
            };

            _context.Treatments.Add(treatment);
            await _context.SaveChangesAsync();

            // Add medicines
            foreach (var medicineDto in createTreatmentDto.Medicines)
            {
                var treatmentMedicine = new TreatmentMedicine
                {
                    TreatmentId = treatment.Id,
                    MedicineId = medicineDto.MedicineId,
                    Quantity = medicineDto.Quantity,
                    UnitPrice = medicineDto.UnitPrice
                };

                _context.TreatmentMedicines.Add(treatmentMedicine);

                // Update medicine stock
                var medicine = await _context.Medicines.FindAsync(medicineDto.MedicineId);
                if (medicine != null)
                {
                    medicine.Quantity -= medicineDto.Quantity;
                    medicine.UpdatedAt = DateTime.UtcNow;
                }
            }

            await _context.SaveChangesAsync();

            return CreatedAtAction(nameof(GetTreatment), new { id = treatment.Id }, treatment);
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateTreatment(int id, UpdateTreatmentDto updateTreatmentDto)
        {
            var treatment = await _context.Treatments
                .Include(t => t.TreatmentMedicines)
                .FirstOrDefaultAsync(t => t.Id == id);

            if (treatment == null)
            {
                return NotFound();
            }

            treatment.Symptoms = updateTreatmentDto.Symptoms;
            treatment.Diagnosis = updateTreatmentDto.Diagnosis;
            treatment.TreatmentPlan = updateTreatmentDto.TreatmentPlan;
            treatment.Notes = updateTreatmentDto.Notes;
            treatment.TotalCost = updateTreatmentDto.TotalCost;
            treatment.Status = updateTreatmentDto.Status;
            treatment.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            return NoContent();
        }

        [HttpPut("{id}/status")]
        public async Task<IActionResult> UpdateTreatmentStatus(int id, [FromBody] TreatmentStatus status)
        {
            var treatment = await _context.Treatments.FindAsync(id);

            if (treatment == null)
            {
                return NotFound();
            }

            treatment.Status = status;
            treatment.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            return NoContent();
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteTreatment(int id)
        {
            var treatment = await _context.Treatments
                .Include(t => t.TreatmentMedicines)
                .FirstOrDefaultAsync(t => t.Id == id);

            if (treatment == null)
            {
                return NotFound();
            }

            // Restore medicine stock
            foreach (var treatmentMedicine in treatment.TreatmentMedicines)
            {
                var medicine = await _context.Medicines.FindAsync(treatmentMedicine.MedicineId);
                if (medicine != null)
                {
                    medicine.Quantity += treatmentMedicine.Quantity;
                    medicine.UpdatedAt = DateTime.UtcNow;
                }
            }

            _context.Treatments.Remove(treatment);
            await _context.SaveChangesAsync();

            return NoContent();
        }

        [HttpGet("by-breeder/{breederId}")]
        public async Task<ActionResult<IEnumerable<TreatmentDto>>> GetTreatmentsByBreeder(int breederId)
        {
            var treatments = await _context.Treatments
                .Include(t => t.Animal)
                    .ThenInclude(a => a.AnimalType)
                .Include(t => t.Breeder)
                .Include(t => t.Doctor)
                .Include(t => t.Branch)
                .Include(t => t.TreatmentMedicines)
                    .ThenInclude(tm => tm.Medicine)
                .Where(t => t.BreederId == breederId)
                .OrderByDescending(t => t.CreatedAt)
                .ToListAsync();

            var treatmentDtos = treatments.Select(t => new TreatmentDto
            {
                Id = t.Id,
                AnimalId = t.AnimalId,
                AnimalName = t.Animal.Name ?? "",
                AnimalTypeName = t.Animal.AnimalType.Name,
                BreederId = t.BreederId,
                BreederName = t.Breeder.Name,
                DoctorId = t.DoctorId,
                DoctorName = t.Doctor.FullName,
                BranchId = t.BranchId,
                BranchName = t.Branch.Name,
                TreatmentDate = t.TreatmentDate,
                Symptoms = t.Symptoms,
                Diagnosis = t.Diagnosis,
                TreatmentPlan = t.TreatmentPlan,
                Notes = t.Notes,
                TotalCost = t.TotalCost,
                Status = t.Status,
                Medicines = t.TreatmentMedicines.Select(tm => new TreatmentMedicineDto
                {
                    Id = tm.TreatmentId,
                    MedicineId = tm.MedicineId,
                    MedicineName = tm.Medicine.Name,
                    Quantity = tm.Quantity,
                    UnitPrice = tm.UnitPrice
                }).ToList(),
                CreatedAt = t.CreatedAt,
                UpdatedAt = t.UpdatedAt
            }).ToList();

            return Ok(treatmentDtos);
        }

        [HttpGet("by-animal/{animalId}")]
        public async Task<ActionResult<IEnumerable<TreatmentDto>>> GetTreatmentsByAnimal(int animalId)
        {
            var treatments = await _context.Treatments
                .Include(t => t.Animal)
                    .ThenInclude(a => a.AnimalType)
                .Include(t => t.Breeder)
                .Include(t => t.Doctor)
                .Include(t => t.Branch)
                .Include(t => t.TreatmentMedicines)
                    .ThenInclude(tm => tm.Medicine)
                .Where(t => t.AnimalId == animalId)
                .OrderByDescending(t => t.CreatedAt)
                .ToListAsync();

            var treatmentDtos = treatments.Select(t => new TreatmentDto
            {
                Id = t.Id,
                AnimalId = t.AnimalId,
                AnimalName = t.Animal.Name ?? "",
                AnimalTypeName = t.Animal.AnimalType.Name,
                BreederId = t.BreederId,
                BreederName = t.Breeder.Name,
                DoctorId = t.DoctorId,
                DoctorName = t.Doctor.FullName,
                BranchId = t.BranchId,
                BranchName = t.Branch.Name,
                TreatmentDate = t.TreatmentDate,
                Symptoms = t.Symptoms,
                Diagnosis = t.Diagnosis,
                TreatmentPlan = t.TreatmentPlan,
                Notes = t.Notes,
                TotalCost = t.TotalCost,
                Status = t.Status,
                Medicines = t.TreatmentMedicines.Select(tm => new TreatmentMedicineDto
                {
                    Id = tm.TreatmentId,
                    MedicineId = tm.MedicineId,
                    MedicineName = tm.Medicine.Name,
                    Quantity = tm.Quantity,
                    UnitPrice = tm.UnitPrice
                }).ToList(),
                CreatedAt = t.CreatedAt,
                UpdatedAt = t.UpdatedAt
            }).ToList();

            return Ok(treatmentDtos);
        }
    }
}
