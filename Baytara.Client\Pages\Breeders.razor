@page "/breeders"
@using Baytara.Client.Services
@using Baytara.Shared.DTOs
@inject IApiService ApiService
@inject IJSRuntime JSRuntime

<PageTitle>المربين - بيطره</PageTitle>

<div class="breeders-page">
    <div class="page-header">
        <div class="header-content">
            <h1 class="page-title">
                <i class="fas fa-users"></i>
                إدارة المربين
            </h1>
            <p class="page-subtitle">إدارة بيانات المربين وحيواناتهم</p>
        </div>
        <div class="header-actions">
            <button class="btn btn-primary" @onclick="ShowAddBreederModal">
                <i class="fas fa-plus"></i>
                إضافة مربي جديد
            </button>
        </div>
    </div>

    @if (isLoading)
    {
        <div class="loading-container">
            <div class="spinner">
                <i class="fas fa-spinner fa-spin"></i>
            </div>
            <p>جاري تحميل البيانات...</p>
        </div>
    }
    else if (breeders.Any())
    {
        <div class="table-container">
            <div class="table-wrapper">
                <table class="breeders-table">
                    <thead>
                        <tr>
                            <th><i class="fas fa-user"></i> اسم المربي</th>
                            <th><i class="fas fa-phone"></i> رقم الهاتف</th>
                            <th><i class="fas fa-id-card"></i> الرقم المدني</th>
                            <th><i class="fas fa-map-marker-alt"></i> المكان</th>
                            <th><i class="fas fa-paw"></i> الحيوانات</th>
                            <th><i class="fas fa-stethoscope"></i> العلاجات</th>
                            <th><i class="fas fa-calendar"></i> تاريخ التسجيل</th>
                            <th><i class="fas fa-cogs"></i> الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var breeder in breeders)
                        {
                            <tr class="breeder-row">
                                <td class="breeder-name">
                                    <div class="name-cell">
                                        <div class="avatar">
                                            <i class="fas fa-user"></i>
                                        </div>
                                        <span>@breeder.Name</span>
                                    </div>
                                </td>
                                <td class="phone-cell">
                                    @if (!string.IsNullOrEmpty(breeder.PhoneNumber))
                                    {
                                        <span class="phone-number">@breeder.PhoneNumber</span>
                                    }
                                    else
                                    {
                                        <span class="no-data">غير محدد</span>
                                    }
                                </td>
                                <td class="id-cell">
                                    @if (!string.IsNullOrEmpty(breeder.NationalId))
                                    {
                                        <span class="national-id">@breeder.NationalId</span>
                                    }
                                    else
                                    {
                                        <span class="no-data">غير محدد</span>
                                    }
                                </td>
                                <td class="location-cell">
                                    @if (!string.IsNullOrEmpty(breeder.Location))
                                    {
                                        <span class="location">@breeder.Location</span>
                                    }
                                    else
                                    {
                                        <span class="no-data">غير محدد</span>
                                    }
                                </td>
                                <td class="count-cell">
                                    <span class="count-badge animals">@breeder.AnimalsCount</span>
                                </td>
                                <td class="count-cell">
                                    <span class="count-badge treatments">@breeder.TreatmentsCount</span>
                                </td>
                                <td class="date-cell">
                                    <span class="date">@breeder.CreatedAt.ToString("dd/MM/yyyy")</span>
                                </td>
                                <td class="actions-cell">
                                    <div class="action-buttons">
                                        <button class="btn btn-icon btn-info" @onclick="() => ViewBreederDetails(breeder.Id)" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-icon btn-success" @onclick="() => ShowAddAnimalModal(breeder.Id)" title="إضافة حيوان">
                                            <i class="fas fa-plus"></i>
                                        </button>
                                        <button class="btn btn-icon btn-warning" @onclick="() => ShowEditBreederModal(breeder)" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-icon btn-danger" @onclick="() => DeleteBreeder(breeder.Id)" title="حذف">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    }
    else
    {
        <div class="empty-state">
            <i class="fas fa-users"></i>
            <h3>لا يوجد مربين مسجلين</h3>
            <p>ابدأ بإضافة مربي جديد لإدارة الحيوانات</p>
            <button class="btn btn-primary" @onclick="ShowAddBreederModal">
                <i class="fas fa-plus"></i>
                إضافة مربي جديد
            </button>
        </div>
    }
</div>

<!-- Add/Edit Breeder Modal -->
@if (showBreederModal)
{
    <div class="modal-overlay" @onclick="CloseBreederModal">
        <div class="modal-content" @onclick:stopPropagation="true">
            <div class="modal-header">
                <h3>@(editingBreeder == null ? "إضافة مربي جديد" : "تعديل بيانات المربي")</h3>
                <button class="btn btn-close" @onclick="CloseBreederModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <div class="modal-body">
                <EditForm Model="@breederForm" OnValidSubmit="@SaveBreeder">
                    <DataAnnotationsValidator />
                    
                    <div class="form-group">
                        <label for="name">اسم المربي *</label>
                        <InputText id="name" @bind-Value="breederForm.Name" class="form-control" placeholder="أدخل اسم المربي" />
                        <ValidationMessage For="@(() => breederForm.Name)" />
                    </div>
                    
                    <div class="form-group">
                        <label for="phone">رقم الهاتف</label>
                        <InputText id="phone" @bind-Value="breederForm.PhoneNumber" class="form-control" placeholder="أدخل رقم الهاتف" />
                        <ValidationMessage For="@(() => breederForm.PhoneNumber)" />
                    </div>
                    
                    <div class="form-group">
                        <label for="nationalId">الرقم المدني</label>
                        <InputText id="nationalId" @bind-Value="breederForm.NationalId" class="form-control" placeholder="أدخل الرقم المدني" />
                        <ValidationMessage For="@(() => breederForm.NationalId)" />
                    </div>
                    
                    <div class="form-group">
                        <label for="location">المكان</label>
                        <InputText id="location" @bind-Value="breederForm.Location" class="form-control" placeholder="أدخل المكان" />
                        <ValidationMessage For="@(() => breederForm.Location)" />
                    </div>
                    
                    <div class="modal-actions">
                        <button type="button" class="btn btn-secondary" @onclick="CloseBreederModal">إلغاء</button>
                        <button type="submit" class="btn btn-primary" disabled="@isSaving">
                            @if (isSaving)
                            {
                                <i class="fas fa-spinner fa-spin"></i>
                                <span>جاري الحفظ...</span>
                            }
                            else
                            {
                                <i class="fas fa-save"></i>
                                <span>حفظ</span>
                            }
                        </button>
                    </div>
                </EditForm>
            </div>
        </div>
    </div>
}

<!-- Add Animal Modal -->
@if (showAddAnimalModal)
{
    <div class="modal-overlay" @onclick="CloseAddAnimalModal">
        <div class="modal-content" @onclick:stopPropagation="true">
            <div class="modal-header">
                <h3>إضافة حيوان جديد</h3>
                <button class="btn btn-close" @onclick="CloseAddAnimalModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <div class="modal-body">
                <EditForm Model="@animalForm" OnValidSubmit="@SaveAnimal">
                    <DataAnnotationsValidator />
                    
                    <div class="form-group">
                        <label for="animalType">نوع الحيوان *</label>
                        <InputSelect id="animalType" @bind-Value="animalForm.AnimalTypeId" class="form-control">
                            <option value="0">اختر نوع الحيوان</option>
                            @foreach (var animalType in animalTypes)
                            {
                                <option value="@animalType.Id">@animalType.Name</option>
                            }
                        </InputSelect>
                        <ValidationMessage For="@(() => animalForm.AnimalTypeId)" />
                    </div>
                    
                    <div class="form-group">
                        <label for="animalName">اسم الحيوان</label>
                        <InputText id="animalName" @bind-Value="animalForm.Name" class="form-control" placeholder="أدخل اسم الحيوان (اختياري)" />
                        <ValidationMessage For="@(() => animalForm.Name)" />
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="gender">الجنس *</label>
                            <InputSelect id="gender" @bind-Value="animalForm.Gender" class="form-control">
                                <option value="1">ذكر</option>
                                <option value="2">أنثى</option>
                            </InputSelect>
                            <ValidationMessage For="@(() => animalForm.Gender)" />
                        </div>
                        
                        <div class="form-group">
                            <label for="age">العمر</label>
                            <InputNumber id="age" @bind-Value="animalForm.Age" class="form-control" placeholder="العمر بالسنوات" />
                            <ValidationMessage For="@(() => animalForm.Age)" />
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="notes">ملاحظات</label>
                        <InputTextArea id="notes" @bind-Value="animalForm.Notes" class="form-control" rows="3" placeholder="أدخل أي ملاحظات إضافية" />
                        <ValidationMessage For="@(() => animalForm.Notes)" />
                    </div>
                    
                    <div class="modal-actions">
                        <button type="button" class="btn btn-secondary" @onclick="CloseAddAnimalModal">إلغاء</button>
                        <button type="submit" class="btn btn-primary" disabled="@isSaving">
                            @if (isSaving)
                            {
                                <i class="fas fa-spinner fa-spin"></i>
                                <span>جاري الحفظ...</span>
                            }
                            else
                            {
                                <i class="fas fa-save"></i>
                                <span>حفظ</span>
                            }
                        </button>
                    </div>
                </EditForm>
            </div>
        </div>
    </div>
}

<style>
    .breeders-page {
        width: 100%;
        max-width: none;
        margin: 0;
        padding: 0;
        min-height: calc(100vh - 120px);
        color: white;
    }

    .page-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding: 20px 0;
        border-bottom: 2px solid #eee;
        width: 100%;
    }

    .header-content h1 {
        font-size: 2.5rem;
        color: white;
        margin-bottom: 8px;
        display: flex;
        align-items: center;
        gap: 15px;
        font-weight: 700;
        text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    }

    .page-subtitle {
        font-size: 1.2rem;
        color: rgba(255, 255, 255, 0.8);
        margin-bottom: 0;
        font-weight: 400;
    }

    .btn {
        padding: 10px 20px;
        border: none;
        border-radius: 8px;
        cursor: pointer;
        font-size: 0.9rem;
        font-weight: 500;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 8px;
        text-decoration: none;
    }

    .btn-primary {
        background: rgba(255, 255, 255, 0.2);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.3);
        color: white;
        font-weight: 600;
    }

    .btn-primary:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        background: rgba(255, 255, 255, 0.3);
    }

    .btn-icon {
        padding: 8px;
        width: 36px;
        height: 36px;
        border-radius: 50%;
        justify-content: center;
    }

    /* Responsive Design */
    @media (max-width: 1200px) {
        .breeders-table th,
        .breeders-table td {
            padding: 12px 10px;
            font-size: 0.9rem;
        }

        .avatar {
            width: 35px;
            height: 35px;
            font-size: 0.9rem;
        }
    }

    @media (max-width: 992px) {
        .table-wrapper {
            max-height: calc(100vh - 180px);
        }

        .breeders-table th,
        .breeders-table td {
            padding: 10px 8px;
            font-size: 0.85rem;
        }

        .btn-icon {
            width: 32px;
            height: 32px;
            padding: 6px;
        }

        .action-buttons {
            gap: 6px;
        }
    }

    @media (max-width: 768px) {
        .page-header {
            flex-direction: column;
            gap: 15px;
            text-align: center;
        }

        .table-wrapper {
            max-height: calc(100vh - 160px);
        }

        .breeders-table {
            font-size: 0.8rem;
        }

        .breeders-table th,
        .breeders-table td {
            padding: 8px 6px;
        }

        .name-cell {
            flex-direction: column;
            gap: 8px;
            text-align: center;
        }

        .avatar {
            width: 30px;
            height: 30px;
            font-size: 0.8rem;
        }

        .action-buttons {
            flex-direction: column;
            gap: 4px;
        }

        .btn-icon {
            width: 28px;
            height: 28px;
            padding: 4px;
            font-size: 0.8rem;
        }
    }

    @media (max-width: 480px) {
        .breeders-table th:nth-child(3),
        .breeders-table td:nth-child(3),
        .breeders-table th:nth-child(4),
        .breeders-table td:nth-child(4) {
            display: none;
        }

        .table-wrapper {
            max-height: calc(100vh - 140px);
        }
    }

    .btn-info { background: #17a2b8; color: white; }
    .btn-success { background: #28a745; color: white; }
    .btn-warning { background: #ffc107; color: white; }
    .btn-danger { background: #dc3545; color: white; }
    .btn-secondary { background: #6c757d; color: white; }

    .loading-container {
        text-align: center;
        padding: 60px 20px;
        color: #666;
    }

    .loading-container .spinner {
        font-size: 3rem;
        color: #667eea;
        margin-bottom: 20px;
    }

    /* Table Container */
    .table-container {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 20px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        width: 100%;
        margin: 0;
    }

    .table-wrapper {
        overflow-x: auto;
        overflow-y: auto;
        max-height: calc(100vh - 180px);
        width: 100%;
    }

    /* Breeders Table */
    .breeders-table {
        width: 100%;
        border-collapse: collapse;
        font-size: 0.95rem;
        background: white;
    }

    .breeders-table thead {
        background: rgba(255, 255, 255, 0.2);
        backdrop-filter: blur(10px);
        color: white;
        position: sticky;
        top: 0;
        z-index: 10;
    }

    .breeders-table th {
        padding: 20px 18px;
        text-align: right;
        font-weight: 700;
        font-size: 1.1rem;
        border-bottom: 2px solid rgba(255, 255, 255, 0.3);
        white-space: nowrap;
        text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
    }

    .breeders-table th i {
        margin-left: 8px;
        opacity: 0.9;
    }

    .breeders-table tbody tr {
        transition: all 0.3s ease;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        background: rgba(255, 255, 255, 0.05);
    }

    .breeders-table tbody tr:hover {
        background: rgba(255, 255, 255, 0.15);
        transform: scale(1.02);
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
    }

    .breeders-table td {
        padding: 18px 15px;
        text-align: right;
        vertical-align: middle;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        color: white;
    }

    /* Table Cell Styles */
    .name-cell {
        display: flex;
        align-items: center;
        gap: 15px;
        font-weight: 600;
        color: white;
    }

    .avatar {
        width: 45px;
        height: 45px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.2);
        backdrop-filter: blur(10px);
        border: 2px solid rgba(255, 255, 255, 0.3);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.1rem;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    }

    .phone-number, .national-id, .location {
        color: rgba(255, 255, 255, 0.9);
        font-weight: 500;
    }

    .no-data {
        color: rgba(255, 255, 255, 0.5);
        font-style: italic;
        font-size: 0.9rem;
    }

    .count-badge {
        display: inline-block;
        padding: 6px 12px;
        border-radius: 20px;
        font-weight: 600;
        font-size: 0.9rem;
        text-align: center;
        min-width: 40px;
    }

    .count-badge.animals {
        background: rgba(40, 167, 69, 0.2);
        color: #4ade80;
        border: 1px solid rgba(40, 167, 69, 0.4);
        backdrop-filter: blur(10px);
    }

    .count-badge.treatments {
        background: rgba(59, 130, 246, 0.2);
        color: #60a5fa;
        border: 1px solid rgba(59, 130, 246, 0.4);
        backdrop-filter: blur(10px);
    }

    .date {
        color: rgba(255, 255, 255, 0.7);
        font-size: 0.9rem;
        font-weight: 500;
    }

    .action-buttons {
        display: flex;
        gap: 8px;
        justify-content: center;
    }



    /* Empty State */
    .empty-state {
        text-align: center;
        padding: 80px 20px;
        color: #666;
    }

    .empty-state i {
        font-size: 4rem;
        color: #ddd;
        margin-bottom: 20px;
    }

    .empty-state h3 {
        font-size: 1.5rem;
        margin-bottom: 10px;
        color: #333;
    }

    .empty-state p {
        font-size: 1rem;
        margin-bottom: 30px;
    }

    /* Modal Styles */
    .modal-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1000;
        backdrop-filter: blur(5px);
    }

    .modal-content {
        background: white;
        border-radius: 15px;
        width: 90%;
        max-width: 500px;
        max-height: 90vh;
        overflow-y: auto;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    }

    .modal-header {
        padding: 20px 25px;
        border-bottom: 1px solid #eee;
        display: flex;
        justify-content: space-between;
        align-items: center;
        background: #f8f9fa;
        border-radius: 15px 15px 0 0;
    }

    .modal-header h3 {
        font-size: 1.3rem;
        color: #333;
        margin-bottom: 0;
    }

    .btn-close {
        background: none;
        border: none;
        font-size: 1.2rem;
        color: #666;
        cursor: pointer;
        padding: 5px;
        border-radius: 50%;
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .btn-close:hover {
        background: #e9ecef;
        color: #333;
    }

    .modal-body {
        padding: 25px;
    }

    .form-group {
        margin-bottom: 20px;
    }

    .form-group label {
        display: block;
        margin-bottom: 8px;
        font-weight: 500;
        color: #333;
    }

    .form-control {
        width: 100%;
        padding: 12px 15px;
        border: 2px solid #e9ecef;
        border-radius: 8px;
        font-size: 1rem;
        transition: border-color 0.3s ease;
    }

    .form-control:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }

    .form-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 15px;
    }

    .modal-actions {
        display: flex;
        gap: 15px;
        justify-content: flex-end;
        margin-top: 25px;
        padding-top: 20px;
        border-top: 1px solid #eee;
    }

    .validation-message {
        color: #dc3545;
        font-size: 0.875rem;
        margin-top: 5px;
        display: block;
    }
</style>

@code {
    private List<BreederDto> breeders = new();
    private List<AnimalTypeDto> animalTypes = new();
    private bool isLoading = true;
    private bool isSaving = false;

    // Modal states
    private bool showBreederModal = false;
    private bool showAddAnimalModal = false;
    private BreederDto? editingBreeder = null;
    private int selectedBreederId = 0;

    // Forms
    private CreateBreederDto breederForm = new();
    private CreateAnimalDto animalForm = new();

    protected override async Task OnInitializedAsync()
    {
        await LoadData();
    }

    private async Task LoadData()
    {
        try
        {
            isLoading = true;
            StateHasChanged();

            var breedersTask = ApiService.GetBreedersAsync();
            var animalTypesTask = ApiService.GetAnimalTypesAsync();

            await Task.WhenAll(breedersTask, animalTypesTask);

            breeders = (await breedersTask).ToList();
            animalTypes = (await animalTypesTask).ToList();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", "حدث خطأ في تحميل البيانات");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private void ShowAddBreederModal()
    {
        editingBreeder = null;
        breederForm = new CreateBreederDto();
        showBreederModal = true;
    }

    private void ShowEditBreederModal(BreederDto breeder)
    {
        editingBreeder = breeder;
        breederForm = new CreateBreederDto
        {
            Name = breeder.Name,
            PhoneNumber = breeder.PhoneNumber,
            NationalId = breeder.NationalId,
            Location = breeder.Location,
            BranchId = breeder.BranchId
        };
        showBreederModal = true;
    }

    private void CloseBreederModal()
    {
        showBreederModal = false;
        editingBreeder = null;
        breederForm = new CreateBreederDto();
    }

    private async Task SaveBreeder()
    {
        try
        {
            isSaving = true;
            StateHasChanged();

            if (editingBreeder == null)
            {
                var result = await ApiService.CreateBreederAsync(breederForm);
                if (result != null)
                {
                    breeders.Insert(0, result);
                    CloseBreederModal();
                    await JSRuntime.InvokeVoidAsync("alert", "تم إضافة المربي بنجاح");
                }
                else
                {
                    await JSRuntime.InvokeVoidAsync("alert", "حدث خطأ في إضافة المربي");
                }
            }
            else
            {
                var updateDto = new UpdateBreederDto
                {
                    Name = breederForm.Name,
                    PhoneNumber = breederForm.PhoneNumber,
                    NationalId = breederForm.NationalId,
                    Location = breederForm.Location,
                    BranchId = breederForm.BranchId,
                    IsActive = true
                };

                var result = await ApiService.UpdateBreederAsync(editingBreeder.Id, updateDto);
                if (result != null)
                {
                    var index = breeders.FindIndex(b => b.Id == editingBreeder.Id);
                    if (index >= 0)
                    {
                        breeders[index] = result;
                    }
                    CloseBreederModal();
                    await JSRuntime.InvokeVoidAsync("alert", "تم تحديث بيانات المربي بنجاح");
                }
                else
                {
                    await JSRuntime.InvokeVoidAsync("alert", "حدث خطأ في تحديث بيانات المربي");
                }
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", "حدث خطأ غير متوقع");
        }
        finally
        {
            isSaving = false;
            StateHasChanged();
        }
    }

    private async Task DeleteBreeder(int breederId)
    {
        var confirmed = await JSRuntime.InvokeAsync<bool>("confirm", "هل أنت متأكد من حذف هذا المربي؟");
        if (confirmed)
        {
            try
            {
                var success = await ApiService.DeleteBreederAsync(breederId);
                if (success)
                {
                    breeders.RemoveAll(b => b.Id == breederId);
                    StateHasChanged();
                    await JSRuntime.InvokeVoidAsync("alert", "تم حذف المربي بنجاح");
                }
                else
                {
                    await JSRuntime.InvokeVoidAsync("alert", "حدث خطأ في حذف المربي");
                }
            }
            catch (Exception ex)
            {
                await JSRuntime.InvokeVoidAsync("alert", "حدث خطأ غير متوقع");
            }
        }
    }

    private void ShowAddAnimalModal(int breederId)
    {
        selectedBreederId = breederId;
        animalForm = new CreateAnimalDto { BreederId = breederId };
        showAddAnimalModal = true;
    }

    private void CloseAddAnimalModal()
    {
        showAddAnimalModal = false;
        selectedBreederId = 0;
        animalForm = new CreateAnimalDto();
    }

    private async Task SaveAnimal()
    {
        try
        {
            isSaving = true;
            StateHasChanged();

            var result = await ApiService.CreateAnimalAsync(animalForm);
            if (result != null)
            {
                // Update breeder's animal count
                var breeder = breeders.FirstOrDefault(b => b.Id == selectedBreederId);
                if (breeder != null)
                {
                    breeder.AnimalsCount++;
                }

                CloseAddAnimalModal();
                await JSRuntime.InvokeVoidAsync("alert", "تم إضافة الحيوان بنجاح");
            }
            else
            {
                await JSRuntime.InvokeVoidAsync("alert", "حدث خطأ في إضافة الحيوان");
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", "حدث خطأ غير متوقع");
        }
        finally
        {
            isSaving = false;
            StateHasChanged();
        }
    }

    private async Task ViewBreederDetails(int breederId)
    {
        // Navigate to breeder details page or show detailed modal
        await JSRuntime.InvokeVoidAsync("alert", $"عرض تفاصيل المربي رقم {breederId}");
    }
}
