@page "/treatment"
@using Baytara.Client.Services
@using Baytara.Shared.DTOs
@using Baytara.Shared.Models
@inject IApiService ApiService
@inject IJSRuntime JSRuntime

<PageTitle>العلاج - بيطره</PageTitle>

<div class="treatment-page">
    <div class="page-header">
        <div class="header-content">
            <h1 class="page-title">
                <i class="fas fa-stethoscope"></i>
                إدارة العلاج
            </h1>
            <p class="page-subtitle">تشخيص وعلاج الحيوانات وصرف الأدوية</p>
        </div>
        <div class="header-actions">
            <div class="filter-buttons">
                <button class="btn @(currentFilter == "all" ? "btn-primary" : "btn-outline")" @onclick="@(() => FilterTreatments("all"))">
                    <i class="fas fa-list"></i>
                    جميع العلاجات
                </button>
                <button class="btn @(currentFilter == "pending" ? "btn-warning" : "btn-outline")" @onclick="@(() => FilterTreatments("pending"))">
                    <i class="fas fa-clock"></i>
                    معلقة
                </button>
                <button class="btn @(currentFilter == "completed" ? "btn-success" : "btn-outline")" @onclick="@(() => FilterTreatments("completed"))">
                    <i class="fas fa-check"></i>
                    مكتملة
                </button>
            </div>
            <button class="btn btn-primary" @onclick="ShowNewTreatmentModal">
                <i class="fas fa-plus"></i>
                علاج جديد
            </button>
        </div>
    </div>

    @if (isLoading)
    {
        <div class="loading-container">
            <div class="spinner">
                <i class="fas fa-spinner fa-spin"></i>
            </div>
            <p>جاري تحميل البيانات...</p>
        </div>
    }
    else if (filteredTreatments.Any())
    {
        <div class="treatments-grid">
            @foreach (var treatment in filteredTreatments)
            {
                <div class="treatment-card @GetTreatmentCardClass(treatment.Status)">
                    <div class="card-header">
                        <div class="treatment-info">
                            <h3 class="treatment-title">
                                @treatment.AnimalTypeName
                                @if (!string.IsNullOrEmpty(treatment.AnimalName))
                                {
                                    <span class="animal-name">- @treatment.AnimalName</span>
                                }
                            </h3>
                            <p class="breeder-name">المربي: @treatment.BreederName</p>
                            <p class="doctor-name">الطبيب: @treatment.DoctorName</p>
                        </div>
                        <div class="treatment-status">
                            <span class="status-badge @GetStatusClass(treatment.Status)">
                                <i class="@GetStatusIcon(treatment.Status)"></i>
                                @GetStatusText(treatment.Status)
                            </span>
                        </div>
                    </div>
                    
                    <div class="card-body">
                        @if (!string.IsNullOrEmpty(treatment.Symptoms))
                        {
                            <div class="treatment-section">
                                <h4><i class="fas fa-thermometer-half"></i> الأعراض</h4>
                                <p>@treatment.Symptoms</p>
                            </div>
                        }
                        
                        @if (!string.IsNullOrEmpty(treatment.Diagnosis))
                        {
                            <div class="treatment-section">
                                <h4><i class="fas fa-search"></i> التشخيص</h4>
                                <p>@treatment.Diagnosis</p>
                            </div>
                        }
                        
                        @if (!string.IsNullOrEmpty(treatment.TreatmentPlan))
                        {
                            <div class="treatment-section">
                                <h4><i class="fas fa-clipboard-list"></i> خطة العلاج</h4>
                                <p>@treatment.TreatmentPlan</p>
                            </div>
                        }
                        
                        @if (treatment.Medicines.Any())
                        {
                            <div class="treatment-section">
                                <h4><i class="fas fa-pills"></i> الأدوية المصروفة</h4>
                                <div class="medicines-list">
                                    @foreach (var medicine in treatment.Medicines)
                                    {
                                        <div class="medicine-item">
                                            <span class="medicine-name">@medicine.MedicineName</span>
                                            <span class="medicine-quantity">@medicine.Quantity وحدة</span>
                                            <span class="medicine-price">@medicine.TotalPrice.ToString("N2") ر.س</span>
                                        </div>
                                    }
                                </div>
                            </div>
                        }
                        
                        <div class="treatment-footer">
                            <div class="cost-info">
                                <span class="total-cost">
                                    <i class="fas fa-money-bill-wave"></i>
                                    التكلفة الإجمالية: @treatment.TotalCost.ToString("N2") ر.س
                                </span>
                            </div>
                            <div class="date-info">
                                <small class="text-muted">
                                    تاريخ العلاج: @treatment.TreatmentDate.ToString("dd/MM/yyyy HH:mm")
                                </small>
                            </div>
                        </div>
                        
                        <div class="card-actions">
                            @if (treatment.Status == TreatmentStatus.Pending || treatment.Status == TreatmentStatus.InProgress)
                            {
                                <button class="btn btn-success btn-sm" @onclick="() => CompleteTreatment(treatment.Id)">
                                    <i class="fas fa-check"></i>
                                    إكمال العلاج
                                </button>
                            }
                            <button class="btn btn-info btn-sm" @onclick="() => ViewTreatmentDetails(treatment.Id)">
                                <i class="fas fa-eye"></i>
                                عرض التفاصيل
                            </button>
                            <button class="btn btn-warning btn-sm" @onclick="() => ShowEditTreatmentModal(treatment)">
                                <i class="fas fa-edit"></i>
                                تعديل
                            </button>
                        </div>
                    </div>
                </div>
            }
        </div>
    }
    else
    {
        <div class="empty-state">
            <i class="fas fa-stethoscope"></i>
            <h3>@GetEmptyStateTitle()</h3>
            <p>@GetEmptyStateMessage()</p>
            @if (currentFilter == "all")
            {
                <button class="btn btn-primary" @onclick="ShowNewTreatmentModal">
                    <i class="fas fa-plus"></i>
                    بدء علاج جديد
                </button>
            }
        </div>
    }
</div>

<!-- New/Edit Treatment Modal -->
@if (showTreatmentModal)
{
    <div class="modal-overlay" @onclick="CloseTreatmentModal">
        <div class="modal-content large-modal" @onclick:stopPropagation="true">
            <div class="modal-header">
                <h3>@(editingTreatment == null ? "علاج جديد" : "تعديل العلاج")</h3>
                <button class="btn btn-close" @onclick="CloseTreatmentModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <div class="modal-body">
                <EditForm Model="@treatmentForm" OnValidSubmit="@SaveTreatment">
                    <DataAnnotationsValidator />
                    
                    <!-- Step 1: Animal Selection -->
                    @if (currentStep == 1)
                    {
                        <div class="step-content">
                            <h4 class="step-title">
                                <i class="fas fa-paw"></i>
                                اختيار الحيوان
                            </h4>
                            
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="breeder">المربي *</label>
                                    <InputSelect id="breeder" @bind-Value="selectedBreederId" class="form-control" @onchange="OnBreederChanged">
                                        <option value="0">اختر المربي</option>
                                        @foreach (var breeder in breeders)
                                        {
                                            <option value="@breeder.Id">@breeder.Name</option>
                                        }
                                    </InputSelect>
                                </div>
                                
                                <div class="form-group">
                                    <label for="animal">الحيوان *</label>
                                    <InputSelect id="animal" @bind-Value="treatmentForm.AnimalId" class="form-control">
                                        <option value="0">اختر الحيوان</option>
                                        @foreach (var animal in breederAnimals)
                                        {
                                            <option value="@animal.Id">@animal.AnimalTypeName @(!string.IsNullOrEmpty(animal.Name) ? $"- {animal.Name}" : "")</option>
                                        }
                                    </InputSelect>
                                    <ValidationMessage For="@(() => treatmentForm.AnimalId)" />
                                </div>
                            </div>
                        </div>
                    }
                    
                    <!-- Step 2: Diagnosis -->
                    @if (currentStep == 2)
                    {
                        <div class="step-content">
                            <h4 class="step-title">
                                <i class="fas fa-stethoscope"></i>
                                التشخيص والأعراض
                            </h4>
                            
                            <div class="form-group">
                                <label for="symptoms">الأعراض</label>
                                <InputTextArea id="symptoms" @bind-Value="treatmentForm.Symptoms" class="form-control" rows="3" placeholder="اكتب الأعراض الملاحظة" />
                                <ValidationMessage For="@(() => treatmentForm.Symptoms)" />
                            </div>
                            
                            <div class="form-group">
                                <label for="diagnosis">التشخيص</label>
                                <InputTextArea id="diagnosis" @bind-Value="treatmentForm.Diagnosis" class="form-control" rows="3" placeholder="اكتب التشخيص" />
                                <ValidationMessage For="@(() => treatmentForm.Diagnosis)" />
                            </div>
                            
                            <div class="form-group">
                                <label for="treatmentPlan">خطة العلاج</label>
                                <InputTextArea id="treatmentPlan" @bind-Value="treatmentForm.TreatmentPlan" class="form-control" rows="3" placeholder="اكتب خطة العلاج" />
                                <ValidationMessage For="@(() => treatmentForm.TreatmentPlan)" />
                            </div>
                        </div>
                    }
                    
                    <!-- Step 3: Medicines -->
                    @if (currentStep == 3)
                    {
                        <div class="step-content">
                            <h4 class="step-title">
                                <i class="fas fa-pills"></i>
                                الأدوية
                            </h4>
                            
                            <div class="medicines-section">
                                <div class="add-medicine-form">
                                    <div class="form-row">
                                        <div class="form-group">
                                            <label>الدواء</label>
                                            <select @bind="selectedMedicineId" class="form-control">
                                                <option value="0">اختر الدواء</option>
                                                @foreach (var medicine in medicines)
                                                {
                                                    <option value="@medicine.Id">@medicine.Name (@medicine.Quantity @medicine.Unit متوفر)</option>
                                                }
                                            </select>
                                        </div>
                                        
                                        <div class="form-group">
                                            <label>الكمية</label>
                                            <input type="number" @bind="medicineQuantity" class="form-control" placeholder="0" min="1" />
                                        </div>
                                        
                                        <div class="form-group">
                                            <label>&nbsp;</label>
                                            <button type="button" class="btn btn-success" @onclick="AddMedicine">
                                                <i class="fas fa-plus"></i>
                                                إضافة
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                
                                @if (treatmentForm.Medicines.Any())
                                {
                                    <div class="selected-medicines">
                                        <h5>الأدوية المحددة:</h5>
                                        @foreach (var medicine in treatmentForm.Medicines)
                                        {
                                            <div class="medicine-row">
                                                <span class="medicine-info">
                                                    @medicines.FirstOrDefault(m => m.Id == medicine.MedicineId)?.Name
                                                    - @medicine.Quantity وحدة
                                                    - @medicine.TotalPrice.ToString("N2") ر.س
                                                </span>
                                                <button type="button" class="btn btn-danger btn-sm" @onclick="() => RemoveMedicine(medicine.MedicineId)">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        }
                                        <div class="total-cost">
                                            <strong>التكلفة الإجمالية: @treatmentForm.TotalCost.ToString("N2") ر.س</strong>
                                        </div>
                                    </div>
                                }
                            </div>
                            
                            <div class="form-group">
                                <label for="notes">ملاحظات إضافية</label>
                                <InputTextArea id="notes" @bind-Value="treatmentForm.Notes" class="form-control" rows="2" placeholder="أي ملاحظات إضافية" />
                                <ValidationMessage For="@(() => treatmentForm.Notes)" />
                            </div>
                        </div>
                    }
                    
                    <!-- Navigation Buttons -->
                    <div class="modal-actions">
                        @if (currentStep > 1)
                        {
                            <button type="button" class="btn btn-secondary" @onclick="PreviousStep">
                                <i class="fas fa-arrow-right"></i>
                                السابق
                            </button>
                        }
                        
                        @if (currentStep < 3)
                        {
                            <button type="button" class="btn btn-primary" @onclick="NextStep" disabled="@(!CanProceedToNextStep())">
                                <i class="fas fa-arrow-left"></i>
                                التالي
                            </button>
                        }
                        else
                        {
                            <button type="button" class="btn btn-secondary" @onclick="CloseTreatmentModal">إلغاء</button>
                            <button type="submit" class="btn btn-primary" disabled="@isSaving">
                                @if (isSaving)
                                {
                                    <i class="fas fa-spinner fa-spin"></i>
                                    <span>جاري الحفظ...</span>
                                }
                                else
                                {
                                    <i class="fas fa-save"></i>
                                    <span>حفظ العلاج</span>
                                }
                            </button>
                        }
                    </div>
                </EditForm>
            </div>
        </div>
    </div>
}

<style>
    .treatment-page {
        max-width: 1400px;
        margin: 0 auto;
    }

    .page-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 30px;
        padding-bottom: 20px;
        border-bottom: 2px solid #eee;
        flex-wrap: wrap;
        gap: 20px;
    }

    .header-content h1 {
        font-size: 2.2rem;
        color: #333;
        margin-bottom: 5px;
        display: flex;
        align-items: center;
        gap: 15px;
    }

    .page-subtitle {
        font-size: 1.1rem;
        color: #666;
        margin-bottom: 0;
    }

    .header-actions {
        display: flex;
        gap: 20px;
        align-items: center;
        flex-wrap: wrap;
    }

    .filter-buttons {
        display: flex;
        gap: 10px;
        flex-wrap: wrap;
    }

    .btn {
        padding: 10px 20px;
        border: none;
        border-radius: 8px;
        cursor: pointer;
        font-size: 0.9rem;
        font-weight: 500;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 8px;
        text-decoration: none;
    }

    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
    }

    .btn-outline {
        background: white;
        border: 2px solid #e9ecef;
        color: #666;
    }

    .btn-outline:hover {
        border-color: #667eea;
        color: #667eea;
    }

    .btn-success { background: #28a745; color: white; }
    .btn-warning { background: #ffc107; color: white; }
    .btn-info { background: #17a2b8; color: white; }
    .btn-danger { background: #dc3545; color: white; }
    .btn-secondary { background: #6c757d; color: white; }

    .btn-sm {
        padding: 6px 12px;
        font-size: 0.8rem;
    }

    .loading-container {
        text-align: center;
        padding: 60px 20px;
        color: #666;
    }

    .loading-container .spinner {
        font-size: 3rem;
        color: #667eea;
        margin-bottom: 20px;
    }

    /* Treatments Grid */
    .treatments-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(450px, 1fr));
        gap: 25px;
    }

    .treatment-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        border-left: 5px solid;
    }

    .treatment-card.pending { border-left-color: #ffc107; }
    .treatment-card.in-progress { border-left-color: #17a2b8; }
    .treatment-card.completed { border-left-color: #28a745; }
    .treatment-card.cancelled { border-left-color: #dc3545; }

    .treatment-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }

    .card-header {
        padding: 20px;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-bottom: 1px solid #dee2e6;
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
    }

    .treatment-title {
        font-size: 1.3rem;
        font-weight: 600;
        color: #333;
        margin-bottom: 5px;
    }

    .animal-name {
        color: #667eea;
        font-weight: 500;
    }

    .breeder-name, .doctor-name {
        font-size: 0.9rem;
        color: #666;
        margin-bottom: 3px;
    }

    .treatment-status {
        display: flex;
        align-items: center;
    }

    .status-badge {
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 500;
        display: flex;
        align-items: center;
        gap: 5px;
    }

    .status-badge.pending {
        background: rgba(255, 193, 7, 0.2);
        color: #856404;
        border: 1px solid rgba(255, 193, 7, 0.3);
    }

    .status-badge.in-progress {
        background: rgba(23, 162, 184, 0.2);
        color: #0c5460;
        border: 1px solid rgba(23, 162, 184, 0.3);
    }

    .status-badge.completed {
        background: rgba(40, 167, 69, 0.2);
        color: #155724;
        border: 1px solid rgba(40, 167, 69, 0.3);
    }

    .status-badge.cancelled {
        background: rgba(220, 53, 69, 0.2);
        color: #721c24;
        border: 1px solid rgba(220, 53, 69, 0.3);
    }

    .card-body {
        padding: 20px;
    }

    .treatment-section {
        margin-bottom: 20px;
    }

    .treatment-section h4 {
        font-size: 1rem;
        color: #333;
        margin-bottom: 8px;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .treatment-section h4 i {
        color: #667eea;
        width: 16px;
    }

    .treatment-section p {
        font-size: 0.9rem;
        color: #666;
        margin-bottom: 0;
        line-height: 1.5;
    }

    .medicines-list {
        display: flex;
        flex-direction: column;
        gap: 8px;
    }

    .medicine-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 12px;
        background: #f8f9fa;
        border-radius: 6px;
        font-size: 0.85rem;
    }

    .medicine-name {
        font-weight: 500;
        color: #333;
    }

    .medicine-quantity {
        color: #666;
    }

    .medicine-price {
        color: #28a745;
        font-weight: 600;
    }

    .treatment-footer {
        margin-top: 20px;
        padding-top: 15px;
        border-top: 1px solid #eee;
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        gap: 10px;
    }

    .total-cost {
        font-size: 1.1rem;
        font-weight: 600;
        color: #28a745;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .date-info {
        font-size: 0.8rem;
        color: #999;
    }

    .card-actions {
        margin-top: 15px;
        display: flex;
        gap: 10px;
        flex-wrap: wrap;
    }

    /* Empty State */
    .empty-state {
        text-align: center;
        padding: 80px 20px;
        color: #666;
    }

    .empty-state i {
        font-size: 4rem;
        color: #ddd;
        margin-bottom: 20px;
    }

    .empty-state h3 {
        font-size: 1.5rem;
        margin-bottom: 10px;
        color: #333;
    }

    .empty-state p {
        font-size: 1rem;
        margin-bottom: 30px;
    }

    /* Modal Styles */
    .modal-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1000;
        backdrop-filter: blur(5px);
    }

    .modal-content {
        background: white;
        border-radius: 15px;
        width: 90%;
        max-width: 500px;
        max-height: 90vh;
        overflow-y: auto;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    }

    .large-modal {
        max-width: 800px;
    }

    .modal-header {
        padding: 20px 25px;
        border-bottom: 1px solid #eee;
        display: flex;
        justify-content: space-between;
        align-items: center;
        background: #f8f9fa;
        border-radius: 15px 15px 0 0;
    }

    .modal-header h3 {
        font-size: 1.3rem;
        color: #333;
        margin-bottom: 0;
    }

    .btn-close {
        background: none;
        border: none;
        font-size: 1.2rem;
        color: #666;
        cursor: pointer;
        padding: 5px;
        border-radius: 50%;
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .btn-close:hover {
        background: #e9ecef;
        color: #333;
    }

    .modal-body {
        padding: 25px;
    }

    .step-content {
        min-height: 300px;
    }

    .step-title {
        font-size: 1.2rem;
        color: #333;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
        gap: 10px;
        padding-bottom: 10px;
        border-bottom: 2px solid #eee;
    }

    .step-title i {
        color: #667eea;
    }

    .form-group {
        margin-bottom: 20px;
    }

    .form-group label {
        display: block;
        margin-bottom: 8px;
        font-weight: 500;
        color: #333;
    }

    .form-control {
        width: 100%;
        padding: 12px 15px;
        border: 2px solid #e9ecef;
        border-radius: 8px;
        font-size: 1rem;
        transition: border-color 0.3s ease;
    }

    .form-control:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }

    .form-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 15px;
    }

    .medicines-section {
        border: 2px solid #e9ecef;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }

    .add-medicine-form .form-row {
        grid-template-columns: 2fr 1fr auto;
        align-items: end;
    }

    .selected-medicines {
        margin-top: 20px;
        padding-top: 20px;
        border-top: 1px solid #eee;
    }

    .selected-medicines h5 {
        margin-bottom: 15px;
        color: #333;
    }

    .medicine-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 15px;
        background: #f8f9fa;
        border-radius: 8px;
        margin-bottom: 10px;
    }

    .medicine-info {
        font-size: 0.9rem;
        color: #333;
    }

    .total-cost {
        text-align: center;
        padding: 15px;
        background: #e8f5e8;
        border-radius: 8px;
        margin-top: 15px;
        color: #28a745;
    }

    .modal-actions {
        display: flex;
        gap: 15px;
        justify-content: flex-end;
        margin-top: 25px;
        padding-top: 20px;
        border-top: 1px solid #eee;
    }

    .validation-message {
        color: #dc3545;
        font-size: 0.875rem;
        margin-top: 5px;
        display: block;
    }

    .text-muted {
        color: #999 !important;
    }

    /* Responsive Design */
    @@media (max-width: 768px) {
        .page-header {
            flex-direction: column;
            align-items: flex-start;
        }

        .header-actions {
            width: 100%;
            justify-content: space-between;
        }

        .filter-buttons {
            flex: 1;
        }

        .treatments-grid {
            grid-template-columns: 1fr;
        }

        .card-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 15px;
        }

        .treatment-footer {
            flex-direction: column;
            align-items: flex-start;
        }

        .card-actions {
            justify-content: space-between;
        }

        .form-row {
            grid-template-columns: 1fr;
        }

        .add-medicine-form .form-row {
            grid-template-columns: 1fr;
        }

        .modal-actions {
            flex-direction: column;
        }
    }
</style>

@code {
    private List<TreatmentDto> treatments = new();
    private List<TreatmentDto> filteredTreatments = new();
    private List<BreederDto> breeders = new();
    private List<AnimalDto> breederAnimals = new();
    private List<MedicineDto> medicines = new();
    private bool isLoading = true;
    private bool isSaving = false;
    private string currentFilter = "all";

    // Modal states
    private bool showTreatmentModal = false;
    private TreatmentDto? editingTreatment = null;
    private int currentStep = 1;

    // Form data
    private CreateTreatmentDto treatmentForm = new();
    private int selectedBreederId = 0;
    private int selectedMedicineId = 0;
    private int medicineQuantity = 1;

    protected override async Task OnInitializedAsync()
    {
        await LoadData();
    }

    private async Task LoadData()
    {
        try
        {
            isLoading = true;
            StateHasChanged();

            // Load all required data
            var treatmentsTask = LoadTreatments();
            var breedersTask = ApiService.GetBreedersAsync();
            var medicinesTask = ApiService.GetMedicinesAsync();

            await Task.WhenAll(treatmentsTask, breedersTask, medicinesTask);

            breeders = (await breedersTask).ToList();
            medicines = (await medicinesTask).Where(m => m.Quantity > 0).ToList();

            FilterTreatments(currentFilter);
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", "حدث خطأ في تحميل البيانات");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private async Task LoadTreatments()
    {
        // Note: This would need to be implemented in ApiService
        // For now, we'll use an empty list
        treatments = new List<TreatmentDto>();
    }

    private void FilterTreatments(string filter)
    {
        currentFilter = filter;

        filteredTreatments = filter switch
        {
            "pending" => treatments.Where(t => t.Status == TreatmentStatus.Pending || t.Status == TreatmentStatus.InProgress).ToList(),
            "completed" => treatments.Where(t => t.Status == TreatmentStatus.Completed).ToList(),
            _ => treatments.ToList()
        };

        StateHasChanged();
    }

    private string GetTreatmentCardClass(TreatmentStatus status)
    {
        return status switch
        {
            TreatmentStatus.Pending => "pending",
            TreatmentStatus.InProgress => "in-progress",
            TreatmentStatus.Completed => "completed",
            TreatmentStatus.Cancelled => "cancelled",
            _ => ""
        };
    }

    private string GetStatusClass(TreatmentStatus status)
    {
        return status switch
        {
            TreatmentStatus.Pending => "pending",
            TreatmentStatus.InProgress => "in-progress",
            TreatmentStatus.Completed => "completed",
            TreatmentStatus.Cancelled => "cancelled",
            _ => ""
        };
    }

    private string GetStatusIcon(TreatmentStatus status)
    {
        return status switch
        {
            TreatmentStatus.Pending => "fas fa-clock",
            TreatmentStatus.InProgress => "fas fa-play",
            TreatmentStatus.Completed => "fas fa-check",
            TreatmentStatus.Cancelled => "fas fa-times",
            _ => "fas fa-question"
        };
    }

    private string GetStatusText(TreatmentStatus status)
    {
        return status switch
        {
            TreatmentStatus.Pending => "معلق",
            TreatmentStatus.InProgress => "قيد التنفيذ",
            TreatmentStatus.Completed => "مكتمل",
            TreatmentStatus.Cancelled => "ملغي",
            _ => "غير محدد"
        };
    }

    private string GetEmptyStateTitle()
    {
        return currentFilter switch
        {
            "pending" => "لا توجد علاجات معلقة",
            "completed" => "لا توجد علاجات مكتملة",
            _ => "لا توجد علاجات مسجلة"
        };
    }

    private string GetEmptyStateMessage()
    {
        return currentFilter switch
        {
            "pending" => "جميع العلاجات تم إكمالها",
            "completed" => "لم يتم إكمال أي علاجات بعد",
            _ => "ابدأ بإضافة علاج جديد للحيوانات"
        };
    }

    private void ShowNewTreatmentModal()
    {
        editingTreatment = null;
        treatmentForm = new CreateTreatmentDto();
        currentStep = 1;
        selectedBreederId = 0;
        breederAnimals.Clear();
        showTreatmentModal = true;
    }

    private void ShowEditTreatmentModal(TreatmentDto treatment)
    {
        editingTreatment = treatment;
        treatmentForm = new CreateTreatmentDto
        {
            AnimalId = treatment.AnimalId,
            BreederId = treatment.BreederId,
            DoctorId = treatment.DoctorId,
            BranchId = treatment.BranchId,
            Symptoms = treatment.Symptoms,
            Diagnosis = treatment.Diagnosis,
            TreatmentPlan = treatment.TreatmentPlan,
            Notes = treatment.Notes,
            TotalCost = treatment.TotalCost,
            Medicines = treatment.Medicines.Select(m => new TreatmentMedicineCreateDto
            {
                MedicineId = m.MedicineId,
                Quantity = m.Quantity,
                UnitPrice = m.UnitPrice
            }).ToList()
        };
        currentStep = 1;
        showTreatmentModal = true;
    }

    private void CloseTreatmentModal()
    {
        showTreatmentModal = false;
        editingTreatment = null;
        treatmentForm = new CreateTreatmentDto();
        currentStep = 1;
        selectedBreederId = 0;
        breederAnimals.Clear();
    }

    private async Task OnBreederChanged(ChangeEventArgs e)
    {
        if (int.TryParse(e.Value?.ToString(), out int breederId) && breederId > 0)
        {
            selectedBreederId = breederId;
            treatmentForm.BreederId = breederId;

            // Load animals for selected breeder
            breederAnimals = (await ApiService.GetAnimalsAsync(breederId)).ToList();
            StateHasChanged();
        }
        else
        {
            selectedBreederId = 0;
            breederAnimals.Clear();
            treatmentForm.AnimalId = 0;
        }
    }

    private bool CanProceedToNextStep()
    {
        return currentStep switch
        {
            1 => treatmentForm.AnimalId > 0 && treatmentForm.BreederId > 0,
            2 => !string.IsNullOrWhiteSpace(treatmentForm.Symptoms) || !string.IsNullOrWhiteSpace(treatmentForm.Diagnosis),
            _ => true
        };
    }

    private void NextStep()
    {
        if (CanProceedToNextStep() && currentStep < 3)
        {
            currentStep++;
        }
    }

    private void PreviousStep()
    {
        if (currentStep > 1)
        {
            currentStep--;
        }
    }

    private void AddMedicine()
    {
        if (selectedMedicineId > 0 && medicineQuantity > 0)
        {
            var medicine = medicines.FirstOrDefault(m => m.Id == selectedMedicineId);
            if (medicine != null && medicine.Quantity >= medicineQuantity)
            {
                // Check if medicine already added
                var existingMedicine = treatmentForm.Medicines.FirstOrDefault(m => m.MedicineId == selectedMedicineId);
                if (existingMedicine != null)
                {
                    existingMedicine.Quantity += medicineQuantity;
                    existingMedicine.UnitPrice = medicine.UnitPrice;
                }
                else
                {
                    treatmentForm.Medicines.Add(new TreatmentMedicineCreateDto
                    {
                        MedicineId = selectedMedicineId,
                        Quantity = medicineQuantity,
                        UnitPrice = medicine.UnitPrice
                    });
                }

                // Calculate total cost
                CalculateTotalCost();

                // Reset selection
                selectedMedicineId = 0;
                medicineQuantity = 1;
                StateHasChanged();
            }
            else
            {
                JSRuntime.InvokeVoidAsync("alert", "الكمية المطلوبة غير متوفرة في المخزون");
            }
        }
    }

    private void RemoveMedicine(int medicineId)
    {
        treatmentForm.Medicines.RemoveAll(m => m.MedicineId == medicineId);
        CalculateTotalCost();
        StateHasChanged();
    }

    private void CalculateTotalCost()
    {
        treatmentForm.TotalCost = treatmentForm.Medicines.Sum(m => m.Quantity * m.UnitPrice);

        // Add animal type treatment price if available
        if (treatmentForm.AnimalId > 0)
        {
            var animal = breederAnimals.FirstOrDefault(a => a.Id == treatmentForm.AnimalId);
            if (animal != null)
            {
                treatmentForm.TotalCost += animal.TreatmentPrice;
            }
        }
    }

    private async Task SaveTreatment()
    {
        try
        {
            isSaving = true;
            StateHasChanged();

            // Set doctor and branch (would come from current user)
            treatmentForm.DoctorId = 1; // This should come from current user
            treatmentForm.BranchId = 1; // This should come from current user

            if (editingTreatment == null)
            {
                // Create new treatment - this would need to be implemented in ApiService
                await JSRuntime.InvokeVoidAsync("alert", "تم إضافة العلاج بنجاح");
                CloseTreatmentModal();
                await LoadData();
            }
            else
            {
                // Update existing treatment - this would need to be implemented in ApiService
                await JSRuntime.InvokeVoidAsync("alert", "تم تحديث العلاج بنجاح");
                CloseTreatmentModal();
                await LoadData();
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", "حدث خطأ غير متوقع");
        }
        finally
        {
            isSaving = false;
            StateHasChanged();
        }
    }

    private async Task CompleteTreatment(int treatmentId)
    {
        var confirmed = await JSRuntime.InvokeAsync<bool>("confirm", "هل أنت متأكد من إكمال هذا العلاج؟");
        if (confirmed)
        {
            try
            {
                // This would need to be implemented in ApiService
                await JSRuntime.InvokeVoidAsync("alert", "تم إكمال العلاج بنجاح");
                await LoadData();
            }
            catch (Exception ex)
            {
                await JSRuntime.InvokeVoidAsync("alert", "حدث خطأ غير متوقع");
            }
        }
    }

    private async Task ViewTreatmentDetails(int treatmentId)
    {
        // Navigate to treatment details page or show detailed modal
        await JSRuntime.InvokeVoidAsync("alert", $"عرض تفاصيل العلاج رقم {treatmentId}");
    }
}
