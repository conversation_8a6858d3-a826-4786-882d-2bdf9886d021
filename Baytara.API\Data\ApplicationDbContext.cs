using Microsoft.EntityFrameworkCore;
using Baytara.Shared.Models;

namespace Baytara.API.Data
{
    public class ApplicationDbContext : DbContext
    {
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) : base(options)
        {
        }

        public DbSet<User> Users { get; set; }
        public DbSet<Branch> Branches { get; set; }
        public DbSet<Breeder> Breeders { get; set; }
        public DbSet<AnimalType> AnimalTypes { get; set; }
        public DbSet<Animal> Animals { get; set; }
        public DbSet<Disease> Diseases { get; set; }
        public DbSet<Medicine> Medicines { get; set; }
        public DbSet<Treatment> Treatments { get; set; }
        public DbSet<TreatmentDisease> TreatmentDiseases { get; set; }
        public DbSet<TreatmentMedicine> TreatmentMedicines { get; set; }
        public DbSet<LabTest> LabTests { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Configure User entity
            modelBuilder.Entity<User>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.HasIndex(e => e.Username).IsUnique();
                entity.Property(e => e.Username).IsRequired().HasMaxLength(100);
                entity.Property(e => e.PasswordHash).IsRequired().HasMaxLength(255);
                entity.Property(e => e.FullName).IsRequired().HasMaxLength(100);
                entity.Property(e => e.PhoneNumber).HasMaxLength(15);
                entity.Property(e => e.Email).HasMaxLength(100);
                
                entity.HasOne(e => e.Branch)
                      .WithMany(b => b.Users)
                      .HasForeignKey(e => e.BranchId)
                      .OnDelete(DeleteBehavior.SetNull);
            });

            // Configure Branch entity
            modelBuilder.Entity<Branch>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Address).HasMaxLength(200);
                entity.Property(e => e.PhoneNumber).HasMaxLength(15);
                entity.Property(e => e.Email).HasMaxLength(100);
            });

            // Configure Breeder entity
            modelBuilder.Entity<Breeder>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).IsRequired().HasMaxLength(100);
                entity.Property(e => e.PhoneNumber).HasMaxLength(15);
                entity.Property(e => e.NationalId).HasMaxLength(20);
                entity.Property(e => e.Location).HasMaxLength(200);
                
                entity.HasOne(e => e.Branch)
                      .WithMany(b => b.Breeders)
                      .HasForeignKey(e => e.BranchId)
                      .OnDelete(DeleteBehavior.Restrict);
            });

            // Configure AnimalType entity
            modelBuilder.Entity<AnimalType>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).IsRequired().HasMaxLength(50);
                entity.Property(e => e.TreatmentPrice).HasColumnType("decimal(18,2)");
            });

            // Configure Animal entity
            modelBuilder.Entity<Animal>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).HasMaxLength(50);
                entity.Property(e => e.Color).HasMaxLength(50);
                entity.Property(e => e.Breed).HasMaxLength(100);
                entity.Property(e => e.Weight).HasColumnType("decimal(18,2)");
                entity.Property(e => e.Notes).HasMaxLength(500);
                
                entity.HasOne(e => e.AnimalType)
                      .WithMany(at => at.Animals)
                      .HasForeignKey(e => e.AnimalTypeId)
                      .OnDelete(DeleteBehavior.Restrict);
                      
                entity.HasOne(e => e.Breeder)
                      .WithMany(b => b.Animals)
                      .HasForeignKey(e => e.BreederId)
                      .OnDelete(DeleteBehavior.Restrict);
            });

            // Configure Disease entity
            modelBuilder.Entity<Disease>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Description).HasMaxLength(500);
                entity.Property(e => e.Symptoms).HasMaxLength(1000);
                entity.Property(e => e.Treatment).HasMaxLength(1000);
            });

            // Configure Medicine entity
            modelBuilder.Entity<Medicine>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Description).HasMaxLength(500);
                entity.Property(e => e.Unit).HasMaxLength(50);
                entity.Property(e => e.UnitPrice).HasColumnType("decimal(18,2)");
                entity.Property(e => e.BatchNumber).HasMaxLength(50);
                entity.Property(e => e.Manufacturer).HasMaxLength(100);
                
                entity.HasOne(e => e.Branch)
                      .WithMany(b => b.Medicines)
                      .HasForeignKey(e => e.BranchId)
                      .OnDelete(DeleteBehavior.Restrict);
            });

            // Configure Treatment entity
            modelBuilder.Entity<Treatment>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Symptoms).HasMaxLength(1000);
                entity.Property(e => e.Diagnosis).HasMaxLength(1000);
                entity.Property(e => e.TreatmentPlan).HasMaxLength(1000);
                entity.Property(e => e.Notes).HasMaxLength(500);
                entity.Property(e => e.TotalCost).HasColumnType("decimal(18,2)");
                
                entity.HasOne(e => e.Animal)
                      .WithMany(a => a.Treatments)
                      .HasForeignKey(e => e.AnimalId)
                      .OnDelete(DeleteBehavior.Restrict);
                      
                entity.HasOne(e => e.Breeder)
                      .WithMany(b => b.Treatments)
                      .HasForeignKey(e => e.BreederId)
                      .OnDelete(DeleteBehavior.Restrict);
                      
                entity.HasOne(e => e.Doctor)
                      .WithMany(u => u.Treatments)
                      .HasForeignKey(e => e.DoctorId)
                      .OnDelete(DeleteBehavior.Restrict);
                      
                entity.HasOne(e => e.Branch)
                      .WithMany(b => b.Treatments)
                      .HasForeignKey(e => e.BranchId)
                      .OnDelete(DeleteBehavior.Restrict);
            });

            // Configure TreatmentDisease entity (Many-to-Many)
            modelBuilder.Entity<TreatmentDisease>(entity =>
            {
                entity.HasKey(e => new { e.TreatmentId, e.DiseaseId });
                
                entity.HasOne(e => e.Treatment)
                      .WithMany(t => t.TreatmentDiseases)
                      .HasForeignKey(e => e.TreatmentId)
                      .OnDelete(DeleteBehavior.Cascade);
                      
                entity.HasOne(e => e.Disease)
                      .WithMany(d => d.TreatmentDiseases)
                      .HasForeignKey(e => e.DiseaseId)
                      .OnDelete(DeleteBehavior.Cascade);
            });

            // Configure TreatmentMedicine entity (Many-to-Many)
            modelBuilder.Entity<TreatmentMedicine>(entity =>
            {
                entity.HasKey(e => new { e.TreatmentId, e.MedicineId });
                entity.Property(e => e.UnitPrice).HasColumnType("decimal(18,2)");
                entity.Property(e => e.TotalPrice).HasColumnType("decimal(18,2)");
                
                entity.HasOne(e => e.Treatment)
                      .WithMany(t => t.TreatmentMedicines)
                      .HasForeignKey(e => e.TreatmentId)
                      .OnDelete(DeleteBehavior.Cascade);
                      
                entity.HasOne(e => e.Medicine)
                      .WithMany(m => m.TreatmentMedicines)
                      .HasForeignKey(e => e.MedicineId)
                      .OnDelete(DeleteBehavior.Restrict);
            });

            // Configure LabTest entity
            modelBuilder.Entity<LabTest>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.TestName).IsRequired().HasMaxLength(200);
                entity.Property(e => e.Description).HasMaxLength(1000);
                entity.Property(e => e.Results).HasMaxLength(2000);
                entity.Property(e => e.Notes).HasMaxLength(1000);
                entity.Property(e => e.Cost).HasColumnType("decimal(18,2)");

                entity.HasOne(e => e.Animal)
                      .WithMany(a => a.LabTests)
                      .HasForeignKey(e => e.AnimalId)
                      .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(e => e.Breeder)
                      .WithMany(b => b.LabTests)
                      .HasForeignKey(e => e.BreederId)
                      .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(e => e.Treatment)
                      .WithMany(t => t.LabTests)
                      .HasForeignKey(e => e.TreatmentId)
                      .OnDelete(DeleteBehavior.SetNull);

                entity.HasOne(e => e.RequestedByUser)
                      .WithMany()
                      .HasForeignKey(e => e.RequestedById)
                      .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(e => e.ProcessedByUser)
                      .WithMany()
                      .HasForeignKey(e => e.ProcessedById)
                      .OnDelete(DeleteBehavior.SetNull);

                entity.HasOne(e => e.Branch)
                      .WithMany(b => b.LabTests)
                      .HasForeignKey(e => e.BranchId)
                      .OnDelete(DeleteBehavior.Restrict);
            });

            // Seed initial data
            SeedData(modelBuilder);
        }

        private void SeedData(ModelBuilder modelBuilder)
        {
            var seedDate = new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc);

            // Seed Branches
            modelBuilder.Entity<Branch>().HasData(
                new Branch { Id = 1, Name = "الفرع الرئيسي", Address = "الرياض", PhoneNumber = "0112345678", IsActive = true, CreatedAt = seedDate }
            );

            // Seed Animal Types
            modelBuilder.Entity<AnimalType>().HasData(
                // Free category
                new AnimalType { Id = 1, Name = "أرانب", Category = AnimalCategory.Free, TreatmentPrice = 0, IsActive = true, CreatedAt = seedDate },
                new AnimalType { Id = 2, Name = "دواجن", Category = AnimalCategory.Free, TreatmentPrice = 0, IsActive = true, CreatedAt = seedDate },

                // Economic category - 100 بيسة = 1 ريال
                new AnimalType { Id = 3, Name = "جمال", Category = AnimalCategory.Economic, TreatmentPrice = 1.00m, IsActive = true, CreatedAt = seedDate },
                new AnimalType { Id = 4, Name = "خيول", Category = AnimalCategory.Economic, TreatmentPrice = 1.00m, IsActive = true, CreatedAt = seedDate },
                new AnimalType { Id = 5, Name = "أبقار", Category = AnimalCategory.Economic, TreatmentPrice = 1.00m, IsActive = true, CreatedAt = seedDate },
                new AnimalType { Id = 6, Name = "ماعز", Category = AnimalCategory.Economic, TreatmentPrice = 1.00m, IsActive = true, CreatedAt = seedDate },
                new AnimalType { Id = 7, Name = "ضأن", Category = AnimalCategory.Economic, TreatmentPrice = 1.00m, IsActive = true, CreatedAt = seedDate },

                // Non-Economic category - price determined by staff
                new AnimalType { Id = 8, Name = "قطط", Category = AnimalCategory.NonEconomic, TreatmentPrice = 0, IsActive = true, CreatedAt = seedDate },
                new AnimalType { Id = 9, Name = "كلاب", Category = AnimalCategory.NonEconomic, TreatmentPrice = 0, IsActive = true, CreatedAt = seedDate },
                new AnimalType { Id = 10, Name = "طيور زينة", Category = AnimalCategory.NonEconomic, TreatmentPrice = 0, IsActive = true, CreatedAt = seedDate },
                new AnimalType { Id = 11, Name = "أخرى", Category = AnimalCategory.NonEconomic, TreatmentPrice = 0, IsActive = true, CreatedAt = seedDate }
            );

            // Seed default admin user
            modelBuilder.Entity<User>().HasData(
                new User
                {
                    Id = 1,
                    Username = "admin",
                    PasswordHash = "$2a$11$K8gHqVhL8.WvQZKqz5Zj8.rQZKqz5Zj8rQZKqz5Zj8rQZKqz5Zj8r", // BCrypt hash for "admin123"
                    FullName = "مدير النظام",
                    Role = UserRole.SystemAdmin,
                    BranchId = 1,
                    IsActive = true,
                    CreatedAt = seedDate
                }
            );
        }
    }
}
