using System.ComponentModel.DataAnnotations;

namespace Baytara.Shared.DTOs
{
    public class DiseaseDto
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string? Description { get; set; }
        public string? Symptoms { get; set; }
        public string? Treatment { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedAt { get; set; }
    }
    
    public class CreateDiseaseDto
    {
        [Required(ErrorMessage = "اسم المرض مطلوب")]
        [StringLength(100, ErrorMessage = "اسم المرض يجب أن يكون أقل من 100 حرف")]
        public string Name { get; set; } = string.Empty;
        
        [StringLength(500, ErrorMessage = "الوصف يجب أن يكون أقل من 500 حرف")]
        public string? Description { get; set; }
        
        [StringLength(1000, ErrorMessage = "الأعراض يجب أن تكون أقل من 1000 حرف")]
        public string? Symptoms { get; set; }
        
        [StringLength(1000, ErrorMessage = "العلاج يجب أن يكون أقل من 1000 حرف")]
        public string? Treatment { get; set; }
    }
    
    public class UpdateDiseaseDto
    {
        [Required(ErrorMessage = "اسم المرض مطلوب")]
        [StringLength(100, ErrorMessage = "اسم المرض يجب أن يكون أقل من 100 حرف")]
        public string Name { get; set; } = string.Empty;
        
        [StringLength(500, ErrorMessage = "الوصف يجب أن يكون أقل من 500 حرف")]
        public string? Description { get; set; }
        
        [StringLength(1000, ErrorMessage = "الأعراض يجب أن تكون أقل من 1000 حرف")]
        public string? Symptoms { get; set; }
        
        [StringLength(1000, ErrorMessage = "العلاج يجب أن يكون أقل من 1000 حرف")]
        public string? Treatment { get; set; }
        
        public bool IsActive { get; set; }
    }
}
