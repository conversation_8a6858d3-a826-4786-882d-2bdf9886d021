using System.ComponentModel.DataAnnotations;

namespace Baytara.Shared.DTOs
{
    public class MedicineDto
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string? Description { get; set; }
        public string? Unit { get; set; }
        public int Quantity { get; set; }
        public int MinimumStock { get; set; }
        public decimal UnitPrice { get; set; }
        public DateTime? ExpiryDate { get; set; }
        public string? BatchNumber { get; set; }
        public string? Manufacturer { get; set; }
        public int BranchId { get; set; }
        public string BranchName { get; set; } = string.Empty;
        public bool IsActive { get; set; }
        public DateTime CreatedAt { get; set; }
        public bool IsLowStock { get; set; }
        public bool IsExpiringSoon { get; set; }
        public bool IsExpired { get; set; }
    }
    
    public class CreateMedicineDto
    {
        [Required(ErrorMessage = "اسم الدواء مطلوب")]
        [StringLength(100, ErrorMessage = "اسم الدواء يجب أن يكون أقل من 100 حرف")]
        public string Name { get; set; } = string.Empty;
        
        [StringLength(500, ErrorMessage = "الوصف يجب أن يكون أقل من 500 حرف")]
        public string? Description { get; set; }
        
        [StringLength(50, ErrorMessage = "الوحدة يجب أن تكون أقل من 50 حرف")]
        public string? Unit { get; set; }
        
        [Required(ErrorMessage = "الكمية مطلوبة")]
        [Range(0, int.MaxValue, ErrorMessage = "الكمية يجب أن تكون أكبر من أو تساوي 0")]
        public int Quantity { get; set; }
        
        [Required(ErrorMessage = "الحد الأدنى للمخزون مطلوب")]
        [Range(0, int.MaxValue, ErrorMessage = "الحد الأدنى للمخزون يجب أن يكون أكبر من أو يساوي 0")]
        public int MinimumStock { get; set; }
        
        [Required(ErrorMessage = "سعر الوحدة مطلوب")]
        [Range(0, double.MaxValue, ErrorMessage = "سعر الوحدة يجب أن يكون أكبر من أو يساوي 0")]
        public decimal UnitPrice { get; set; }
        
        public DateTime? ExpiryDate { get; set; }
        
        [StringLength(50, ErrorMessage = "رقم الدفعة يجب أن يكون أقل من 50 حرف")]
        public string? BatchNumber { get; set; }
        
        [StringLength(100, ErrorMessage = "الشركة المصنعة يجب أن تكون أقل من 100 حرف")]
        public string? Manufacturer { get; set; }
        
        [Required(ErrorMessage = "الفرع مطلوب")]
        public int BranchId { get; set; }
    }
    
    public class UpdateMedicineDto
    {
        [Required(ErrorMessage = "اسم الدواء مطلوب")]
        [StringLength(100, ErrorMessage = "اسم الدواء يجب أن يكون أقل من 100 حرف")]
        public string Name { get; set; } = string.Empty;
        
        [StringLength(500, ErrorMessage = "الوصف يجب أن يكون أقل من 500 حرف")]
        public string? Description { get; set; }
        
        [StringLength(50, ErrorMessage = "الوحدة يجب أن تكون أقل من 50 حرف")]
        public string? Unit { get; set; }
        
        [Required(ErrorMessage = "الكمية مطلوبة")]
        [Range(0, int.MaxValue, ErrorMessage = "الكمية يجب أن تكون أكبر من أو تساوي 0")]
        public int Quantity { get; set; }
        
        [Required(ErrorMessage = "الحد الأدنى للمخزون مطلوب")]
        [Range(0, int.MaxValue, ErrorMessage = "الحد الأدنى للمخزون يجب أن يكون أكبر من أو يساوي 0")]
        public int MinimumStock { get; set; }
        
        [Required(ErrorMessage = "سعر الوحدة مطلوب")]
        [Range(0, double.MaxValue, ErrorMessage = "سعر الوحدة يجب أن يكون أكبر من أو يساوي 0")]
        public decimal UnitPrice { get; set; }
        
        public DateTime? ExpiryDate { get; set; }
        
        [StringLength(50, ErrorMessage = "رقم الدفعة يجب أن يكون أقل من 50 حرف")]
        public string? BatchNumber { get; set; }
        
        [StringLength(100, ErrorMessage = "الشركة المصنعة يجب أن تكون أقل من 100 حرف")]
        public string? Manufacturer { get; set; }
        
        [Required(ErrorMessage = "الفرع مطلوب")]
        public int BranchId { get; set; }
        
        public bool IsActive { get; set; }
    }
}
