@inherits LayoutView

<div class="page">
    <div class="sidebar">
        <NavMenu />
    </div>

    <main>
        <div class="top-row px-4">
            <h3>نظام بيطره</h3>
        </div>

        <article class="content px-4">
            @Body
        </article>
    </main>
</div>

<style>
    body {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        direction: rtl;
        text-align: right;
    }

    .page {
        position: relative;
        display: flex;
        flex-direction: row;
    }

    .sidebar {
        background-image: linear-gradient(180deg, rgb(5, 39, 103) 0%, #3a0647 70%);
        width: 250px;
        height: 100vh;
        position: fixed;
        right: 0;
        top: 0;
    }

    main {
        flex: 1;
        margin-right: 250px;
    }

    .top-row {
        background-color: #f7f7f7;
        border-bottom: 1px solid #d6d5d5;
        height: 3.5rem;
        display: flex;
        align-items: center;
    }

    .content {
        padding-top: 1.1rem;
    }

    @@media (max-width: 640.98px) {
        .sidebar {
            width: 100%;
            height: auto;
            position: static;
        }

        main {
            margin-right: 0;
        }
    }
</style>
