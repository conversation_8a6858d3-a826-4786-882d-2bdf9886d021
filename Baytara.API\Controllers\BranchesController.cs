using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using Baytara.API.Data;
using Baytara.Shared.DTOs;
using Baytara.Shared.Models;

namespace Baytara.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class BranchesController : ControllerBase
    {
        private readonly ApplicationDbContext _context;

        public BranchesController(ApplicationDbContext context)
        {
            _context = context;
        }

        [HttpGet]
        public async Task<ActionResult<IEnumerable<BranchDto>>> GetBranches()
        {
            var branches = await _context.Branches
                .Include(b => b.Users)
                .Include(b => b.Breeders)
                .ToListAsync();

            var branchDtos = branches.Select(b => new BranchDto
            {
                Id = b.Id,
                Name = b.Name,
                Address = b.Address,
                Email = b.Email,
                IsActive = b.IsActive,
                UsersCount = b.Users.Count,
                BreedersCount = b.Breeders.Count,
                CreatedAt = b.CreatedAt,
                UpdatedAt = b.UpdatedAt
            }).ToList();

            return Ok(branchDtos);
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<BranchDto>> GetBranch(int id)
        {
            var branch = await _context.Branches
                .Include(b => b.Users)
                .Include(b => b.Breeders)
                .FirstOrDefaultAsync(b => b.Id == id);

            if (branch == null)
            {
                return NotFound();
            }

            var branchDto = new BranchDto
            {
                Id = branch.Id,
                Name = branch.Name,
                Address = branch.Address,
                Email = branch.Email,
                IsActive = branch.IsActive,
                UsersCount = branch.Users.Count,
                BreedersCount = branch.Breeders.Count,
                CreatedAt = branch.CreatedAt,
                UpdatedAt = branch.UpdatedAt
            };

            return Ok(branchDto);
        }

        [HttpPost]
        public async Task<ActionResult<BranchDto>> CreateBranch(CreateBranchDto createBranchDto)
        {
            var branch = new Branch
            {
                Name = createBranchDto.Name,
                Address = createBranchDto.Address,
                Email = createBranchDto.Email,
                IsActive = true,
                CreatedAt = DateTime.UtcNow
            };

            _context.Branches.Add(branch);
            await _context.SaveChangesAsync();

            var branchDto = new BranchDto
            {
                Id = branch.Id,
                Name = branch.Name,
                Address = branch.Address,
                Email = branch.Email,
                IsActive = branch.IsActive,
                UsersCount = 0,
                BreedersCount = 0,
                CreatedAt = branch.CreatedAt,
                UpdatedAt = branch.UpdatedAt
            };

            return CreatedAtAction(nameof(GetBranch), new { id = branch.Id }, branchDto);
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateBranch(int id, UpdateBranchDto updateBranchDto)
        {
            var branch = await _context.Branches.FindAsync(id);

            if (branch == null)
            {
                return NotFound();
            }

            branch.Name = updateBranchDto.Name;
            branch.Address = updateBranchDto.Address;
            branch.Email = updateBranchDto.Email;
            branch.IsActive = updateBranchDto.IsActive;
            branch.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            return NoContent();
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteBranch(int id)
        {
            var branch = await _context.Branches.FindAsync(id);

            if (branch == null)
            {
                return NotFound();
            }

            // Check if branch has users or breeders
            var hasUsers = await _context.Users.AnyAsync(u => u.BranchId == id);
            var hasBreeders = await _context.Breeders.AnyAsync(b => b.BranchId == id);

            if (hasUsers || hasBreeders)
            {
                return BadRequest("لا يمكن حذف الفرع لأنه يحتوي على مستخدمين أو مربين");
            }

            _context.Branches.Remove(branch);
            await _context.SaveChangesAsync();

            return NoContent();
        }

        [HttpPut("{id}/toggle-status")]
        public async Task<IActionResult> ToggleBranchStatus(int id)
        {
            var branch = await _context.Branches.FindAsync(id);

            if (branch == null)
            {
                return NotFound();
            }

            branch.IsActive = !branch.IsActive;
            branch.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            return NoContent();
        }
    }
}
