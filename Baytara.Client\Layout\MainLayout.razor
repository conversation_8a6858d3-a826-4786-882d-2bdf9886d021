﻿@using Baytara.Client.Services
@using Baytara.Shared.DTOs
@inherits LayoutComponentBase
@inject IAuthService AuthService
@inject NavigationManager Navigation
@inject IJSRuntime JSRuntime

<div class="page">
    @if (isAuthenticated)
    {
        <div class="main-layout">
            <!-- Top Header -->
            <div class="top-header">
                <div class="header-left">
                    <span class="username">@currentUser?.FullName</span>
                    <button class="btn btn-logout" @onclick="Logout">
                        <i class="fas fa-sign-out-alt"></i>
                        تسجيل الخروج
                    </button>
                </div>
                <div class="header-right">
                    <div class="clinic-info">
                        <img src="images/logo.png" alt="بيطره" class="header-logo" />
                        <div class="clinic-details">
                            <h2 class="clinic-name">بيطره</h2>
                            <span class="branch-name">@currentUser?.BranchName</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Content Area -->
            <div class="content-area">
                <!-- Right Sidebar Navigation -->
                <nav class="sidebar">
                    <div class="nav-menu">
                        <NavLink class="nav-item" href="/" Match="NavLinkMatch.All">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>الرئيسية</span>
                        </NavLink>

                        <NavLink class="nav-item" href="/breeders">
                            <i class="fas fa-users"></i>
                            <span>المربين</span>
                        </NavLink>

                        <NavLink class="nav-item" href="/treatment">
                            <i class="fas fa-stethoscope"></i>
                            <span>العلاج</span>
                        </NavLink>

                        <NavLink class="nav-item" href="/laboratory">
                            <i class="fas fa-flask"></i>
                            <span>المختبر</span>
                        </NavLink>

                        <NavLink class="nav-item" href="/medicines">
                            <i class="fas fa-pills"></i>
                            <span>الأدوية</span>
                        </NavLink>

                        <NavLink class="nav-item" href="/reports">
                            <i class="fas fa-chart-bar"></i>
                            <span>التقارير</span>
                        </NavLink>

                        <NavLink class="nav-item" href="/settings">
                            <i class="fas fa-cog"></i>
                            <span>الإعدادات</span>
                        </NavLink>
                    </div>
                </nav>

                <!-- Main Content -->
                <main class="main-content">
                    @Body
                </main>
            </div>
        </div>
    }
    else
    {
        @Body
    }
</div>

<style>

    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }

    html, body, #app {
        width: 100%;
        height: 100%;
        margin: 0;
        padding: 0;
        overflow-x: hidden;
    }

    body {
        font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        direction: rtl;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
    }

    .page {
        min-height: 100vh;
        width: 100vw;
        direction: rtl;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        margin: 0;
        padding: 0;
        overflow-x: hidden;
    }

    .main-layout {
        display: flex;
        flex-direction: column;
        min-height: 100vh;
        width: 100vw;
        background: rgba(255, 255, 255, 0.05);
        backdrop-filter: blur(20px);
        margin: 0;
        padding: 0;
    }

    /* Top Header */
    .top-header {
        background: rgba(255, 255, 255, 0.15);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        color: white;
        padding: 20px 40px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        z-index: 1000;
        margin: 10px;
        border-radius: 20px;
        width: calc(100% - 20px);
        box-sizing: border-box;
    }

    .header-left {
        display: flex;
        align-items: center;
        gap: 20px;
    }

    .username {
        font-size: 1.1rem;
        font-weight: 600;
    }

    .btn-logout {
        background: rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
        color: white;
        padding: 12px 20px;
        border-radius: 15px;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 10px;
        font-size: 1rem;
        font-weight: 500;
        backdrop-filter: blur(10px);
    }

    .btn-logout:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: translateY(-2px);
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
    }

    .header-right {
        display: flex;
        align-items: center;
    }

    .clinic-info {
        display: flex;
        align-items: center;
        gap: 15px;
    }

    .header-logo {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        border: 2px solid rgba(255, 255, 255, 0.3);
    }

    .clinic-details {
        text-align: right;
    }

    .clinic-name {
        font-size: 1.5rem;
        font-weight: 700;
        margin-bottom: 2px;
    }

    .branch-name {
        font-size: 0.9rem;
        opacity: 0.9;
    }

    /* Content Area */
    .content-area {
        display: flex;
        flex: 1;
        min-height: calc(100vh - 120px);
        width: 100%;
        gap: 10px;
        padding: 0 10px 10px 10px;
        margin: 0;
        box-sizing: border-box;
    }

    /* Sidebar Navigation */
    .sidebar {
        width: 280px;
        min-width: 280px;
        background: rgba(255, 255, 255, 0.15);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 20px;
        padding: 30px 0;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    }

    .nav-menu {
        display: flex;
        flex-direction: column;
        gap: 8px;
        padding: 0 25px;
    }

    .nav-item {
        display: flex;
        align-items: center;
        gap: 18px;
        padding: 18px 25px;
        color: rgba(255, 255, 255, 0.8);
        text-decoration: none;
        border-radius: 15px;
        transition: all 0.3s ease;
        font-size: 1.1rem;
        font-weight: 500;
        position: relative;
        overflow: hidden;
        backdrop-filter: blur(10px);
    }

    .nav-item::before {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 0;
        height: 100%;
        background: rgba(255, 255, 255, 0.2);
        transition: width 0.3s ease;
        z-index: -1;
        border-radius: 15px;
    }

    .nav-item:hover::before,
    .nav-item.active::before {
        width: 100%;
    }

    .nav-item:hover,
    .nav-item.active {
        color: white;
        transform: translateX(-8px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        background: rgba(255, 255, 255, 0.1);
    }

    .nav-item i {
        font-size: 1.2rem;
        width: 20px;
        text-align: center;
    }

    /* Main Content */
    .main-content {
        flex: 1;
        padding: 30px;
        background: rgba(255, 255, 255, 0.15);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 20px;
        overflow-y: auto;
        width: 100%;
        max-width: none;
        min-width: 0;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        box-sizing: border-box;
        margin: 0;
    }

    /* Responsive Design */


</style>

@code {
    private bool isAuthenticated = false;
    private UserDto? currentUser;

    protected override async Task OnInitializedAsync()
    {
        AuthService.AuthenticationStateChanged += OnAuthenticationStateChanged;
        await CheckAuthenticationState();
    }

    private async Task CheckAuthenticationState()
    {
        isAuthenticated = await AuthService.IsAuthenticatedAsync();
        if (isAuthenticated)
        {
            currentUser = await AuthService.GetCurrentUserAsync();
        }
        else
        {
            Navigation.NavigateTo("/login");
        }
        StateHasChanged();
    }

    private void OnAuthenticationStateChanged(bool authenticated)
    {
        InvokeAsync(async () =>
        {
            isAuthenticated = authenticated;
            if (authenticated)
            {
                currentUser = await AuthService.GetCurrentUserAsync();
            }
            else
            {
                currentUser = null;
                Navigation.NavigateTo("/login");
            }
            StateHasChanged();
        });
    }

    private async Task Logout()
    {
        await AuthService.LogoutAsync();
    }

    public void Dispose()
    {
        AuthService.AuthenticationStateChanged -= OnAuthenticationStateChanged;
    }
}
