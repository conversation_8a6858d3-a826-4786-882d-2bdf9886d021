using Baytara.Shared.Models;
using Microsoft.EntityFrameworkCore;

namespace Baytara.API.Data
{
    public static class SeedData
    {
        public static async Task SeedAsync(ApplicationDbContext context)
        {
            // Ensure database is created
            await context.Database.EnsureCreatedAsync();

            // Seed Branches
            if (!await context.Branches.AnyAsync())
            {
                var branches = new List<Branch>
                {
                    new Branch
                    {
                        Name = "الفرع الرئيسي",
                        Address = "الرياض، المملكة العربية السعودية",
                        PhoneNumber = "+966112345678",
                        Email = "<EMAIL>",
                        IsActive = true,
                        CreatedAt = DateTime.UtcNow
                    },
                    new Branch
                    {
                        Name = "فرع جدة",
                        Address = "جدة، المملكة العربية السعودية",
                        PhoneNumber = "+966126789012",
                        Email = "<EMAIL>",
                        IsActive = true,
                        CreatedAt = DateTime.UtcNow
                    }
                };

                await context.Branches.AddRangeAsync(branches);
                await context.SaveChangesAsync();
            }

            // Seed Users
            if (!await context.Users.AnyAsync())
            {
                var mainBranch = await context.Branches.FirstAsync();
                
                var users = new List<User>
                {
                    new User
                    {
                        Username = "admin",
                        PasswordHash = BCrypt.Net.BCrypt.HashPassword("admin123"),
                        FullName = "مدير النظام",
                        Email = "<EMAIL>",
                        PhoneNumber = "+966501234567",
                        Role = UserRole.SystemAdmin,
                        BranchId = mainBranch.Id,
                        IsActive = true,
                        CreatedAt = DateTime.UtcNow
                    },
                    new User
                    {
                        Username = "doctor1",
                        PasswordHash = BCrypt.Net.BCrypt.HashPassword("doctor123"),
                        FullName = "د. أحمد محمد",
                        Email = "<EMAIL>",
                        PhoneNumber = "+966507654321",
                        Role = UserRole.Doctor,
                        BranchId = mainBranch.Id,
                        IsActive = true,
                        CreatedAt = DateTime.UtcNow
                    }
                };

                await context.Users.AddRangeAsync(users);
                await context.SaveChangesAsync();
            }

            // Seed Animal Types
            if (!await context.AnimalTypes.AnyAsync())
            {
                var animalTypes = new List<AnimalType>
                {
                    // مجاني
                    new AnimalType
                    {
                        Name = "أرانب",
                        Category = AnimalCategory.Free,
                        TreatmentPrice = 0.00m,
                        IsActive = true,
                        CreatedAt = DateTime.UtcNow
                    },
                    new AnimalType
                    {
                        Name = "دواجن",
                        Category = AnimalCategory.Free,
                        TreatmentPrice = 0.00m,
                        IsActive = true,
                        CreatedAt = DateTime.UtcNow
                    },

                    // اقتصادي
                    new AnimalType
                    {
                        Name = "أغنام",
                        Category = AnimalCategory.Economic,
                        TreatmentPrice = 100.00m,
                        IsActive = true,
                        CreatedAt = DateTime.UtcNow
                    },
                    new AnimalType
                    {
                        Name = "أبقار",
                        Category = AnimalCategory.Economic,
                        TreatmentPrice = 150.00m,
                        IsActive = true,
                        CreatedAt = DateTime.UtcNow
                    },
                    new AnimalType
                    {
                        Name = "جمال",
                        Category = AnimalCategory.Economic,
                        TreatmentPrice = 200.00m,
                        IsActive = true,
                        CreatedAt = DateTime.UtcNow
                    },
                    new AnimalType
                    {
                        Name = "ضأن",
                        Category = AnimalCategory.Economic,
                        TreatmentPrice = 90.00m,
                        IsActive = true,
                        CreatedAt = DateTime.UtcNow
                    },
                    new AnimalType
                    {
                        Name = "خيول",
                        Category = AnimalCategory.Economic,
                        TreatmentPrice = 300.00m,
                        IsActive = true,
                        CreatedAt = DateTime.UtcNow
                    },

                    // غير اقتصادي
                    new AnimalType
                    {
                        Name = "طيور زينة",
                        Category = AnimalCategory.NonEconomic,
                        TreatmentPrice = 50.00m,
                        IsActive = true,
                        CreatedAt = DateTime.UtcNow
                    },
                    new AnimalType
                    {
                        Name = "كلاب",
                        Category = AnimalCategory.NonEconomic,
                        TreatmentPrice = 120.00m,
                        IsActive = true,
                        CreatedAt = DateTime.UtcNow
                    },
                    new AnimalType
                    {
                        Name = "قطط",
                        Category = AnimalCategory.NonEconomic,
                        TreatmentPrice = 80.00m,
                        IsActive = true,
                        CreatedAt = DateTime.UtcNow
                    },
                    new AnimalType
                    {
                        Name = "أخرى",
                        Category = AnimalCategory.NonEconomic,
                        TreatmentPrice = 100.00m,
                        IsActive = true,
                        CreatedAt = DateTime.UtcNow
                    }
                };

                await context.AnimalTypes.AddRangeAsync(animalTypes);
                await context.SaveChangesAsync();
            }

            // Seed Diseases
            if (!await context.Diseases.AnyAsync())
            {
                var diseases = new List<Disease>
                {
                    new Disease
                    {
                        Name = "الحمى القلاعية",
                        Description = "مرض فيروسي يصيب الحيوانات ذات الحافر المشقوق",
                        Symptoms = "حمى، تقرحات في الفم والقدم، عرج، فقدان الشهية",
                        Treatment = "علاج داعم، مضادات حيوية للعدوى الثانوية، راحة",
                        IsActive = true,
                        CreatedAt = DateTime.UtcNow
                    },
                    new Disease
                    {
                        Name = "الجمرة الخبيثة",
                        Description = "مرض بكتيري خطير يصيب الحيوانات والإنسان",
                        Symptoms = "حمى شديدة، تورم، صعوبة في التنفس، موت مفاجئ",
                        Treatment = "مضادات حيوية قوية، عزل الحيوان، تطهير المكان",
                        IsActive = true,
                        CreatedAt = DateTime.UtcNow
                    },
                    new Disease
                    {
                        Name = "الطاعون البقري",
                        Description = "مرض فيروسي شديد العدوى يصيب الأبقار",
                        Symptoms = "حمى عالية، إسهال دموي، تقرحات في الفم، جفاف",
                        Treatment = "لا يوجد علاج محدد، علاج داعم، منع انتشار العدوى",
                        IsActive = true,
                        CreatedAt = DateTime.UtcNow
                    },
                    new Disease
                    {
                        Name = "جدري الأغنام",
                        Description = "مرض فيروسي يصيب الأغنام والماعز",
                        Symptoms = "حمى، طفح جلدي، تقرحات، فقدان الوزن",
                        Treatment = "علاج داعم، مضادات حيوية للعدوى الثانوية، عزل",
                        IsActive = true,
                        CreatedAt = DateTime.UtcNow
                    },
                    new Disease
                    {
                        Name = "التهاب الضرع",
                        Description = "التهاب في غدة اللبن عند الحيوانات المنتجة للحليب",
                        Symptoms = "تورم الضرع، حرارة موضعية، تغير في الحليب، ألم",
                        Treatment = "مضادات حيوية، مضادات الالتهاب، حلب منتظم",
                        IsActive = true,
                        CreatedAt = DateTime.UtcNow
                    }
                };

                await context.Diseases.AddRangeAsync(diseases);
                await context.SaveChangesAsync();
            }

            // Seed Sample Medicines
            if (!await context.Medicines.AnyAsync())
            {
                var mainBranch = await context.Branches.FirstAsync();
                
                var medicines = new List<Medicine>
                {
                    new Medicine
                    {
                        Name = "بنسلين",
                        Description = "مضاد حيوي واسع المجال",
                        Unit = "أمبولة",
                        Quantity = 100,
                        MinimumStock = 20,
                        UnitPrice = 15.00m,
                        ExpiryDate = DateTime.UtcNow.AddMonths(18),
                        BatchNumber = "PEN001",
                        Manufacturer = "شركة الأدوية السعودية",
                        BranchId = mainBranch.Id,
                        IsActive = true,
                        CreatedAt = DateTime.UtcNow
                    },
                    new Medicine
                    {
                        Name = "إيفرمكتين",
                        Description = "مضاد للطفيليات الداخلية والخارجية",
                        Unit = "قرص",
                        Quantity = 200,
                        MinimumStock = 50,
                        UnitPrice = 8.00m,
                        ExpiryDate = DateTime.UtcNow.AddMonths(24),
                        BatchNumber = "IVR002",
                        Manufacturer = "شركة الدواء المتقدم",
                        BranchId = mainBranch.Id,
                        IsActive = true,
                        CreatedAt = DateTime.UtcNow
                    },
                    new Medicine
                    {
                        Name = "فيتامين أ د 3 هـ",
                        Description = "مكمل فيتامينات للحيوانات",
                        Unit = "زجاجة",
                        Quantity = 50,
                        MinimumStock = 10,
                        UnitPrice = 25.00m,
                        ExpiryDate = DateTime.UtcNow.AddMonths(12),
                        BatchNumber = "VIT003",
                        Manufacturer = "شركة التغذية البيطرية",
                        BranchId = mainBranch.Id,
                        IsActive = true,
                        CreatedAt = DateTime.UtcNow
                    }
                };

                await context.Medicines.AddRangeAsync(medicines);
                await context.SaveChangesAsync();
            }

            // Seed Sample Breeders
            if (!await context.Breeders.AnyAsync())
            {
                var mainBranch = await context.Branches.FirstAsync();
                
                var breeders = new List<Breeder>
                {
                    new Breeder
                    {
                        Name = "محمد عبدالله الأحمد",
                        PhoneNumber = "+966501111111",
                        NationalId = "1234567890",
                        Location = "مزرعة الخير - الرياض",
                        BranchId = mainBranch.Id,
                        IsActive = true,
                        CreatedAt = DateTime.UtcNow
                    },
                    new Breeder
                    {
                        Name = "سعد محمد القحطاني",
                        PhoneNumber = "+966502222222",
                        NationalId = "2345678901",
                        Location = "مزرعة النخيل - القصيم",
                        BranchId = mainBranch.Id,
                        IsActive = true,
                        CreatedAt = DateTime.UtcNow
                    }
                };

                await context.Breeders.AddRangeAsync(breeders);
                await context.SaveChangesAsync();
            }
        }
    }
}
