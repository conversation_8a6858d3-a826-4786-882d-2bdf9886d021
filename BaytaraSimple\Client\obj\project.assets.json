{"version": 3, "targets": {"net6.0": {"Microsoft.AspNetCore.Authorization/6.0.36": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Metadata": "6.0.36", "Microsoft.Extensions.Logging.Abstractions": "6.0.4", "Microsoft.Extensions.Options": "6.0.1"}, "compile": {"lib/net6.0/Microsoft.AspNetCore.Authorization.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.AspNetCore.Authorization.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Components/6.0.36": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Authorization": "6.0.36", "Microsoft.AspNetCore.Components.Analyzers": "6.0.36"}, "compile": {"lib/net6.0/Microsoft.AspNetCore.Components.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.AspNetCore.Components.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Components.Analyzers/6.0.36": {"type": "package", "build": {"buildTransitive/netstandard2.0/Microsoft.AspNetCore.Components.Analyzers.targets": {}}}, "Microsoft.AspNetCore.Components.Forms/6.0.36": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Components": "6.0.36"}, "compile": {"lib/net6.0/Microsoft.AspNetCore.Components.Forms.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.AspNetCore.Components.Forms.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Components.Web/6.0.36": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Components": "6.0.36", "Microsoft.AspNetCore.Components.Forms": "6.0.36", "Microsoft.Extensions.DependencyInjection": "6.0.2", "Microsoft.JSInterop": "6.0.36", "System.IO.Pipelines": "6.0.3"}, "compile": {"lib/net6.0/Microsoft.AspNetCore.Components.Web.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.AspNetCore.Components.Web.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Components.WebAssembly/6.0.36": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Components.Web": "6.0.36", "Microsoft.Extensions.Configuration.Binder": "6.0.1", "Microsoft.Extensions.Configuration.Json": "6.0.1", "Microsoft.Extensions.Logging": "6.0.1", "Microsoft.JSInterop.WebAssembly": "6.0.36"}, "compile": {"lib/net6.0/Microsoft.AspNetCore.Components.WebAssembly.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.AspNetCore.Components.WebAssembly.dll": {"related": ".xml"}}, "build": {"build/net6.0/Microsoft.AspNetCore.Components.WebAssembly.props": {}}}, "Microsoft.AspNetCore.Components.WebAssembly.DevServer/6.0.36": {"type": "package", "build": {"build/Microsoft.AspNetCore.Components.WebAssembly.DevServer.targets": {}}}, "Microsoft.AspNetCore.Metadata/6.0.36": {"type": "package", "compile": {"lib/net6.0/Microsoft.AspNetCore.Metadata.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.AspNetCore.Metadata.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Configuration/6.0.2": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "6.0.1", "Microsoft.Extensions.Primitives": "6.0.1"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Configuration.Abstractions/6.0.1": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "6.0.1"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Configuration.Binder/6.0.1": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "6.0.1"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Configuration.FileExtensions/6.0.1": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "6.0.2", "Microsoft.Extensions.Configuration.Abstractions": "6.0.1", "Microsoft.Extensions.FileProviders.Abstractions": "6.0.1", "Microsoft.Extensions.FileProviders.Physical": "6.0.1", "Microsoft.Extensions.Primitives": "6.0.1"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Configuration.Json/6.0.1": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "6.0.2", "Microsoft.Extensions.Configuration.Abstractions": "6.0.1", "Microsoft.Extensions.Configuration.FileExtensions": "6.0.1", "Microsoft.Extensions.FileProviders.Abstractions": "6.0.1", "System.Text.Json": "6.0.11"}, "compile": {"lib/netstandard2.1/Microsoft.Extensions.Configuration.Json.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/Microsoft.Extensions.Configuration.Json.dll": {"related": ".xml"}}}, "Microsoft.Extensions.DependencyInjection/6.0.2": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0"}, "compile": {"lib/net6.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/6.0.0": {"type": "package", "compile": {"lib/net6.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "Microsoft.Extensions.FileProviders.Abstractions/6.0.1": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "6.0.1"}, "compile": {"lib/net6.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "Microsoft.Extensions.FileProviders.Physical/6.0.1": {"type": "package", "dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "6.0.1", "Microsoft.Extensions.FileSystemGlobbing": "6.0.0", "Microsoft.Extensions.Primitives": "6.0.1"}, "compile": {"lib/net6.0/Microsoft.Extensions.FileProviders.Physical.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.FileProviders.Physical.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "Microsoft.Extensions.FileSystemGlobbing/6.0.0": {"type": "package", "compile": {"lib/net6.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "Microsoft.Extensions.Logging/6.0.1": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection": "6.0.2", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.4", "Microsoft.Extensions.Options": "6.0.1", "System.Diagnostics.DiagnosticSource": "6.0.2"}, "compile": {"lib/netstandard2.1/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Logging.Abstractions/6.0.4": {"type": "package", "compile": {"lib/net6.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/Microsoft.Extensions.Logging.Abstractions.targets": {}}}, "Microsoft.Extensions.Options/6.0.1": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Primitives": "6.0.1"}, "compile": {"lib/netstandard2.1/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/Microsoft.Extensions.Options.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Primitives/6.0.1": {"type": "package", "compile": {"lib/net6.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "Microsoft.JSInterop/6.0.36": {"type": "package", "compile": {"lib/net6.0/Microsoft.JSInterop.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.JSInterop.dll": {"related": ".xml"}}}, "Microsoft.JSInterop.WebAssembly/6.0.36": {"type": "package", "dependencies": {"Microsoft.JSInterop": "6.0.36"}, "compile": {"lib/net6.0/Microsoft.JSInterop.WebAssembly.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.JSInterop.WebAssembly.dll": {"related": ".xml"}}}, "Microsoft.NET.ILLink.Analyzers/7.0.100-1.23211.1": {"type": "package", "build": {"build/Microsoft.NET.ILLink.Analyzers.props": {}}}, "Microsoft.NET.ILLink.Tasks/7.0.100-1.23211.1": {"type": "package", "build": {"build/Microsoft.NET.ILLink.Tasks.props": {}}}, "Microsoft.NET.Sdk.WebAssembly.Pack/9.0.6": {"type": "package", "build": {"build/Microsoft.NET.Sdk.WebAssembly.Pack.props": {}, "build/Microsoft.NET.Sdk.WebAssembly.Pack.targets": {}}}, "System.Diagnostics.DiagnosticSource/6.0.2": {"type": "package", "compile": {"lib/net6.0/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "System.IO.Pipelines/6.0.3": {"type": "package", "compile": {"lib/net6.0/System.IO.Pipelines.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.IO.Pipelines.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "System.Text.Json/6.0.11": {"type": "package", "compile": {"lib/net6.0/System.Text.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Text.Json.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/System.Text.Json.targets": {}}}, "BaytaraSimple.Shared/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v6.0", "compile": {"bin/placeholder/BaytaraSimple.Shared.dll": {}}, "runtime": {"bin/placeholder/BaytaraSimple.Shared.dll": {}}}}, "net6.0/browser-wasm": {"Microsoft.AspNetCore.Authorization/6.0.36": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Metadata": "6.0.36", "Microsoft.Extensions.Logging.Abstractions": "6.0.4", "Microsoft.Extensions.Options": "6.0.1"}, "compile": {"lib/net6.0/Microsoft.AspNetCore.Authorization.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.AspNetCore.Authorization.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Components/6.0.36": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Authorization": "6.0.36", "Microsoft.AspNetCore.Components.Analyzers": "6.0.36"}, "compile": {"lib/net6.0/Microsoft.AspNetCore.Components.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.AspNetCore.Components.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Components.Analyzers/6.0.36": {"type": "package", "build": {"buildTransitive/netstandard2.0/Microsoft.AspNetCore.Components.Analyzers.targets": {}}}, "Microsoft.AspNetCore.Components.Forms/6.0.36": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Components": "6.0.36"}, "compile": {"lib/net6.0/Microsoft.AspNetCore.Components.Forms.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.AspNetCore.Components.Forms.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Components.Web/6.0.36": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Components": "6.0.36", "Microsoft.AspNetCore.Components.Forms": "6.0.36", "Microsoft.Extensions.DependencyInjection": "6.0.2", "Microsoft.JSInterop": "6.0.36", "System.IO.Pipelines": "6.0.3"}, "compile": {"lib/net6.0/Microsoft.AspNetCore.Components.Web.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.AspNetCore.Components.Web.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Components.WebAssembly/6.0.36": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Components.Web": "6.0.36", "Microsoft.Extensions.Configuration.Binder": "6.0.1", "Microsoft.Extensions.Configuration.Json": "6.0.1", "Microsoft.Extensions.Logging": "6.0.1", "Microsoft.JSInterop.WebAssembly": "6.0.36"}, "compile": {"lib/net6.0/Microsoft.AspNetCore.Components.WebAssembly.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.AspNetCore.Components.WebAssembly.dll": {"related": ".xml"}}, "build": {"build/net6.0/Microsoft.AspNetCore.Components.WebAssembly.props": {}}}, "Microsoft.AspNetCore.Components.WebAssembly.DevServer/6.0.36": {"type": "package", "build": {"build/Microsoft.AspNetCore.Components.WebAssembly.DevServer.targets": {}}}, "Microsoft.AspNetCore.Metadata/6.0.36": {"type": "package", "compile": {"lib/net6.0/Microsoft.AspNetCore.Metadata.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.AspNetCore.Metadata.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Configuration/6.0.2": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "6.0.1", "Microsoft.Extensions.Primitives": "6.0.1"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Configuration.Abstractions/6.0.1": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "6.0.1"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Configuration.Binder/6.0.1": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "6.0.1"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Configuration.FileExtensions/6.0.1": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "6.0.2", "Microsoft.Extensions.Configuration.Abstractions": "6.0.1", "Microsoft.Extensions.FileProviders.Abstractions": "6.0.1", "Microsoft.Extensions.FileProviders.Physical": "6.0.1", "Microsoft.Extensions.Primitives": "6.0.1"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Configuration.Json/6.0.1": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "6.0.2", "Microsoft.Extensions.Configuration.Abstractions": "6.0.1", "Microsoft.Extensions.Configuration.FileExtensions": "6.0.1", "Microsoft.Extensions.FileProviders.Abstractions": "6.0.1", "System.Text.Json": "6.0.11"}, "compile": {"lib/netstandard2.1/Microsoft.Extensions.Configuration.Json.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/Microsoft.Extensions.Configuration.Json.dll": {"related": ".xml"}}}, "Microsoft.Extensions.DependencyInjection/6.0.2": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0"}, "compile": {"lib/net6.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/6.0.0": {"type": "package", "compile": {"lib/net6.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "Microsoft.Extensions.FileProviders.Abstractions/6.0.1": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "6.0.1"}, "compile": {"lib/net6.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "Microsoft.Extensions.FileProviders.Physical/6.0.1": {"type": "package", "dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "6.0.1", "Microsoft.Extensions.FileSystemGlobbing": "6.0.0", "Microsoft.Extensions.Primitives": "6.0.1"}, "compile": {"lib/net6.0/Microsoft.Extensions.FileProviders.Physical.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.FileProviders.Physical.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "Microsoft.Extensions.FileSystemGlobbing/6.0.0": {"type": "package", "compile": {"lib/net6.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "Microsoft.Extensions.Logging/6.0.1": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection": "6.0.2", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.4", "Microsoft.Extensions.Options": "6.0.1", "System.Diagnostics.DiagnosticSource": "6.0.2"}, "compile": {"lib/netstandard2.1/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Logging.Abstractions/6.0.4": {"type": "package", "compile": {"lib/net6.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/Microsoft.Extensions.Logging.Abstractions.targets": {}}}, "Microsoft.Extensions.Options/6.0.1": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Primitives": "6.0.1"}, "compile": {"lib/netstandard2.1/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/Microsoft.Extensions.Options.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Primitives/6.0.1": {"type": "package", "compile": {"lib/net6.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "Microsoft.JSInterop/6.0.36": {"type": "package", "compile": {"lib/net6.0/Microsoft.JSInterop.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.JSInterop.dll": {"related": ".xml"}}}, "Microsoft.JSInterop.WebAssembly/6.0.36": {"type": "package", "dependencies": {"Microsoft.JSInterop": "6.0.36"}, "compile": {"lib/net6.0/Microsoft.JSInterop.WebAssembly.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.JSInterop.WebAssembly.dll": {"related": ".xml"}}}, "Microsoft.NET.ILLink.Analyzers/7.0.100-1.23211.1": {"type": "package", "build": {"build/Microsoft.NET.ILLink.Analyzers.props": {}}}, "Microsoft.NET.ILLink.Tasks/7.0.100-1.23211.1": {"type": "package", "build": {"build/Microsoft.NET.ILLink.Tasks.props": {}}}, "Microsoft.NET.Sdk.WebAssembly.Pack/9.0.6": {"type": "package", "build": {"build/Microsoft.NET.Sdk.WebAssembly.Pack.props": {}, "build/Microsoft.NET.Sdk.WebAssembly.Pack.targets": {}}}, "System.Diagnostics.DiagnosticSource/6.0.2": {"type": "package", "compile": {"lib/net6.0/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "System.IO.Pipelines/6.0.3": {"type": "package", "compile": {"lib/net6.0/System.IO.Pipelines.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.IO.Pipelines.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "System.Text.Json/6.0.11": {"type": "package", "compile": {"lib/net6.0/System.Text.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Text.Json.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/System.Text.Json.targets": {}}}, "BaytaraSimple.Shared/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v6.0", "compile": {"bin/placeholder/BaytaraSimple.Shared.dll": {}}, "runtime": {"bin/placeholder/BaytaraSimple.Shared.dll": {}}}}}, "libraries": {"Microsoft.AspNetCore.Authorization/6.0.36": {"sha512": "/UkyIBwx4BAcDmrBFrAFf9M/pzXn2azPdmtjqT07sc9xvlyk9kOECFupJUFPHagNe/ePC6m4yeQfOgVv7FOx1w==", "type": "package", "path": "microsoft.aspnetcore.authorization/6.0.36", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.AspNetCore.Authorization.dll", "lib/net461/Microsoft.AspNetCore.Authorization.xml", "lib/net6.0/Microsoft.AspNetCore.Authorization.dll", "lib/net6.0/Microsoft.AspNetCore.Authorization.xml", "lib/netstandard2.0/Microsoft.AspNetCore.Authorization.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Authorization.xml", "microsoft.aspnetcore.authorization.6.0.36.nupkg.sha512", "microsoft.aspnetcore.authorization.nuspec"]}, "Microsoft.AspNetCore.Components/6.0.36": {"sha512": "QSmgMLmWX9pu21rVkGHodQESZLE7D3rG2IT0olr8mOyHA2rgaV8kfot+dDAC8TBSuv8BcYJ8poL22F66ivc8dA==", "type": "package", "path": "microsoft.aspnetcore.components/6.0.36", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.txt", "lib/net6.0/Microsoft.AspNetCore.Components.dll", "lib/net6.0/Microsoft.AspNetCore.Components.xml", "microsoft.aspnetcore.components.6.0.36.nupkg.sha512", "microsoft.aspnetcore.components.nuspec"]}, "Microsoft.AspNetCore.Components.Analyzers/6.0.36": {"sha512": "9YzKvreEe4c6bAeJ8Sa6THnx6GGgNi8q315cpA88WkA1p1JQYRtX7782WZwRU1EwvO+v/x+LJECC4Ga4KCgXHw==", "type": "package", "path": "microsoft.aspnetcore.components.analyzers/6.0.36", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.txt", "analyzers/dotnet/cs/Microsoft.AspNetCore.Components.Analyzers.dll", "build/netstandard2.0/Microsoft.AspNetCore.Components.Analyzers.targets", "buildTransitive/netstandard2.0/Microsoft.AspNetCore.Components.Analyzers.targets", "microsoft.aspnetcore.components.analyzers.6.0.36.nupkg.sha512", "microsoft.aspnetcore.components.analyzers.nuspec"]}, "Microsoft.AspNetCore.Components.Forms/6.0.36": {"sha512": "YllldqfPbsP6o94+7RubQrnNLmkN5LnJEuKwJY5EL2mhvyFqPN33jFo4IKQs4B02rjB66ZfJQdFp7faTPVxFXA==", "type": "package", "path": "microsoft.aspnetcore.components.forms/6.0.36", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.txt", "lib/net6.0/Microsoft.AspNetCore.Components.Forms.dll", "lib/net6.0/Microsoft.AspNetCore.Components.Forms.xml", "microsoft.aspnetcore.components.forms.6.0.36.nupkg.sha512", "microsoft.aspnetcore.components.forms.nuspec"]}, "Microsoft.AspNetCore.Components.Web/6.0.36": {"sha512": "dLMvIZICjiZGIhnUTh4fT6k6w+JUBfuQWvlNIxZ2RJk/sFQ11pQMevUMnd1u+w7T1Ez4l3BQfRYobyrJFNIWzQ==", "type": "package", "path": "microsoft.aspnetcore.components.web/6.0.36", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.txt", "lib/net6.0/Microsoft.AspNetCore.Components.Web.dll", "lib/net6.0/Microsoft.AspNetCore.Components.Web.xml", "microsoft.aspnetcore.components.web.6.0.36.nupkg.sha512", "microsoft.aspnetcore.components.web.nuspec"]}, "Microsoft.AspNetCore.Components.WebAssembly/6.0.36": {"sha512": "7O25TDLuhHs30pG8z0uJePL9p9FUPkpF0W5InYDYAX9UaoCBmnPmH9GyVv1qa4LudZWSKk2prmmMXGebLsyxrw==", "type": "package", "path": "microsoft.aspnetcore.components.webassembly/6.0.36", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.txt", "build/net6.0/Microsoft.AspNetCore.Components.WebAssembly.props", "build/net6.0/blazor.webassembly.js", "lib/net6.0/Microsoft.AspNetCore.Components.WebAssembly.dll", "lib/net6.0/Microsoft.AspNetCore.Components.WebAssembly.xml", "microsoft.aspnetcore.components.webassembly.6.0.36.nupkg.sha512", "microsoft.aspnetcore.components.webassembly.nuspec"]}, "Microsoft.AspNetCore.Components.WebAssembly.DevServer/6.0.36": {"sha512": "yVLoXqVRSMBObakbVDXIFGsOs7jcqwdg94/tSlZVuj/1ewlsuQ7ADGOh7+hfqNL6raJmmLwd59fRwgQV8ZwGCQ==", "type": "package", "path": "microsoft.aspnetcore.components.webassembly.devserver/6.0.36", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.txt", "build/Microsoft.AspNetCore.Components.WebAssembly.DevServer.targets", "microsoft.aspnetcore.components.webassembly.devserver.6.0.36.nupkg.sha512", "microsoft.aspnetcore.components.webassembly.devserver.nuspec", "tools/BlazorDebugProxy/BrowserDebugHost.dll", "tools/BlazorDebugProxy/BrowserDebugHost.runtimeconfig.json", "tools/BlazorDebugProxy/BrowserDebugProxy.dll", "tools/BlazorDebugProxy/Microsoft.CodeAnalysis.CSharp.dll", "tools/BlazorDebugProxy/Microsoft.CodeAnalysis.dll", "tools/BlazorDebugProxy/Newtonsoft.Json.dll", "tools/Microsoft.AspNetCore.Authentication.Abstractions.dll", "tools/Microsoft.AspNetCore.Authentication.Abstractions.xml", "tools/Microsoft.AspNetCore.Authentication.Core.dll", "tools/Microsoft.AspNetCore.Authentication.Core.xml", "tools/Microsoft.AspNetCore.Authorization.dll", "tools/Microsoft.AspNetCore.Authorization.xml", "tools/Microsoft.AspNetCore.Components.WebAssembly.Server.dll", "tools/Microsoft.AspNetCore.Components.WebAssembly.Server.xml", "tools/Microsoft.AspNetCore.Connections.Abstractions.dll", "tools/Microsoft.AspNetCore.Connections.Abstractions.xml", "tools/Microsoft.AspNetCore.Diagnostics.Abstractions.dll", "tools/Microsoft.AspNetCore.Diagnostics.Abstractions.xml", "tools/Microsoft.AspNetCore.Diagnostics.dll", "tools/Microsoft.AspNetCore.Diagnostics.xml", "tools/Microsoft.AspNetCore.HostFiltering.dll", "tools/Microsoft.AspNetCore.HostFiltering.xml", "tools/Microsoft.AspNetCore.Hosting.Abstractions.dll", "tools/Microsoft.AspNetCore.Hosting.Abstractions.xml", "tools/Microsoft.AspNetCore.Hosting.Server.Abstractions.dll", "tools/Microsoft.AspNetCore.Hosting.Server.Abstractions.xml", "tools/Microsoft.AspNetCore.Hosting.dll", "tools/Microsoft.AspNetCore.Hosting.xml", "tools/Microsoft.AspNetCore.Http.Abstractions.dll", "tools/Microsoft.AspNetCore.Http.Abstractions.xml", "tools/Microsoft.AspNetCore.Http.Extensions.dll", "tools/Microsoft.AspNetCore.Http.Extensions.xml", "tools/Microsoft.AspNetCore.Http.Features.dll", "tools/Microsoft.AspNetCore.Http.Features.xml", "tools/Microsoft.AspNetCore.Http.dll", "tools/Microsoft.AspNetCore.Http.xml", "tools/Microsoft.AspNetCore.HttpOverrides.dll", "tools/Microsoft.AspNetCore.HttpOverrides.xml", "tools/Microsoft.AspNetCore.Metadata.dll", "tools/Microsoft.AspNetCore.Metadata.xml", "tools/Microsoft.AspNetCore.Routing.Abstractions.dll", "tools/Microsoft.AspNetCore.Routing.Abstractions.xml", "tools/Microsoft.AspNetCore.Routing.dll", "tools/Microsoft.AspNetCore.Routing.xml", "tools/Microsoft.AspNetCore.Server.IIS.dll", "tools/Microsoft.AspNetCore.Server.IIS.xml", "tools/Microsoft.AspNetCore.Server.IISIntegration.dll", "tools/Microsoft.AspNetCore.Server.IISIntegration.xml", "tools/Microsoft.AspNetCore.Server.Kestrel.Core.dll", "tools/Microsoft.AspNetCore.Server.Kestrel.Core.xml", "tools/Microsoft.AspNetCore.Server.Kestrel.Transport.Quic.dll", "tools/Microsoft.AspNetCore.Server.Kestrel.Transport.Quic.xml", "tools/Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.dll", "tools/Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.xml", "tools/Microsoft.AspNetCore.Server.Kestrel.dll", "tools/Microsoft.AspNetCore.Server.Kestrel.xml", "tools/Microsoft.AspNetCore.StaticFiles.dll", "tools/Microsoft.AspNetCore.StaticFiles.xml", "tools/Microsoft.AspNetCore.WebUtilities.dll", "tools/Microsoft.AspNetCore.WebUtilities.xml", "tools/Microsoft.AspNetCore.dll", "tools/Microsoft.AspNetCore.xml", "tools/Microsoft.Extensions.Configuration.Abstractions.dll", "tools/Microsoft.Extensions.Configuration.Binder.dll", "tools/Microsoft.Extensions.Configuration.CommandLine.dll", "tools/Microsoft.Extensions.Configuration.EnvironmentVariables.dll", "tools/Microsoft.Extensions.Configuration.FileExtensions.dll", "tools/Microsoft.Extensions.Configuration.Json.dll", "tools/Microsoft.Extensions.Configuration.UserSecrets.dll", "tools/Microsoft.Extensions.Configuration.dll", "tools/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "tools/Microsoft.Extensions.DependencyInjection.dll", "tools/Microsoft.Extensions.Features.dll", "tools/Microsoft.Extensions.Features.xml", "tools/Microsoft.Extensions.FileProviders.Abstractions.dll", "tools/Microsoft.Extensions.FileProviders.Composite.dll", "tools/Microsoft.Extensions.FileProviders.Physical.dll", "tools/Microsoft.Extensions.FileSystemGlobbing.dll", "tools/Microsoft.Extensions.Hosting.Abstractions.dll", "tools/Microsoft.Extensions.Hosting.dll", "tools/Microsoft.Extensions.Logging.Abstractions.dll", "tools/Microsoft.Extensions.Logging.Configuration.dll", "tools/Microsoft.Extensions.Logging.Console.dll", "tools/Microsoft.Extensions.Logging.Debug.dll", "tools/Microsoft.Extensions.Logging.EventLog.dll", "tools/Microsoft.Extensions.Logging.EventSource.dll", "tools/Microsoft.Extensions.Logging.dll", "tools/Microsoft.Extensions.ObjectPool.dll", "tools/Microsoft.Extensions.ObjectPool.xml", "tools/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "tools/Microsoft.Extensions.Options.dll", "tools/Microsoft.Extensions.Primitives.dll", "tools/Microsoft.Extensions.WebEncoders.dll", "tools/Microsoft.Extensions.WebEncoders.xml", "tools/Microsoft.Net.Http.Headers.dll", "tools/Microsoft.Net.Http.Headers.xml", "tools/System.Diagnostics.DiagnosticSource.dll", "tools/System.Diagnostics.EventLog.dll", "tools/System.IO.Pipelines.dll", "tools/System.Text.Json.dll", "tools/blazor-devserver.deps.json", "tools/blazor-devserver.dll", "tools/blazor-devserver.exe", "tools/blazor-devserver.runtimeconfig.json", "tools/blazor-devserver.xml", "tools/runtimes/win/lib/net6.0/System.Diagnostics.EventLog.Messages.dll", "tools/runtimes/win/lib/net6.0/System.Diagnostics.EventLog.dll", "tools/x64/aspnetcorev2_inprocess.dll", "tools/x86/aspnetcorev2_inprocess.dll"]}, "Microsoft.AspNetCore.Metadata/6.0.36": {"sha512": "9VgcKKs3TU1JschItYWFesTZ1B1VVx/+JqBlFGePf/hu1gghnnioePy3bXeyYE0CSIfYTbCVofTxwd6HMmjgzg==", "type": "package", "path": "microsoft.aspnetcore.metadata/6.0.36", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.AspNetCore.Metadata.dll", "lib/net461/Microsoft.AspNetCore.Metadata.xml", "lib/net6.0/Microsoft.AspNetCore.Metadata.dll", "lib/net6.0/Microsoft.AspNetCore.Metadata.xml", "lib/netstandard2.0/Microsoft.AspNetCore.Metadata.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Metadata.xml", "microsoft.aspnetcore.metadata.6.0.36.nupkg.sha512", "microsoft.aspnetcore.metadata.nuspec"]}, "Microsoft.Extensions.Configuration/6.0.2": {"sha512": "2dpWx3Lxqq1lzLa9pY4lTOl6xg7VL45z0oe3r1xfXM0nQzR9XpuuMdk54B1LrC/AsyKbOukry+pdoQ7M7e/ezg==", "type": "package", "path": "microsoft.extensions.configuration/6.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.Configuration.dll", "lib/net461/Microsoft.Extensions.Configuration.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.xml", "microsoft.extensions.configuration.6.0.2.nupkg.sha512", "microsoft.extensions.configuration.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.Abstractions/6.0.1": {"sha512": "+FhuIM7bE3CyzhhIju6K1pmcDAp4ez2PKwx8jnV4dEI/LXXBGdQbDijlaQWqMa1oC38DX390bSshQVaKXioiXA==", "type": "package", "path": "microsoft.extensions.configuration.abstractions/6.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net461/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.xml", "microsoft.extensions.configuration.abstractions.6.0.1.nupkg.sha512", "microsoft.extensions.configuration.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.Binder/6.0.1": {"sha512": "akSVfaHcxSSlSjC/dVpHbPLDHvYd/l/x0dlp2v+9zQVtSaSH3BIfO8AXvGgDEN/VPCCGeHigfflqkH3v6+RI4Q==", "type": "package", "path": "microsoft.extensions.configuration.binder/6.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.Configuration.Binder.dll", "lib/net461/Microsoft.Extensions.Configuration.Binder.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.xml", "microsoft.extensions.configuration.binder.6.0.1.nupkg.sha512", "microsoft.extensions.configuration.binder.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.FileExtensions/6.0.1": {"sha512": "2jaVZJqN6BvEV9DR/gqOnZUY1jjA/NaUcyJVl7Mp3Uvb9N0YE2uSJ6bdmiKxocsiGZxpfjqBe0SRISCCKhVecw==", "type": "package", "path": "microsoft.extensions.configuration.fileextensions/6.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/net461/Microsoft.Extensions.Configuration.FileExtensions.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.FileExtensions.xml", "microsoft.extensions.configuration.fileextensions.6.0.1.nupkg.sha512", "microsoft.extensions.configuration.fileextensions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.Json/6.0.1": {"sha512": "1jgLdq/UXJ71TE0toUF5VPn7yYaeyiN9sAdX2EZ6Z5DbUiWUKcMRAS5biExfu/ChYMvlGH/7ZlJijPY0zB1Vhg==", "type": "package", "path": "microsoft.extensions.configuration.json/6.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.Configuration.Json.dll", "lib/net461/Microsoft.Extensions.Configuration.Json.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Json.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Json.xml", "lib/netstandard2.1/Microsoft.Extensions.Configuration.Json.dll", "lib/netstandard2.1/Microsoft.Extensions.Configuration.Json.xml", "microsoft.extensions.configuration.json.6.0.1.nupkg.sha512", "microsoft.extensions.configuration.json.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyInjection/6.0.2": {"sha512": "gWUfUZ2ZDvwiVCxsOMComAhG43xstNWWVjV2takUZYRuDSJjO9Q5/b3tfOSkl5mcVwZAL3RZviRj5ZilxHghlw==", "type": "package", "path": "microsoft.extensions.dependencyinjection/6.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/Microsoft.Extensions.DependencyInjection.dll", "lib/net461/Microsoft.Extensions.DependencyInjection.xml", "lib/net6.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net6.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.xml", "microsoft.extensions.dependencyinjection.6.0.2.nupkg.sha512", "microsoft.extensions.dependencyinjection.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyInjection.Abstractions/6.0.0": {"sha512": "xlzi2IYREJH3/m6+lUrQlujzX8wDitm4QGnUu6kUXTQAWPuZY8i+ticFJbzfqaetLA6KR/rO6Ew/HuYD+bxifg==", "type": "package", "path": "microsoft.extensions.dependencyinjection.abstractions/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net461/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "microsoft.extensions.dependencyinjection.abstractions.6.0.0.nupkg.sha512", "microsoft.extensions.dependencyinjection.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.FileProviders.Abstractions/6.0.1": {"sha512": "Kr8S/Uunxsjyhh5D91avbME3pITDrVYp/NACiymemtwhnvxpe82Jmr99cnPqiVridg2nIkcNNRVKzl/iP5/00g==", "type": "package", "path": "microsoft.extensions.fileproviders.abstractions/6.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.FileProviders.Abstractions.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/net461/Microsoft.Extensions.FileProviders.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.FileProviders.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.xml", "microsoft.extensions.fileproviders.abstractions.6.0.1.nupkg.sha512", "microsoft.extensions.fileproviders.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.FileProviders.Physical/6.0.1": {"sha512": "k9h04ZJiQ7mr4hsLNCUc/CL4AwOAHKq0uam4JBfeZ33XSU5MHxn7l7Vpf+T5sd5HXI7us1MGrkB5awqfg5xyHw==", "type": "package", "path": "microsoft.extensions.fileproviders.physical/6.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.FileProviders.Physical.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/Microsoft.Extensions.FileProviders.Physical.dll", "lib/net461/Microsoft.Extensions.FileProviders.Physical.xml", "lib/net6.0/Microsoft.Extensions.FileProviders.Physical.dll", "lib/net6.0/Microsoft.Extensions.FileProviders.Physical.xml", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Physical.dll", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Physical.xml", "microsoft.extensions.fileproviders.physical.6.0.1.nupkg.sha512", "microsoft.extensions.fileproviders.physical.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.FileSystemGlobbing/6.0.0": {"sha512": "ip8jnL1aPiaPeKINCqaTEbvBFDmVx9dXQEBZ2HOBRXPD1eabGNqP/bKlsIcp7U2lGxiXd5xIhoFcmY8nM4Hdiw==", "type": "package", "path": "microsoft.extensions.filesystemglobbing/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.FileSystemGlobbing.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/net461/Microsoft.Extensions.FileSystemGlobbing.xml", "lib/net6.0/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/net6.0/Microsoft.Extensions.FileSystemGlobbing.xml", "lib/netstandard2.0/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/netstandard2.0/Microsoft.Extensions.FileSystemGlobbing.xml", "microsoft.extensions.filesystemglobbing.6.0.0.nupkg.sha512", "microsoft.extensions.filesystemglobbing.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging/6.0.1": {"sha512": "k6tbYaHrqY9kq7p5FfpPbddY1OImPCpXQ/PGcED6N9s5ULRp8n1PdmMzsIwIzCnhIS5bs06G/lO9LfNVpUj8jg==", "type": "package", "path": "microsoft.extensions.logging/6.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.Logging.dll", "lib/net461/Microsoft.Extensions.Logging.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.xml", "lib/netstandard2.1/Microsoft.Extensions.Logging.dll", "lib/netstandard2.1/Microsoft.Extensions.Logging.xml", "microsoft.extensions.logging.6.0.1.nupkg.sha512", "microsoft.extensions.logging.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.Abstractions/6.0.4": {"sha512": "K14wYgwOfKVELrUh5eBqlC8Wvo9vvhS3ZhIvcswV2uS/ubkTRPSQsN557EZiYUSSoZNxizG+alN4wjtdyLdcyw==", "type": "package", "path": "microsoft.extensions.logging.abstractions/6.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn3.11/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn3.11/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn4.0/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/netcoreapp3.1/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.targets", "lib/net461/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net461/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.xml", "microsoft.extensions.logging.abstractions.6.0.4.nupkg.sha512", "microsoft.extensions.logging.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Options/6.0.1": {"sha512": "v5rh5jRcLBOKOaLVyYCm4TY/RoJlxWsW7N2TAPkmlHe55/0cB0Syp979x4He1+MIXsaTvJl1WOc7b1D1PSsO3A==", "type": "package", "path": "microsoft.extensions.options/6.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.Options.dll", "lib/net461/Microsoft.Extensions.Options.xml", "lib/netstandard2.0/Microsoft.Extensions.Options.dll", "lib/netstandard2.0/Microsoft.Extensions.Options.xml", "lib/netstandard2.1/Microsoft.Extensions.Options.dll", "lib/netstandard2.1/Microsoft.Extensions.Options.xml", "microsoft.extensions.options.6.0.1.nupkg.sha512", "microsoft.extensions.options.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Primitives/6.0.1": {"sha512": "zyJfttJduuQbGEsd/TgerUEdgzHgUn/6oFgeaE04DKUMs7bbzckV7Ci1QZxf/VyQAlG41gR/GjecEScuYoHCUg==", "type": "package", "path": "microsoft.extensions.primitives/6.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Primitives.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/Microsoft.Extensions.Primitives.dll", "lib/net461/Microsoft.Extensions.Primitives.xml", "lib/net6.0/Microsoft.Extensions.Primitives.dll", "lib/net6.0/Microsoft.Extensions.Primitives.xml", "lib/netcoreapp3.1/Microsoft.Extensions.Primitives.dll", "lib/netcoreapp3.1/Microsoft.Extensions.Primitives.xml", "lib/netstandard2.0/Microsoft.Extensions.Primitives.dll", "lib/netstandard2.0/Microsoft.Extensions.Primitives.xml", "microsoft.extensions.primitives.6.0.1.nupkg.sha512", "microsoft.extensions.primitives.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.JSInterop/6.0.36": {"sha512": "e8jDwaCpfpSCR8f+UbbfU6ZrJE37svywOhevNnAuzQG9IdDJai/AgeAW19t2LMumFXVrdqhX/BIQNzSfyHpsXg==", "type": "package", "path": "microsoft.jsinterop/6.0.36", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/net6.0/Microsoft.JSInterop.dll", "lib/net6.0/Microsoft.JSInterop.xml", "microsoft.jsinterop.6.0.36.nupkg.sha512", "microsoft.jsinterop.nuspec"]}, "Microsoft.JSInterop.WebAssembly/6.0.36": {"sha512": "CVb1y5DhZfwgQ3vfo8XoNHTp0e0XSLAYSeNKh442Lv7vL4c7z6NaPpnnDV4qsOKOpbnl9PjJzhAOq6Jq7xqiKA==", "type": "package", "path": "microsoft.jsinterop.webassembly/6.0.36", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.txt", "lib/net6.0/Microsoft.JSInterop.WebAssembly.dll", "lib/net6.0/Microsoft.JSInterop.WebAssembly.xml", "microsoft.jsinterop.webassembly.6.0.36.nupkg.sha512", "microsoft.jsinterop.webassembly.nuspec"]}, "Microsoft.NET.ILLink.Analyzers/7.0.100-1.23211.1": {"sha512": "0GvbEgDGcUQA9KuWcQU1WwYHXt1tBzNr1Nls/M57rM7NA/AndFwCaCEoJpJkmxRY7xLlPDBnmGp8h5+FNqUngg==", "type": "package", "path": "microsoft.net.illink.analyzers/7.0.100-1.23211.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "analyzers/dotnet/cs/ILLink.CodeFixProvider.dll", "analyzers/dotnet/cs/ILLink.RoslynAnalyzer.dll", "build/Microsoft.NET.ILLink.Analyzers.props", "microsoft.net.illink.analyzers.7.0.100-1.23211.1.nupkg.sha512", "microsoft.net.illink.analyzers.nuspec"]}, "Microsoft.NET.ILLink.Tasks/7.0.100-1.23211.1": {"sha512": "tvG8XZYLjT0o3WicCyKBZysVWo1jC9HdCFmNRmddx3WbAz0UCsd0qKZqpiEo99VLA8Re+FzWK51OcRldQPbt2Q==", "type": "package", "path": "microsoft.net.illink.tasks/7.0.100-1.23211.1", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "Sdk/Sdk.props", "build/6.0_suppressions.xml", "build/Microsoft.NET.ILLink.Tasks.props", "build/Microsoft.NET.ILLink.targets", "microsoft.net.illink.tasks.7.0.100-1.23211.1.nupkg.sha512", "microsoft.net.illink.tasks.nuspec", "tools/net472/ILLink.Tasks.dll", "tools/net472/Mono.Cecil.dll", "tools/net472/System.Buffers.dll", "tools/net472/System.Collections.Immutable.dll", "tools/net472/System.Memory.dll", "tools/net472/System.Numerics.Vectors.dll", "tools/net472/System.Reflection.Metadata.dll", "tools/net472/System.Runtime.CompilerServices.Unsafe.dll", "tools/net7.0/ILLink.Tasks.deps.json", "tools/net7.0/ILLink.Tasks.dll", "tools/net7.0/Mono.Cecil.Pdb.dll", "tools/net7.0/Mono.Cecil.dll", "tools/net7.0/illink.deps.json", "tools/net7.0/illink.dll", "tools/net7.0/illink.runtimeconfig.json"]}, "Microsoft.NET.Sdk.WebAssembly.Pack/9.0.6": {"sha512": "0KAM0CWU4v1KbKdsyOAkqY3txcrvwVkgYkgRwCnKJS8so0B+O562YAUwDcok5YWC3qFK/+dBAwtn9KK1JL4Xrw==", "type": "package", "path": "microsoft.net.sdk.webassembly.pack/9.0.6", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "WasmAppHost/BrowserDebugHost.deps.json", "WasmAppHost/BrowserDebugHost.dll", "WasmAppHost/BrowserDebugHost.runtimeconfig.json", "WasmAppHost/BrowserDebugHost.staticwebassets.endpoints.json", "WasmAppHost/BrowserDebugProxy.dll", "WasmAppHost/Microsoft.CodeAnalysis.CSharp.Scripting.dll", "WasmAppHost/Microsoft.CodeAnalysis.CSharp.dll", "WasmAppHost/Microsoft.CodeAnalysis.Scripting.dll", "WasmAppHost/Microsoft.CodeAnalysis.dll", "WasmAppHost/Microsoft.FileFormats.dll", "WasmAppHost/Microsoft.NET.WebAssembly.Webcil.dll", "WasmAppHost/Microsoft.SymbolStore.dll", "WasmAppHost/Newtonsoft.Json.dll", "WasmAppHost/WasmAppHost.deps.json", "WasmAppHost/WasmAppHost.dll", "WasmAppHost/WasmAppHost.runtimeconfig.json", "WasmAppHost/WasmAppHost.staticwebassets.endpoints.json", "build/Microsoft.NET.Sdk.WebAssembly.Browser.props", "build/Microsoft.NET.Sdk.WebAssembly.Browser.targets", "build/Microsoft.NET.Sdk.WebAssembly.Pack.props", "build/Microsoft.NET.Sdk.WebAssembly.Pack.targets", "build/Wasm.web.config", "build/browser.runtimeconfig.template.json", "microsoft.net.sdk.webassembly.pack.9.0.6.nupkg.sha512", "microsoft.net.sdk.webassembly.pack.nuspec", "tools/net472/Microsoft.NET.Sdk.WebAssembly.Pack.Tasks.dll", "tools/net472/Microsoft.NET.WebAssembly.Webcil.dll", "tools/net9.0/Microsoft.NET.Sdk.WebAssembly.Pack.Tasks.dll", "tools/net9.0/Microsoft.NET.WebAssembly.Webcil.dll"]}, "System.Diagnostics.DiagnosticSource/6.0.2": {"sha512": "6tQaIexFycaotdGn23lf3XJ/eI1GOjQKIvQDRFN9N4pwoNsKnHuXccQ3lnQO6GX8KAb1ic+6ZofJmPdbUVwZag==", "type": "package", "path": "system.diagnostics.diagnosticsource/6.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Diagnostics.DiagnosticSource.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Diagnostics.DiagnosticSource.dll", "lib/net461/System.Diagnostics.DiagnosticSource.xml", "lib/net5.0/System.Diagnostics.DiagnosticSource.dll", "lib/net5.0/System.Diagnostics.DiagnosticSource.xml", "lib/net6.0/System.Diagnostics.DiagnosticSource.dll", "lib/net6.0/System.Diagnostics.DiagnosticSource.xml", "lib/netstandard2.0/System.Diagnostics.DiagnosticSource.dll", "lib/netstandard2.0/System.Diagnostics.DiagnosticSource.xml", "system.diagnostics.diagnosticsource.6.0.2.nupkg.sha512", "system.diagnostics.diagnosticsource.nuspec", "useSharedDesignerContext.txt"]}, "System.IO.Pipelines/6.0.3": {"sha512": "ryTgF+iFkpGZY1vRQhfCzX0xTdlV3pyaTTqRu2ETbEv+HlV7O6y7hyQURnghNIXvctl5DuZ//Dpks6HdL/Txgw==", "type": "package", "path": "system.io.pipelines/6.0.3", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.IO.Pipelines.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.IO.Pipelines.dll", "lib/net461/System.IO.Pipelines.xml", "lib/net6.0/System.IO.Pipelines.dll", "lib/net6.0/System.IO.Pipelines.xml", "lib/netcoreapp3.1/System.IO.Pipelines.dll", "lib/netcoreapp3.1/System.IO.Pipelines.xml", "lib/netstandard2.0/System.IO.Pipelines.dll", "lib/netstandard2.0/System.IO.Pipelines.xml", "system.io.pipelines.6.0.3.nupkg.sha512", "system.io.pipelines.nuspec", "useSharedDesignerContext.txt"]}, "System.Text.Json/6.0.11": {"sha512": "xqC1HIbJMBFhrpYs76oYP+NAskNVjc6v73HqLal7ECRDPIp4oRU5pPavkD//vNactCn9DA2aaald/I5N+uZ5/g==", "type": "package", "path": "system.text.json/6.0.11", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn3.11/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn3.11/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn4.0/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "buildTransitive/netcoreapp2.0/System.Text.Json.targets", "buildTransitive/netcoreapp3.1/System.Text.Json.targets", "buildTransitive/netstandard2.0/System.Text.Json.targets", "lib/net461/System.Text.Json.dll", "lib/net461/System.Text.Json.xml", "lib/net6.0/System.Text.Json.dll", "lib/net6.0/System.Text.Json.xml", "lib/netcoreapp3.1/System.Text.Json.dll", "lib/netcoreapp3.1/System.Text.Json.xml", "lib/netstandard2.0/System.Text.Json.dll", "lib/netstandard2.0/System.Text.Json.xml", "system.text.json.6.0.11.nupkg.sha512", "system.text.json.nuspec", "useSharedDesignerContext.txt"]}, "BaytaraSimple.Shared/1.0.0": {"type": "project", "path": "../Shared/BaytaraSimple.Shared.csproj", "msbuildProject": "../Shared/BaytaraSimple.Shared.csproj"}}, "projectFileDependencyGroups": {"net6.0": ["BaytaraSimple.Shared >= 1.0.0", "Microsoft.AspNetCore.Components.WebAssembly >= 6.0.36", "Microsoft.AspNetCore.Components.WebAssembly.DevServer >= 6.0.36", "Microsoft.NET.ILLink.Analyzers >= 7.0.100-1.23211.1", "Microsoft.NET.ILLink.Tasks >= 7.0.100-1.23211.1", "Microsoft.NET.Sdk.WebAssembly.Pack >= 9.0.6"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files\\DevExpress 24.1\\Components\\Offline Packages": {}, "C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Baytara\\BaytaraSimple\\Client\\BaytaraSimple.Client.csproj", "projectName": "BaytaraSimple.Client", "projectPath": "D:\\Baytara\\BaytaraSimple\\Client\\BaytaraSimple.Client.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Baytara\\BaytaraSimple\\Client\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files\\DevExpress 24.1\\Components\\Offline Packages", "C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages", "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 24.1.config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 24.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\DevExpress 24.1\\Components\\System\\Components\\Packages": {}, "C:\\Program Files\\DevExpress 24.2\\Components\\System\\Components\\Packages": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"D:\\Baytara\\BaytaraSimple\\Shared\\BaytaraSimple.Shared.csproj": {"projectPath": "D:\\Baytara\\BaytaraSimple\\Shared\\BaytaraSimple.Shared.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Microsoft.AspNetCore.Components.WebAssembly": {"target": "Package", "version": "[6.0.36, )"}, "Microsoft.AspNetCore.Components.WebAssembly.DevServer": {"suppressParent": "All", "target": "Package", "version": "[6.0.36, )"}, "Microsoft.NET.ILLink.Analyzers": {"suppressParent": "All", "target": "Package", "version": "[7.0.100-1.23211.1, )", "autoReferenced": true}, "Microsoft.NET.ILLink.Tasks": {"suppressParent": "All", "target": "Package", "version": "[7.0.100-1.23211.1, )", "autoReferenced": true}, "Microsoft.NET.Sdk.WebAssembly.Pack": {"suppressParent": "All", "target": "Package", "version": "[9.0.6, )", "autoReferenced": true}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.NETCore.App.Runtime.Mono.browser-wasm", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}, "runtimes": {"browser-wasm": {"#import": []}}}}