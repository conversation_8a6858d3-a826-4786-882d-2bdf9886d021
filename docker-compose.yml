version: '3.8'

services:
  # SQL Server Database
  sqlserver:
    image: mcr.microsoft.com/mssql/server:2022-latest
    container_name: baytara-sqlserver
    environment:
      - ACCEPT_EULA=Y
      - SA_PASSWORD=**************
      - MSSQL_PID=Express
    ports:
      - "1433:1433"
    volumes:
      - sqlserver_data:/var/opt/mssql
    networks:
      - baytara-network
    restart: unless-stopped

  # Baytara API
  baytara-api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: baytara-api
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:80
      - ConnectionStrings__DefaultConnection=Server=sqlserver,1433;Database=BaytaraDB;User Id=sa;Password=**************;TrustServerCertificate=true;
      - Jwt__Key=BaytaraSecretKeyForJWTTokenGeneration2024
      - Jwt__Issuer=BaytaraAPI
      - Jwt__Audience=BaytaraClient
      - Jwt__ExpiryInMinutes=1440
    ports:
      - "5000:80"
    depends_on:
      - sqlserver
    networks:
      - baytara-network
    restart: unless-stopped
    volumes:
      - ./logs:/app/logs

  # Nginx Reverse Proxy (Optional)
  nginx:
    image: nginx:alpine
    container_name: baytara-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - baytara-api
    networks:
      - baytara-network
    restart: unless-stopped

volumes:
  sqlserver_data:
    driver: local

networks:
  baytara-network:
    driver: bridge
