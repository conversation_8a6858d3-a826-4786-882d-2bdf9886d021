# بيطره - نظام إدارة العيادات البيطرية

نظام شامل لإدارة العيادات البيطرية مبني بتقنية ASP.NET Core API و Blazor WebAssembly مع دعم كامل للغة العربية والتخطيط من اليمين إلى اليسار (RTL).

## 🌟 المميزات الرئيسية

### 📊 لوحة التحكم الرئيسية
- إحصائيات شاملة عن أداء العيادة
- تنبيهات فورية للأدوية منخفضة المخزون
- عرض العلاجات الأخيرة والمعلقة
- رسوم بيانية تفاعلية للإيرادات والعلاجات

### 👥 إدارة المربين
- تسجيل بيانات المربين الكاملة
- ربط المربين بالحيوانات والعلاجات
- تتبع تاريخ العلاجات لكل مربي
- إحصائيات مفصلة لكل مربي

### 🐾 إدارة الحيوانات
- تصنيف الحيوانات حسب النوع والفئة
- ربط الحيوانات بالمربين
- تتبع تاريخ العلاجات لكل حيوان
- إضافة متعددة للحيوانات

### 🏥 نظام العلاج المتقدم
- تشخيص شامل مع تسجيل الأعراض
- صرف الأدوية مع تتبع المخزون
- حساب التكلفة الإجمالية تلقائياً
- متابعة حالة العلاج (معلق، قيد التنفيذ، مكتمل)

### 💊 إدارة الأدوية والمخزون
- تتبع مخزون الأدوية في الوقت الفعلي
- تنبيهات للأدوية منخفضة المخزون
- تنبيهات انتهاء الصلاحية
- إدارة الدفعات وتواريخ الانتهاء

### 🔬 نظام المختبر
- طلب الفحوصات المخبرية
- تتبع حالة الفحوصات
- ربط النتائج بالعلاجات

### 📈 التقارير والإحصائيات
- تقارير مالية شاملة
- إحصائيات العلاجات حسب نوع الحيوان
- تقارير استخدام الأدوية
- تقارير أداء المربين
- إمكانية تصدير التقارير

### ⚙️ الإعدادات والإدارة
- إدارة المستخدمين والأدوار
- إدارة الفروع المتعددة
- إدارة أنواع الحيوانات والأمراض
- النسخ الاحتياطي وتصدير البيانات

## 🛠️ التقنيات المستخدمة

### Backend (API)
- **ASP.NET Core 8.0** - إطار العمل الرئيسي
- **Entity Framework Core** - ORM لقاعدة البيانات
- **SQL Server** - قاعدة البيانات
- **JWT Authentication** - نظام المصادقة
- **AutoMapper** - تحويل البيانات
- **BCrypt** - تشفير كلمات المرور

### Frontend (Client)
- **Blazor WebAssembly** - تطبيق الويب التفاعلي
- **Bootstrap 5** - إطار العمل للتصميم
- **Font Awesome** - الأيقونات
- **Chart.js** - الرسوم البيانية
- **CSS Grid & Flexbox** - التخطيط المتجاوب

### المميزات التقنية
- **دعم كامل للغة العربية** مع RTL
- **تصميم متجاوب** يعمل على جميع الأجهزة
- **أمان متقدم** مع تشفير البيانات
- **أداء عالي** مع تحسينات قاعدة البيانات
- **واجهة مستخدم حديثة** مع تأثيرات بصرية

## 🚀 التثبيت والتشغيل

### المتطلبات
- .NET 8.0 SDK
- SQL Server 2019 أو أحدث
- Visual Studio 2022 أو VS Code

### خطوات التثبيت

1. **استنساخ المشروع**
```bash
git clone https://github.com/your-repo/baytara.git
cd baytara
```

2. **إعداد قاعدة البيانات**
```bash
cd Baytara.API
dotnet ef database update
```

3. **تشغيل API**
```bash
dotnet run --project Baytara.API
```

4. **تشغيل العميل**
```bash
dotnet run --project Baytara.Client
```

### إعداد قاعدة البيانات

قم بتحديث connection string في `appsettings.json`:

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=localhost;Database=BaytaraDB;Trusted_Connection=true;TrustServerCertificate=true;"
  }
}
```

## 📱 لقطات الشاشة

### لوحة التحكم الرئيسية
- عرض الإحصائيات الرئيسية
- تنبيهات المخزون
- العلاجات الأخيرة

### إدارة المربين
- قائمة المربين مع البحث والفلترة
- إضافة وتعديل بيانات المربين
- عرض إحصائيات كل مربي

### نظام العلاج
- واجهة متدرجة لإدخال بيانات العلاج
- اختيار الحيوان والمربي
- تشخيص وصرف الأدوية

## 🔐 نظام الأدوار والصلاحيات

### مدير النظام (System Admin)
- الوصول الكامل لجميع الوظائف
- إدارة المستخدمين والفروع
- عرض تقارير جميع الفروع

### مدير الفرع (Branch Manager)
- إدارة فرع واحد
- إضافة وإدارة المستخدمين في الفرع
- عرض تقارير الفرع

### الطبيب البيطري (Doctor)
- إجراء العلاجات والتشخيص
- صرف الأدوية
- عرض تاريخ المرضى

### فني المختبر (Technician)
- إدارة الفحوصات المخبرية
- إدخال نتائج الفحوصات

### موظف الاستقبال (Receptionist)
- تسجيل المربين والحيوانات
- جدولة المواعيد
- إدارة الاستقبال

## 🌐 دعم اللغة العربية

النظام مصمم خصيصاً للبيئة العربية مع:
- **واجهة مستخدم باللغة العربية** بالكامل
- **تخطيط RTL** (من اليمين إلى اليسار)
- **خطوط عربية** محسنة للقراءة
- **تنسيق التواريخ** بالتقويم الهجري والميلادي
- **تنسيق الأرقام** بالطريقة العربية

## 📊 قاعدة البيانات

### الجداول الرئيسية
- **Users** - المستخدمين
- **Branches** - الفروع
- **Breeders** - المربين
- **Animals** - الحيوانات
- **AnimalTypes** - أنواع الحيوانات
- **Treatments** - العلاجات
- **Medicines** - الأدوية
- **LabTests** - الفحوصات المخبرية
- **Diseases** - الأمراض

## 🤝 المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء branch جديد للميزة
3. إجراء التغييرات المطلوبة
4. إرسال Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 📞 الدعم والتواصل

- **البريد الإلكتروني**: <EMAIL>
- **الموقع الإلكتروني**: https://baytara.com
- **التوثيق**: https://docs.baytara.com

---

**بيطره** - نظام إدارة العيادات البيطرية الشامل 🐾
