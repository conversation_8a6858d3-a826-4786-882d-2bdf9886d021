using Baytara.API.Data;
using Baytara.Shared.DTOs;
using Baytara.Shared.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace Baytara.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class MedicinesController : ControllerBase
    {
        private readonly ApplicationDbContext _context;

        public MedicinesController(ApplicationDbContext context)
        {
            _context = context;
        }

        [HttpGet]
        public async Task<ActionResult<IEnumerable<MedicineDto>>> GetMedicines([FromQuery] bool? lowStock, [FromQuery] bool? expiring)
        {
            var userBranchId = GetUserBranchId();
            
            var query = _context.Medicines
                .Include(m => m.Branch)
                .AsQueryable();

            // Filter by branch if user is not system admin
            if (userBranchId.HasValue)
            {
                query = query.Where(m => m.BranchId == userBranchId.Value);
            }

            var medicines = await query
                .Select(m => new MedicineDto
                {
                    Id = m.Id,
                    Name = m.Name,
                    Description = m.Description,
                    Unit = m.Unit,
                    Quantity = m.Quantity,
                    MinimumStock = m.MinimumStock,
                    UnitPrice = m.UnitPrice,
                    ExpiryDate = m.ExpiryDate,
                    BatchNumber = m.BatchNumber,
                    Manufacturer = m.Manufacturer,
                    BranchId = m.BranchId,
                    BranchName = m.Branch.Name,
                    IsActive = m.IsActive,
                    CreatedAt = m.CreatedAt,
                    IsLowStock = m.Quantity <= m.MinimumStock,
                    IsExpiringSoon = m.ExpiryDate.HasValue && m.ExpiryDate.Value <= DateTime.UtcNow.AddDays(30),
                    IsExpired = m.ExpiryDate.HasValue && m.ExpiryDate.Value <= DateTime.UtcNow
                })
                .ToListAsync();

            // Apply filters
            if (lowStock == true)
            {
                medicines = medicines.Where(m => m.IsLowStock).ToList();
            }

            if (expiring == true)
            {
                medicines = medicines.Where(m => m.IsExpiringSoon || m.IsExpired).ToList();
            }

            return Ok(medicines.OrderByDescending(m => m.CreatedAt));
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<MedicineDto>> GetMedicine(int id)
        {
            var userBranchId = GetUserBranchId();
            
            var medicine = await _context.Medicines
                .Include(m => m.Branch)
                .Where(m => m.Id == id && (!userBranchId.HasValue || m.BranchId == userBranchId.Value))
                .Select(m => new MedicineDto
                {
                    Id = m.Id,
                    Name = m.Name,
                    Description = m.Description,
                    Unit = m.Unit,
                    Quantity = m.Quantity,
                    MinimumStock = m.MinimumStock,
                    UnitPrice = m.UnitPrice,
                    ExpiryDate = m.ExpiryDate,
                    BatchNumber = m.BatchNumber,
                    Manufacturer = m.Manufacturer,
                    BranchId = m.BranchId,
                    BranchName = m.Branch.Name,
                    IsActive = m.IsActive,
                    CreatedAt = m.CreatedAt,
                    IsLowStock = m.Quantity <= m.MinimumStock,
                    IsExpiringSoon = m.ExpiryDate.HasValue && m.ExpiryDate.Value <= DateTime.UtcNow.AddDays(30),
                    IsExpired = m.ExpiryDate.HasValue && m.ExpiryDate.Value <= DateTime.UtcNow
                })
                .FirstOrDefaultAsync();

            if (medicine == null)
            {
                return NotFound();
            }

            return Ok(medicine);
        }

        [HttpPost]
        public async Task<ActionResult<MedicineDto>> CreateMedicine([FromBody] CreateMedicineDto createMedicineDto)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var userBranchId = GetUserBranchId();
            
            // If user is not system admin, force their branch
            if (userBranchId.HasValue)
            {
                createMedicineDto.BranchId = userBranchId.Value;
            }

            // Check if branch exists
            var branchExists = await _context.Branches.AnyAsync(b => b.Id == createMedicineDto.BranchId && b.IsActive);
            if (!branchExists)
            {
                return BadRequest("الفرع المحدد غير موجود");
            }

            var medicine = new Medicine
            {
                Name = createMedicineDto.Name,
                Description = createMedicineDto.Description,
                Unit = createMedicineDto.Unit,
                Quantity = createMedicineDto.Quantity,
                MinimumStock = createMedicineDto.MinimumStock,
                UnitPrice = createMedicineDto.UnitPrice,
                ExpiryDate = createMedicineDto.ExpiryDate,
                BatchNumber = createMedicineDto.BatchNumber,
                Manufacturer = createMedicineDto.Manufacturer,
                BranchId = createMedicineDto.BranchId,
                IsActive = true,
                CreatedAt = DateTime.UtcNow
            };

            _context.Medicines.Add(medicine);
            await _context.SaveChangesAsync();

            var medicineDto = await GetMedicine(medicine.Id);
            return CreatedAtAction(nameof(GetMedicine), new { id = medicine.Id }, medicineDto.Value);
        }

        [HttpPut("{id}")]
        public async Task<ActionResult<MedicineDto>> UpdateMedicine(int id, [FromBody] UpdateMedicineDto updateMedicineDto)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var userBranchId = GetUserBranchId();
            
            var medicine = await _context.Medicines
                .Where(m => m.Id == id && (!userBranchId.HasValue || m.BranchId == userBranchId.Value))
                .FirstOrDefaultAsync();

            if (medicine == null)
            {
                return NotFound();
            }

            // If user is not system admin, don't allow branch change
            if (userBranchId.HasValue)
            {
                updateMedicineDto.BranchId = medicine.BranchId;
            }

            // Check if branch exists
            var branchExists = await _context.Branches.AnyAsync(b => b.Id == updateMedicineDto.BranchId && b.IsActive);
            if (!branchExists)
            {
                return BadRequest("الفرع المحدد غير موجود");
            }

            medicine.Name = updateMedicineDto.Name;
            medicine.Description = updateMedicineDto.Description;
            medicine.Unit = updateMedicineDto.Unit;
            medicine.Quantity = updateMedicineDto.Quantity;
            medicine.MinimumStock = updateMedicineDto.MinimumStock;
            medicine.UnitPrice = updateMedicineDto.UnitPrice;
            medicine.ExpiryDate = updateMedicineDto.ExpiryDate;
            medicine.BatchNumber = updateMedicineDto.BatchNumber;
            medicine.Manufacturer = updateMedicineDto.Manufacturer;
            medicine.BranchId = updateMedicineDto.BranchId;
            medicine.IsActive = updateMedicineDto.IsActive;
            medicine.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            var medicineDto = await GetMedicine(id);
            return Ok(medicineDto.Value);
        }

        [HttpDelete("{id}")]
        public async Task<ActionResult> DeleteMedicine(int id)
        {
            var userBranchId = GetUserBranchId();
            
            var medicine = await _context.Medicines
                .Where(m => m.Id == id && (!userBranchId.HasValue || m.BranchId == userBranchId.Value))
                .FirstOrDefaultAsync();

            if (medicine == null)
            {
                return NotFound();
            }

            // Check if medicine is used in treatments
            var isUsedInTreatments = await _context.TreatmentMedicines.AnyAsync(tm => tm.MedicineId == id);

            if (isUsedInTreatments)
            {
                // Soft delete
                medicine.IsActive = false;
                medicine.UpdatedAt = DateTime.UtcNow;
            }
            else
            {
                // Hard delete
                _context.Medicines.Remove(medicine);
            }

            await _context.SaveChangesAsync();
            return NoContent();
        }

        [HttpGet("alerts")]
        public async Task<ActionResult<IEnumerable<MedicineAlertDto>>> GetMedicineAlerts()
        {
            var userBranchId = GetUserBranchId();
            
            var query = _context.Medicines
                .Include(m => m.Branch)
                .Where(m => m.IsActive)
                .AsQueryable();

            // Filter by branch if user is not system admin
            if (userBranchId.HasValue)
            {
                query = query.Where(m => m.BranchId == userBranchId.Value);
            }

            var medicines = await query.ToListAsync();
            var alerts = new List<MedicineAlertDto>();

            foreach (var medicine in medicines)
            {
                // Low stock alert
                if (medicine.Quantity <= medicine.MinimumStock)
                {
                    alerts.Add(new MedicineAlertDto
                    {
                        Id = medicine.Id,
                        Name = medicine.Name,
                        Quantity = medicine.Quantity,
                        MinimumStock = medicine.MinimumStock,
                        ExpiryDate = medicine.ExpiryDate,
                        AlertType = "LowStock",
                        BranchName = medicine.Branch.Name
                    });
                }

                // Expiry alerts
                if (medicine.ExpiryDate.HasValue)
                {
                    var daysUntilExpiry = (medicine.ExpiryDate.Value - DateTime.UtcNow).Days;
                    
                    if (daysUntilExpiry <= 0)
                    {
                        alerts.Add(new MedicineAlertDto
                        {
                            Id = medicine.Id,
                            Name = medicine.Name,
                            Quantity = medicine.Quantity,
                            MinimumStock = medicine.MinimumStock,
                            ExpiryDate = medicine.ExpiryDate,
                            AlertType = "Expired",
                            BranchName = medicine.Branch.Name
                        });
                    }
                    else if (daysUntilExpiry <= 30)
                    {
                        alerts.Add(new MedicineAlertDto
                        {
                            Id = medicine.Id,
                            Name = medicine.Name,
                            Quantity = medicine.Quantity,
                            MinimumStock = medicine.MinimumStock,
                            ExpiryDate = medicine.ExpiryDate,
                            AlertType = "Expiring",
                            BranchName = medicine.Branch.Name
                        });
                    }
                }
            }

            return Ok(alerts.OrderBy(a => a.AlertType).ThenBy(a => a.ExpiryDate));
        }

        private int? GetUserBranchId()
        {
            var userRole = User.FindFirst(System.Security.Claims.ClaimTypes.Role)?.Value;
            if (userRole == UserRole.SystemAdmin.ToString())
            {
                return null; // System admin can see all branches
            }

            var branchIdClaim = User.FindFirst("BranchId")?.Value;
            if (int.TryParse(branchIdClaim, out int branchId))
            {
                return branchId;
            }

            return null;
        }
    }
}
