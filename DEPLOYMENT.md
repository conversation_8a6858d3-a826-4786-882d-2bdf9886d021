# دليل النشر والتشغيل - نظام بيطره

## 🚀 طرق النشر

### 1. التشغيل المحلي (Development)

#### المتطلبات
- .NET 8.0 SDK
- SQL Server 2019+ أو SQL Server Express
- Visual Studio 2022 أو VS Code

#### خطوات التشغيل

1. **استنساخ المشروع**
```bash
git clone <repository-url>
cd Baytara
```

2. **إعداد قاعدة البيانات**
```bash
# تحديث connection string في appsettings.json
# ثم تشغيل migrations
cd Baytara.API
dotnet ef database update
```

3. **تشغيل API**
```bash
cd Baytara.API
dotnet run
# API سيعمل على: https://localhost:7000
```

4. **تشغيل العميل**
```bash
cd Baytara.Client
dotnet run
# العميل سيعمل على: https://localhost:7001
```

#### بيانات تسجيل الدخول الافتراضية
- **مدير النظام**: `admin` / `admin123`
- **طبيب**: `doctor1` / `doctor123`

### 2. النشر باستخدام Docker

#### المتطلبات
- Docker Desktop
- Docker Compose

#### خطوات النشر

1. **بناء وتشغيل الحاويات**
```bash
# بناء الصور
docker-compose build

# تشغيل النظام
docker-compose up -d
```

2. **التحقق من حالة الخدمات**
```bash
docker-compose ps
```

3. **عرض السجلات**
```bash
# سجلات API
docker-compose logs baytara-api

# سجلات قاعدة البيانات
docker-compose logs sqlserver
```

#### الوصول للنظام
- **التطبيق**: http://localhost
- **API**: http://localhost:5000
- **قاعدة البيانات**: localhost:1433

### 3. النشر على الخادم (Production)

#### إعداد الخادم

1. **تثبيت المتطلبات**
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install -y docker.io docker-compose nginx certbot python3-certbot-nginx

# تشغيل Docker
sudo systemctl start docker
sudo systemctl enable docker
```

2. **إعداد SSL Certificate**
```bash
# الحصول على شهادة SSL مجانية من Let's Encrypt
sudo certbot --nginx -d yourdomain.com
```

3. **نسخ الملفات**
```bash
# نسخ ملفات المشروع إلى الخادم
scp -r . user@server:/opt/baytara/
```

4. **تشغيل النظام**
```bash
cd /opt/baytara
sudo docker-compose -f docker-compose.prod.yml up -d
```

#### إعداد Nginx للإنتاج

```nginx
server {
    listen 80;
    server_name yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yourdomain.com;
    
    ssl_certificate /etc/letsencrypt/live/yourdomain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/yourdomain.com/privkey.pem;
    
    location / {
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

## 🔧 إعدادات التكوين

### متغيرات البيئة

#### API (Baytara.API)
```env
ASPNETCORE_ENVIRONMENT=Production
ConnectionStrings__DefaultConnection=Server=sqlserver;Database=BaytaraDB;User Id=sa;Password=YourPassword;
Jwt__Key=YourSecretKey
Jwt__Issuer=BaytaraAPI
Jwt__Audience=BaytaraClient
Jwt__ExpiryInMinutes=1440
```

#### قاعدة البيانات
```env
ACCEPT_EULA=Y
SA_PASSWORD=YourStrongPassword
MSSQL_PID=Express
```

### ملف appsettings.json

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=localhost;Database=BaytaraDB;Trusted_Connection=true;TrustServerCertificate=true;"
  },
  "Jwt": {
    "Key": "YourSecretKeyForJWTTokenGeneration",
    "Issuer": "BaytaraAPI",
    "Audience": "BaytaraClient",
    "ExpiryInMinutes": 1440
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "AllowedHosts": "*"
}
```

## 📊 مراقبة النظام

### فحص الصحة (Health Checks)

```bash
# فحص حالة API
curl http://localhost:5000/health

# فحص حالة قاعدة البيانات
docker exec baytara-sqlserver /opt/mssql-tools/bin/sqlcmd -S localhost -U sa -P 'YourPassword' -Q "SELECT 1"
```

### السجلات (Logging)

```bash
# عرض سجلات API
docker logs baytara-api -f

# عرض سجلات قاعدة البيانات
docker logs baytara-sqlserver -f

# عرض سجلات Nginx
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log
```

### النسخ الاحتياطي

#### نسخ احتياطي لقاعدة البيانات
```bash
# إنشاء نسخة احتياطية
docker exec baytara-sqlserver /opt/mssql-tools/bin/sqlcmd -S localhost -U sa -P 'YourPassword' -Q "BACKUP DATABASE BaytaraDB TO DISK = '/var/opt/mssql/backup/BaytaraDB.bak'"

# نسخ الملف من الحاوية
docker cp baytara-sqlserver:/var/opt/mssql/backup/BaytaraDB.bak ./backup/
```

#### استعادة النسخة الاحتياطية
```bash
# نسخ الملف إلى الحاوية
docker cp ./backup/BaytaraDB.bak baytara-sqlserver:/var/opt/mssql/backup/

# استعادة قاعدة البيانات
docker exec baytara-sqlserver /opt/mssql-tools/bin/sqlcmd -S localhost -U sa -P 'YourPassword' -Q "RESTORE DATABASE BaytaraDB FROM DISK = '/var/opt/mssql/backup/BaytaraDB.bak' WITH REPLACE"
```

## 🔒 الأمان

### إعدادات الأمان الموصى بها

1. **تغيير كلمات المرور الافتراضية**
2. **استخدام HTTPS في الإنتاج**
3. **تحديث مفاتيح JWT بانتظام**
4. **تفعيل جدار الحماية**
5. **تحديث النظام والحاويات بانتظام**

### جدار الحماية (UFW)
```bash
# تفعيل جدار الحماية
sudo ufw enable

# السماح بالمنافذ المطلوبة
sudo ufw allow 22    # SSH
sudo ufw allow 80    # HTTP
sudo ufw allow 443   # HTTPS
```

## 🚨 استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### 1. خطأ في الاتصال بقاعدة البيانات
```bash
# التحقق من حالة قاعدة البيانات
docker logs baytara-sqlserver

# إعادة تشغيل قاعدة البيانات
docker-compose restart sqlserver
```

#### 2. خطأ في تشغيل API
```bash
# التحقق من السجلات
docker logs baytara-api

# التحقق من متغيرات البيئة
docker exec baytara-api env
```

#### 3. مشاكل في الشبكة
```bash
# التحقق من الشبكات
docker network ls

# فحص الاتصال بين الحاويات
docker exec baytara-api ping sqlserver
```

## 📈 تحسين الأداء

### إعدادات قاعدة البيانات
- تحسين فهارس قاعدة البيانات
- تنظيف السجلات القديمة
- مراقبة استخدام الذاكرة

### إعدادات الخادم
- تحسين إعدادات Nginx
- تفعيل ضغط Gzip
- استخدام CDN للملفات الثابتة

## 📞 الدعم

للحصول على المساعدة:
- **البريد الإلكتروني**: <EMAIL>
- **التوثيق**: https://docs.baytara.com
- **المشاكل**: https://github.com/baytara/issues
