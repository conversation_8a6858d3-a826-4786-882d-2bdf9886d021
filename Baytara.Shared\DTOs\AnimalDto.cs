using Baytara.Shared.Models;
using System.ComponentModel.DataAnnotations;

namespace Baytara.Shared.DTOs
{
    public class AnimalDto
    {
        public int Id { get; set; }
        public string? Name { get; set; }
        public int AnimalTypeId { get; set; }
        public string AnimalTypeName { get; set; } = string.Empty;
        public AnimalCategory AnimalCategory { get; set; }
        public decimal TreatmentPrice { get; set; }
        public int BreederId { get; set; }
        public string BreederName { get; set; } = string.Empty;
        public Gender Gender { get; set; }
        public int? Age { get; set; }
        public string? Color { get; set; }
        public string? Breed { get; set; }
        public decimal? Weight { get; set; }
        public string? Notes { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedAt { get; set; }
        public int TreatmentsCount { get; set; }
    }
    
    public class CreateAnimalDto
    {
        [StringLength(50, ErrorMessage = "اسم الحيوان يجب أن يكون أقل من 50 حرف")]
        public string? Name { get; set; }
        
        [Required(ErrorMessage = "نوع الحيوان مطلوب")]
        public int AnimalTypeId { get; set; }
        
        [Required(ErrorMessage = "المربي مطلوب")]
        public int BreederId { get; set; }
        
        [Required(ErrorMessage = "الجنس مطلوب")]
        public Gender Gender { get; set; }
        
        [Range(0, 100, ErrorMessage = "العمر يجب أن يكون بين 0 و 100")]
        public int? Age { get; set; }
        
        [StringLength(50, ErrorMessage = "اللون يجب أن يكون أقل من 50 حرف")]
        public string? Color { get; set; }
        
        [StringLength(100, ErrorMessage = "السلالة يجب أن تكون أقل من 100 حرف")]
        public string? Breed { get; set; }
        
        [Range(0, 10000, ErrorMessage = "الوزن يجب أن يكون بين 0 و 10000")]
        public decimal? Weight { get; set; }
        
        [StringLength(500, ErrorMessage = "الملاحظات يجب أن تكون أقل من 500 حرف")]
        public string? Notes { get; set; }
    }
    
    public class UpdateAnimalDto
    {
        [StringLength(50, ErrorMessage = "اسم الحيوان يجب أن يكون أقل من 50 حرف")]
        public string? Name { get; set; }
        
        [Required(ErrorMessage = "نوع الحيوان مطلوب")]
        public int AnimalTypeId { get; set; }
        
        [Required(ErrorMessage = "الجنس مطلوب")]
        public Gender Gender { get; set; }
        
        [Range(0, 100, ErrorMessage = "العمر يجب أن يكون بين 0 و 100")]
        public int? Age { get; set; }
        
        [StringLength(50, ErrorMessage = "اللون يجب أن يكون أقل من 50 حرف")]
        public string? Color { get; set; }
        
        [StringLength(100, ErrorMessage = "السلالة يجب أن تكون أقل من 100 حرف")]
        public string? Breed { get; set; }
        
        [Range(0, 10000, ErrorMessage = "الوزن يجب أن يكون بين 0 و 10000")]
        public decimal? Weight { get; set; }
        
        [StringLength(500, ErrorMessage = "الملاحظات يجب أن تكون أقل من 500 حرف")]
        public string? Notes { get; set; }
        
        public bool IsActive { get; set; }
    }
    
    public class CreateMultipleAnimalsDto
    {
        [Required(ErrorMessage = "نوع الحيوان مطلوب")]
        public int AnimalTypeId { get; set; }
        
        [Required(ErrorMessage = "المربي مطلوب")]
        public int BreederId { get; set; }
        
        [Required(ErrorMessage = "عدد الحيوانات مطلوب")]
        [Range(1, 100, ErrorMessage = "عدد الحيوانات يجب أن يكون بين 1 و 100")]
        public int Count { get; set; }
        
        [StringLength(500, ErrorMessage = "الملاحظات يجب أن تكون أقل من 500 حرف")]
        public string? Notes { get; set; }
    }
}
