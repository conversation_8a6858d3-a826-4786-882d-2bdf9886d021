using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using Baytara.API.Data;
using Baytara.Shared.DTOs;
using Baytara.Shared.Models;

namespace Baytara.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class ReportsController : ControllerBase
    {
        private readonly ApplicationDbContext _context;

        public ReportsController(ApplicationDbContext context)
        {
            _context = context;
        }

        [HttpGet("financial")]
        public async Task<ActionResult<FinancialReportDto>> GetFinancialReport(
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null,
            [FromQuery] int? branchId = null)
        {
            var start = startDate ?? DateTime.UtcNow.AddDays(-30);
            var end = endDate ?? DateTime.UtcNow;

            var treatmentsQuery = _context.Treatments
                .Where(t => t.TreatmentDate >= start && t.TreatmentDate <= end);

            var labTestsQuery = _context.LabTests
                .Where(lt => lt.RequestedAt >= start && lt.RequestedAt <= end);

            if (branchId.HasValue)
            {
                treatmentsQuery = treatmentsQuery.Where(t => t.BranchId == branchId.Value);
                labTestsQuery = labTestsQuery.Where(lt => lt.BranchId == branchId.Value);
            }

            var treatments = await treatmentsQuery.ToListAsync();
            var labTests = await labTestsQuery.ToListAsync();

            var treatmentsRevenue = treatments.Sum(t => t.TotalCost);
            var labTestsRevenue = labTests.Sum(lt => lt.Cost);
            var totalRevenue = treatmentsRevenue + labTestsRevenue;

            // Calculate monthly revenue
            var monthlyRevenue = treatments
                .GroupBy(t => new { t.TreatmentDate.Year, t.TreatmentDate.Month })
                .Select(g => new MonthlyRevenueDto
                {
                    Year = g.Key.Year,
                    Month = g.Key.Month,
                    MonthName = GetMonthName(g.Key.Month),
                    Revenue = g.Sum(t => t.TotalCost),
                    TreatmentsCount = g.Count(),
                    LabTestsCount = labTests.Count(lt => lt.RequestedAt.Year == g.Key.Year && lt.RequestedAt.Month == g.Key.Month)
                })
                .OrderBy(m => m.Year)
                .ThenBy(m => m.Month)
                .ToList();

            var report = new FinancialReportDto
            {
                TotalRevenue = totalRevenue,
                TotalCosts = totalRevenue * 0.6m, // Mock cost calculation
                NetProfit = totalRevenue * 0.4m, // Mock profit calculation
                AverageTreatmentCost = treatments.Any() ? treatments.Average(t => t.TotalCost) : 0,
                AverageDailyRevenue = totalRevenue / Math.Max((decimal)(end - start).Days, 1),
                TotalTreatments = treatments.Count,
                TotalLabTests = labTests.Count,
                LabTestsRevenue = labTestsRevenue,
                TreatmentsRevenue = treatmentsRevenue,
                MonthlyRevenue = monthlyRevenue
            };

            return Ok(report);
        }

        [HttpGet("medicine-usage")]
        public async Task<ActionResult<IEnumerable<MedicineUsageDto>>> GetMedicineUsage(
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null,
            [FromQuery] int? branchId = null)
        {
            var start = startDate ?? DateTime.UtcNow.AddDays(-30);
            var end = endDate ?? DateTime.UtcNow;

            var query = _context.TreatmentMedicines
                .Include(tm => tm.Medicine)
                .Include(tm => tm.Treatment)
                .Where(tm => tm.Treatment.TreatmentDate >= start && tm.Treatment.TreatmentDate <= end);

            if (branchId.HasValue)
            {
                query = query.Where(tm => tm.Treatment.BranchId == branchId.Value);
            }

            var medicineUsage = await query
                .GroupBy(tm => new { tm.MedicineId, tm.Medicine.Name })
                .Select(g => new MedicineUsageDto
                {
                    MedicineId = g.Key.MedicineId,
                    MedicineName = g.Key.Name,
                    TotalUsed = g.Sum(tm => tm.Quantity),
                    TotalValue = g.Sum(tm => tm.Quantity * tm.UnitPrice),
                    TreatmentsCount = g.Select(tm => tm.TreatmentId).Distinct().Count(),
                    AveragePerTreatment = (decimal)g.Average(tm => tm.Quantity),
                    LastUsed = g.Max(tm => tm.Treatment.TreatmentDate)
                })
                .OrderByDescending(mu => mu.TotalValue)
                .ToListAsync();

            return Ok(medicineUsage);
        }

        [HttpGet("animal-type-statistics")]
        public async Task<ActionResult<IEnumerable<AnimalTypeStatisticsDto>>> GetAnimalTypeStatistics(
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null,
            [FromQuery] int? branchId = null)
        {
            var start = startDate ?? DateTime.UtcNow.AddDays(-30);
            var end = endDate ?? DateTime.UtcNow;

            var treatmentsQuery = _context.Treatments
                .Include(t => t.Animal)
                    .ThenInclude(a => a.AnimalType)
                .Where(t => t.TreatmentDate >= start && t.TreatmentDate <= end);

            var labTestsQuery = _context.LabTests
                .Include(lt => lt.Animal)
                    .ThenInclude(a => a.AnimalType)
                .Where(lt => lt.RequestedAt >= start && lt.RequestedAt <= end);

            if (branchId.HasValue)
            {
                treatmentsQuery = treatmentsQuery.Where(t => t.BranchId == branchId.Value);
                labTestsQuery = labTestsQuery.Where(lt => lt.BranchId == branchId.Value);
            }

            var treatments = await treatmentsQuery.ToListAsync();
            var labTests = await labTestsQuery.ToListAsync();

            var animalTypes = await _context.AnimalTypes.ToListAsync();

            var statistics = animalTypes.Select(at => new AnimalTypeStatisticsDto
            {
                AnimalTypeId = at.Id,
                AnimalTypeName = at.Name,
                AnimalsCount = _context.Animals.Count(a => a.AnimalTypeId == at.Id),
                TreatmentsCount = treatments.Count(t => t.Animal.AnimalTypeId == at.Id),
                TotalRevenue = treatments.Where(t => t.Animal.AnimalTypeId == at.Id).Sum(t => t.TotalCost),
                AverageTreatmentCost = treatments.Where(t => t.Animal.AnimalTypeId == at.Id).Any() 
                    ? treatments.Where(t => t.Animal.AnimalTypeId == at.Id).Average(t => t.TotalCost) 
                    : 0,
                MostCommonDisease = "غير محدد", // This would need more complex logic
                LabTestsCount = labTests.Count(lt => lt.Animal.AnimalTypeId == at.Id)
            }).Where(s => s.TreatmentsCount > 0 || s.LabTestsCount > 0).ToList();

            return Ok(statistics);
        }

        [HttpGet("branch-performance")]
        public async Task<ActionResult<IEnumerable<BranchPerformanceDto>>> GetBranchPerformance(
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null)
        {
            var start = startDate ?? DateTime.UtcNow.AddDays(-30);
            var end = endDate ?? DateTime.UtcNow;

            var branches = await _context.Branches.ToListAsync();

            var performance = new List<BranchPerformanceDto>();

            foreach (var branch in branches)
            {
                var treatments = await _context.Treatments
                    .Where(t => t.BranchId == branch.Id && t.TreatmentDate >= start && t.TreatmentDate <= end)
                    .ToListAsync();

                var labTests = await _context.LabTests
                    .Where(lt => lt.BranchId == branch.Id && lt.RequestedAt >= start && lt.RequestedAt <= end)
                    .ToListAsync();

                var activeBreeders = await _context.Treatments
                    .Where(t => t.BranchId == branch.Id && t.TreatmentDate >= start && t.TreatmentDate <= end)
                    .Select(t => t.BreederId)
                    .Distinct()
                    .CountAsync();

                var totalAnimals = await _context.Animals
                    .Where(a => a.Breeder.BranchId == branch.Id)
                    .CountAsync();

                var staffCount = await _context.Users
                    .Where(u => u.BranchId == branch.Id)
                    .CountAsync();

                var totalRevenue = treatments.Sum(t => t.TotalCost) + labTests.Sum(lt => lt.Cost);

                performance.Add(new BranchPerformanceDto
                {
                    BranchId = branch.Id,
                    BranchName = branch.Name,
                    TotalRevenue = totalRevenue,
                    TreatmentsCount = treatments.Count,
                    LabTestsCount = labTests.Count,
                    ActiveBreeders = activeBreeders,
                    TotalAnimals = totalAnimals,
                    AverageRevenuePerTreatment = treatments.Any() ? treatments.Average(t => t.TotalCost) : 0,
                    StaffCount = staffCount
                });
            }

            return Ok(performance.OrderByDescending(p => p.TotalRevenue));
        }

        [HttpGet("breeder-statistics")]
        public async Task<ActionResult<IEnumerable<BreederStatisticsDto>>> GetBreederStatistics(
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null,
            [FromQuery] int? branchId = null,
            [FromQuery] int top = 20)
        {
            var start = startDate ?? DateTime.UtcNow.AddDays(-30);
            var end = endDate ?? DateTime.UtcNow;

            var query = _context.Breeders.AsQueryable();

            if (branchId.HasValue)
            {
                query = query.Where(b => b.BranchId == branchId.Value);
            }

            var breeders = await query.ToListAsync();

            var statistics = new List<BreederStatisticsDto>();

            foreach (var breeder in breeders)
            {
                var treatments = await _context.Treatments
                    .Where(t => t.BreederId == breeder.Id && t.TreatmentDate >= start && t.TreatmentDate <= end)
                    .ToListAsync();

                var animalsCount = await _context.Animals
                    .Where(a => a.BreederId == breeder.Id)
                    .CountAsync();

                var totalSpent = treatments.Sum(t => t.TotalCost);
                var lastVisit = treatments.Any() ? treatments.Max(t => t.TreatmentDate) : breeder.CreatedAt;

                var mostTreatedAnimalType = await _context.Treatments
                    .Include(t => t.Animal)
                        .ThenInclude(a => a.AnimalType)
                    .Where(t => t.BreederId == breeder.Id && t.TreatmentDate >= start && t.TreatmentDate <= end)
                    .GroupBy(t => t.Animal.AnimalType.Name)
                    .OrderByDescending(g => g.Count())
                    .Select(g => g.Key)
                    .FirstOrDefaultAsync() ?? "غير محدد";

                statistics.Add(new BreederStatisticsDto
                {
                    BreederName = breeder.Name,
                    Name = breeder.Name,
                    AnimalsCount = animalsCount,
                    TreatmentsCount = treatments.Count,
                    TotalSpent = totalSpent,
                    LastVisit = lastVisit,
                    AverageSpentPerTreatment = treatments.Any() ? totalSpent / treatments.Count : 0,
                    MostTreatedAnimalType = mostTreatedAnimalType

                });
            }

            return Ok(statistics.OrderByDescending(s => s.TotalSpent).Take(top));
        }

        [HttpPost("export")]
        public async Task<IActionResult> ExportReport([FromBody] DetailedReportRequestDto request)
        {
            // This would implement actual export functionality
            // For now, return a placeholder response
            
            var fileName = $"report_{request.ReportType}_{DateTime.UtcNow:yyyyMMdd_HHmmss}.{request.ExportFormat}";
            
            return Ok(new { 
                message = "تم إنشاء التقرير بنجاح", 
                fileName = fileName,
                downloadUrl = $"/api/reports/download/{fileName}"
            });
        }

        private string GetMonthName(int month)
        {
            return month switch
            {
                1 => "يناير",
                2 => "فبراير",
                3 => "مارس",
                4 => "أبريل",
                5 => "مايو",
                6 => "يونيو",
                7 => "يوليو",
                8 => "أغسطس",
                9 => "سبتمبر",
                10 => "أكتوبر",
                11 => "نوفمبر",
                12 => "ديسمبر",
                _ => "غير محدد"
            };
        }
    }
}
