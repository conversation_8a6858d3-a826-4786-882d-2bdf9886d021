@page "/"
@using Baytara.Client.Services
@using Baytara.Shared.DTOs
@inject IApiService ApiService
@inject IJSRuntime JSRuntime

<PageTitle>الرئيسية - بيطره</PageTitle>

<div class="dashboard">
    <div class="dashboard-header">
        <h1 class="page-title">
            <i class="fas fa-tachometer-alt"></i>
            لوحة التحكم الرئيسية
        </h1>
        <p class="page-subtitle">نظرة عامة على أداء العيادة البيطرية</p>
    </div>

    @if (isLoading)
    {
        <div class="loading-container">
            <div class="spinner">
                <i class="fas fa-spinner fa-spin"></i>
            </div>
            <p>جاري تحميل البيانات...</p>
        </div>
    }
    else if (dashboardData != null)
    {
        <!-- Statistics Cards -->
        <div class="stats-grid">
            <div class="stat-card primary">
                <div class="stat-icon">
                    <i class="fas fa-users"></i>
                </div>
                <div class="stat-content">
                    <h3>@dashboardData.TotalBreeders</h3>
                    <p>إجمالي المربين</p>
                </div>
            </div>

            <div class="stat-card success">
                <div class="stat-icon">
                    <i class="fas fa-paw"></i>
                </div>
                <div class="stat-content">
                    <h3>@dashboardData.TotalAnimals</h3>
                    <p>إجمالي الحيوانات</p>
                </div>
            </div>

            <div class="stat-card info">
                <div class="stat-icon">
                    <i class="fas fa-stethoscope"></i>
                </div>
                <div class="stat-content">
                    <h3>@dashboardData.TotalTreatments</h3>
                    <p>إجمالي العلاجات</p>
                </div>
            </div>

            <div class="stat-card warning">
                <div class="stat-icon">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="stat-content">
                    <h3>@dashboardData.PendingTreatments</h3>
                    <p>العلاجات المعلقة</p>
                </div>
            </div>

            <div class="stat-card danger">
                <div class="stat-icon">
                    <i class="fas fa-pills"></i>
                </div>
                <div class="stat-content">
                    <h3>@dashboardData.LowStockMedicines</h3>
                    <p>أدوية منخفضة المخزون</p>
                </div>
            </div>

            <div class="stat-card revenue">
                <div class="stat-icon">
                    <i class="fas fa-money-bill-wave"></i>
                </div>
                <div class="stat-content">
                    <h3>@dashboardData.MonthlyRevenue.ToString("N0") ر.س</h3>
                    <p>إيرادات الشهر</p>
                </div>
            </div>
        </div>

        <!-- Charts and Recent Data -->
        <div class="dashboard-content">
            <div class="dashboard-row">
                <!-- Recent Treatments -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3>
                            <i class="fas fa-history"></i>
                            العلاجات الأخيرة
                        </h3>
                        <a href="/treatment" class="view-all-link">عرض الكل</a>
                    </div>
                    <div class="card-content">
                        @if (dashboardData.RecentTreatments.Any())
                        {
                            <div class="treatments-list">
                                @foreach (var treatment in dashboardData.RecentTreatments.Take(5))
                                {
                                    <div class="treatment-item">
                                        <div class="treatment-info">
                                            <h4>@treatment.AnimalTypeName - @treatment.BreederName</h4>
                                            <p>الطبيب: @treatment.DoctorName</p>
                                            <small>@treatment.TreatmentDate.ToString("dd/MM/yyyy")</small>
                                        </div>
                                        <div class="treatment-cost">
                                            @treatment.TotalCost.ToString("N0") ر.س
                                        </div>
                                    </div>
                                }
                            </div>
                        }
                        else
                        {
                            <div class="empty-state">
                                <i class="fas fa-clipboard-list"></i>
                                <p>لا توجد علاجات حديثة</p>
                            </div>
                        }
                    </div>
                </div>

                <!-- Medicine Alerts -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3>
                            <i class="fas fa-exclamation-triangle"></i>
                            تنبيهات الأدوية
                        </h3>
                        <a href="/medicines" class="view-all-link">عرض الكل</a>
                    </div>
                    <div class="card-content">
                        @if (dashboardData.MedicineAlerts.Any())
                        {
                            <div class="alerts-list">
                                @foreach (var alert in dashboardData.MedicineAlerts.Take(5))
                                {
                                    <div class="alert-item @GetAlertClass(alert.AlertType)">
                                        <div class="alert-icon">
                                            <i class="@GetAlertIcon(alert.AlertType)"></i>
                                        </div>
                                        <div class="alert-info">
                                            <h4>@alert.Name</h4>
                                            <p>@GetAlertMessage(alert)</p>
                                        </div>
                                    </div>
                                }
                            </div>
                        }
                        else
                        {
                            <div class="empty-state">
                                <i class="fas fa-check-circle"></i>
                                <p>لا توجد تنبيهات</p>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>
    }
    else
    {
        <div class="error-container">
            <i class="fas fa-exclamation-circle"></i>
            <p>حدث خطأ في تحميل البيانات</p>
            <button class="btn btn-primary" @onclick="LoadDashboardData">
                <i class="fas fa-refresh"></i>
                إعادة المحاولة
            </button>
        </div>
    }
</div>

<style>
    .dashboard {
        width: 100%;
        max-width: none;
        margin: 0;
        padding: 0;
        color: white;
    }

    .dashboard-header {
        margin-bottom: 30px;
        text-align: center;
    }

    .page-title {
        font-size: 2.8rem;
        color: white;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 15px;
        font-weight: 700;
        text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    }

    .page-subtitle {
        font-size: 1.3rem;
        color: rgba(255, 255, 255, 0.8);
        margin-bottom: 0;
        font-weight: 400;
    }

    /* Responsive Page Titles */
    @media (max-width: 992px) {
        .page-title {
            font-size: 2.2rem;
            gap: 12px;
        }

        .page-subtitle {
            font-size: 1.1rem;
        }

        .dashboard-header {
            margin-bottom: 25px;
        }
    }

    @media (max-width: 768px) {
        .page-title {
            font-size: 2rem;
            gap: 10px;
        }

        .page-subtitle {
            font-size: 1rem;
        }

        .dashboard-header {
            margin-bottom: 20px;
        }
    }

    @media (max-width: 480px) {
        .page-title {
            font-size: 1.8rem;
            gap: 8px;
            flex-direction: column;
        }

        .page-subtitle {
            font-size: 0.95rem;
        }

        .dashboard-header {
            margin-bottom: 15px;
        }
    }

    .loading-container,
    .error-container {
        text-align: center;
        padding: 60px 20px;
        color: #666;
    }

    .loading-container .spinner {
        font-size: 3rem;
        color: #667eea;
        margin-bottom: 20px;
    }

    .error-container i {
        font-size: 3rem;
        color: #dc3545;
        margin-bottom: 20px;
    }

    /* Statistics Grid */
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 25px;
        margin-bottom: 40px;
    }

    /* Responsive Statistics Grid */
    @media (max-width: 1200px) {
        .stats-grid {
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
        }
    }

    @media (max-width: 992px) {
        .stats-grid {
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
        }
    }

    @media (max-width: 768px) {
        .stats-grid {
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }
    }

    @media (max-width: 480px) {
        .stats-grid {
            grid-template-columns: 1fr;
            gap: 15px;
        }
    }

    .stat-card {
        background: rgba(255, 255, 255, 0.15);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 20px;
        padding: 30px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        display: flex;
        align-items: center;
        gap: 25px;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        border-left: 5px solid;
        min-height: 120px;
    }

    /* Responsive Stat Cards */
    @media (max-width: 992px) {
        .stat-card {
            padding: 25px;
            gap: 20px;
            min-height: 110px;
        }
    }

    @media (max-width: 768px) {
        .stat-card {
            padding: 20px;
            gap: 15px;
            min-height: 100px;
            flex-direction: row;
        }
    }

    @media (max-width: 480px) {
        .stat-card {
            padding: 20px;
            gap: 15px;
            min-height: 90px;
            flex-direction: column;
            text-align: center;
        }
    }

    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }

    .stat-card.primary { border-left-color: #667eea; }
    .stat-card.success { border-left-color: #28a745; }
    .stat-card.info { border-left-color: #17a2b8; }
    .stat-card.warning { border-left-color: #ffc107; }
    .stat-card.danger { border-left-color: #dc3545; }
    .stat-card.revenue { border-left-color: #6f42c1; }

    .stat-icon {
        width: 70px;
        height: 70px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.8rem;
        color: white;
    }

    /* Responsive Icons */
    @media (max-width: 992px) {
        .stat-icon {
            width: 60px;
            height: 60px;
            font-size: 1.6rem;
        }
    }

    @media (max-width: 768px) {
        .stat-icon {
            width: 55px;
            height: 55px;
            font-size: 1.4rem;
        }
    }

    @media (max-width: 480px) {
        .stat-icon {
            width: 50px;
            height: 50px;
            font-size: 1.3rem;
        }
    }

    .stat-card.primary .stat-icon { background: #667eea; }
    .stat-card.success .stat-icon { background: #28a745; }
    .stat-card.info .stat-icon { background: #17a2b8; }
    .stat-card.warning .stat-icon { background: #ffc107; }
    .stat-card.danger .stat-icon { background: #dc3545; }
    .stat-card.revenue .stat-icon { background: #6f42c1; }

    .stat-content h3 {
        font-size: 2.5rem;
        font-weight: 700;
        color: white;
        margin-bottom: 8px;
        text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
    }

    .stat-content p {
        font-size: 1.2rem;
        color: rgba(255, 255, 255, 0.8);
        margin-bottom: 0;
        font-weight: 500;
    }

    /* Responsive Text */
    @media (max-width: 992px) {
        .stat-content h3 {
            font-size: 2.2rem;
        }

        .stat-content p {
            font-size: 1.1rem;
        }
    }

    @media (max-width: 768px) {
        .stat-content h3 {
            font-size: 2rem;
        }

        .stat-content p {
            font-size: 1rem;
        }
    }

    @media (max-width: 480px) {
        .stat-content h3 {
            font-size: 1.8rem;
            margin-bottom: 5px;
        }

        .stat-content p {
            font-size: 0.95rem;
        }
    }

    /* Dashboard Content */
    .dashboard-content {
        margin-top: 40px;
    }

    .dashboard-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 40px;
        margin-bottom: 40px;
    }

    /* Responsive Dashboard Row */
    @media (max-width: 992px) {
        .dashboard-row {
            gap: 30px;
            margin-bottom: 30px;
        }
    }

    @media (max-width: 768px) {
        .dashboard-row {
            grid-template-columns: 1fr;
            gap: 25px;
            margin-bottom: 25px;
        }
    }

    @media (max-width: 480px) {
        .dashboard-row {
            gap: 20px;
            margin-bottom: 20px;
        }
    }

    .dashboard-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }

    /* Responsive Dashboard Cards */
    @media (max-width: 992px) {
        .dashboard-card {
            border-radius: 12px;
        }
    }

    @media (max-width: 768px) {
        .dashboard-card {
            border-radius: 10px;
        }
    }

    .card-header {
        padding: 20px 25px;
        border-bottom: 1px solid #eee;
        display: flex;
        justify-content: space-between;
        align-items: center;
        background: #f8f9fa;
    }

    /* Responsive Card Headers */
    @media (max-width: 768px) {
        .card-header {
            padding: 15px 20px;
            flex-direction: column;
            gap: 10px;
            text-align: center;
        }
    }

    @media (max-width: 480px) {
        .card-header {
            padding: 12px 15px;
        }
    }

    .card-header h3 {
        font-size: 1.3rem;
        color: #333;
        margin-bottom: 0;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .view-all-link {
        color: #667eea;
        text-decoration: none;
        font-size: 0.9rem;
        transition: color 0.3s ease;
    }

    .view-all-link:hover {
        color: #5a6fd8;
    }

    .card-content {
        padding: 25px;
    }

    /* Responsive Card Content */
    @media (max-width: 768px) {
        .card-content {
            padding: 20px;
        }
    }

    @media (max-width: 480px) {
        .card-content {
            padding: 15px;
        }
    }

    /* Treatments List */
    .treatments-list {
        display: flex;
        flex-direction: column;
        gap: 15px;
    }

    .treatment-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px;
        background: #f8f9fa;
        border-radius: 10px;
        border-right: 4px solid #667eea;
    }

    .treatment-info h4 {
        font-size: 1rem;
        color: #333;
        margin-bottom: 5px;
    }

    .treatment-info p {
        font-size: 0.9rem;
        color: #666;
        margin-bottom: 3px;
    }

    .treatment-info small {
        font-size: 0.8rem;
        color: #999;
    }

    .treatment-cost {
        font-size: 1.1rem;
        font-weight: 600;
        color: #28a745;
    }

    /* Alerts List */
    .alerts-list {
        display: flex;
        flex-direction: column;
        gap: 12px;
    }

    .alert-item {
        display: flex;
        align-items: center;
        gap: 15px;
        padding: 12px 15px;
        border-radius: 8px;
        border-right: 4px solid;
    }

    .alert-item.low-stock {
        background: rgba(255, 193, 7, 0.1);
        border-right-color: #ffc107;
    }

    .alert-item.expiring {
        background: rgba(255, 152, 0, 0.1);
        border-right-color: #ff9800;
    }

    .alert-item.expired {
        background: rgba(220, 53, 69, 0.1);
        border-right-color: #dc3545;
    }

    .alert-icon {
        width: 35px;
        height: 35px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 0.9rem;
    }

    .alert-item.low-stock .alert-icon { background: #ffc107; }
    .alert-item.expiring .alert-icon { background: #ff9800; }
    .alert-item.expired .alert-icon { background: #dc3545; }

    .alert-info h4 {
        font-size: 0.95rem;
        color: #333;
        margin-bottom: 3px;
    }

    .alert-info p {
        font-size: 0.85rem;
        color: #666;
        margin-bottom: 0;
    }

    /* Empty State */
    .empty-state {
        text-align: center;
        padding: 40px 20px;
        color: #999;
    }

    .empty-state i {
        font-size: 3rem;
        margin-bottom: 15px;
        color: #ddd;
    }

    /* Responsive Design */
    @@media (max-width: 768px) {
        .stats-grid {
            grid-template-columns: 1fr;
        }

        .dashboard-row {
            grid-template-columns: 1fr;
        }

        .page-title {
            font-size: 2rem;
        }

        .treatment-item {
            flex-direction: column;
            align-items: flex-start;
            gap: 10px;
        }
    }
</style>

@code {
    private DashboardDto? dashboardData;
    private bool isLoading = true;

    protected override async Task OnInitializedAsync()
    {
        await LoadDashboardData();
    }

    private async Task LoadDashboardData()
    {
        try
        {
            isLoading = true;
            StateHasChanged();

            dashboardData = await ApiService.GetDashboardDataAsync();
        }
        catch (Exception ex)
        {
            // Handle error
            dashboardData = null;
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private string GetAlertClass(string alertType)
    {
        return alertType.ToLower() switch
        {
            "lowstock" => "low-stock",
            "expiring" => "expiring",
            "expired" => "expired",
            _ => ""
        };
    }

    private string GetAlertIcon(string alertType)
    {
        return alertType.ToLower() switch
        {
            "lowstock" => "fas fa-exclamation-triangle",
            "expiring" => "fas fa-clock",
            "expired" => "fas fa-times-circle",
            _ => "fas fa-info-circle"
        };
    }

    private string GetAlertMessage(MedicineAlertDto alert)
    {
        return alert.AlertType.ToLower() switch
        {
            "lowstock" => $"المخزون منخفض: {alert.Quantity} من {alert.MinimumStock}",
            "expiring" => $"ينتهي في: {alert.ExpiryDate?.ToString("dd/MM/yyyy")}",
            "expired" => $"منتهي الصلاحية: {alert.ExpiryDate?.ToString("dd/MM/yyyy")}",
            _ => "تنبيه"
        };
    }
}
