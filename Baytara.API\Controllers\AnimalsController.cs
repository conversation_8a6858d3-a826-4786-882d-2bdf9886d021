using Baytara.API.Data;
using Baytara.Shared.DTOs;
using Baytara.Shared.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace Baytara.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class AnimalsController : ControllerBase
    {
        private readonly ApplicationDbContext _context;

        public AnimalsController(ApplicationDbContext context)
        {
            _context = context;
        }

        [HttpGet]
        public async Task<ActionResult<IEnumerable<AnimalDto>>> GetAnimals([FromQuery] int? breederId)
        {
            var userBranchId = GetUserBranchId();
            
            var query = _context.Animals
                .Include(a => a.AnimalType)
                .Include(a => a.Breeder)
                .ThenInclude(b => b.Branch)
                .AsQueryable();

            // Filter by branch if user is not system admin
            if (userBranchId.HasValue)
            {
                query = query.Where(a => a.Breeder.BranchId == userBranchId.Value);
            }

            // Filter by breeder if specified
            if (breederId.HasValue)
            {
                query = query.Where(a => a.BreederId == breederId.Value);
            }

            var animals = await query
                .Select(a => new AnimalDto
                {
                    Id = a.Id,
                    Name = a.Name,
                    AnimalTypeId = a.AnimalTypeId,
                    AnimalTypeName = a.AnimalType.Name,
                    AnimalCategory = a.AnimalType.Category,
                    TreatmentPrice = a.AnimalType.TreatmentPrice,
                    BreederId = a.BreederId,
                    BreederName = a.Breeder.Name,
                    Gender = a.Gender,
                    Age = a.Age,
                    Color = a.Color,
                    Breed = a.Breed,
                    Weight = a.Weight,
                    Notes = a.Notes,
                    IsActive = a.IsActive,
                    CreatedAt = a.CreatedAt,
                    TreatmentsCount = a.Treatments.Count()
                })
                .OrderByDescending(a => a.CreatedAt)
                .ToListAsync();

            return Ok(animals);
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<AnimalDto>> GetAnimal(int id)
        {
            var userBranchId = GetUserBranchId();
            
            var animal = await _context.Animals
                .Include(a => a.AnimalType)
                .Include(a => a.Breeder)
                .ThenInclude(b => b.Branch)
                .Where(a => a.Id == id && (!userBranchId.HasValue || a.Breeder.BranchId == userBranchId.Value))
                .Select(a => new AnimalDto
                {
                    Id = a.Id,
                    Name = a.Name,
                    AnimalTypeId = a.AnimalTypeId,
                    AnimalTypeName = a.AnimalType.Name,
                    AnimalCategory = a.AnimalType.Category,
                    TreatmentPrice = a.AnimalType.TreatmentPrice,
                    BreederId = a.BreederId,
                    BreederName = a.Breeder.Name,
                    Gender = a.Gender,
                    Age = a.Age,
                    Color = a.Color,
                    Breed = a.Breed,
                    Weight = a.Weight,
                    Notes = a.Notes,
                    IsActive = a.IsActive,
                    CreatedAt = a.CreatedAt,
                    TreatmentsCount = a.Treatments.Count()
                })
                .FirstOrDefaultAsync();

            if (animal == null)
            {
                return NotFound();
            }

            return Ok(animal);
        }

        [HttpPost]
        public async Task<ActionResult<AnimalDto>> CreateAnimal([FromBody] CreateAnimalDto createAnimalDto)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var userBranchId = GetUserBranchId();

            // Check if breeder exists and belongs to user's branch
            var breeder = await _context.Breeders
                .Where(b => b.Id == createAnimalDto.BreederId && b.IsActive &&
                           (!userBranchId.HasValue || b.BranchId == userBranchId.Value))
                .FirstOrDefaultAsync();

            if (breeder == null)
            {
                return BadRequest("المربي المحدد غير موجود");
            }

            // Check if animal type exists
            var animalTypeExists = await _context.AnimalTypes.AnyAsync(at => at.Id == createAnimalDto.AnimalTypeId && at.IsActive);
            if (!animalTypeExists)
            {
                return BadRequest("نوع الحيوان المحدد غير موجود");
            }

            var animal = new Animal
            {
                Name = createAnimalDto.Name,
                AnimalTypeId = createAnimalDto.AnimalTypeId,
                BreederId = createAnimalDto.BreederId,
                Gender = createAnimalDto.Gender,
                Age = createAnimalDto.Age,
                Color = createAnimalDto.Color,
                Breed = createAnimalDto.Breed,
                Weight = createAnimalDto.Weight,
                Notes = createAnimalDto.Notes,
                IsActive = true,
                CreatedAt = DateTime.UtcNow
            };

            _context.Animals.Add(animal);
            await _context.SaveChangesAsync();

            var animalDto = await GetAnimal(animal.Id);
            return CreatedAtAction(nameof(GetAnimal), new { id = animal.Id }, animalDto.Value);
        }

        [HttpPost("multiple")]
        public async Task<ActionResult<IEnumerable<AnimalDto>>> CreateMultipleAnimals([FromBody] CreateMultipleAnimalsDto createMultipleDto)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var userBranchId = GetUserBranchId();

            // Check if breeder exists and belongs to user's branch
            var breeder = await _context.Breeders
                .Where(b => b.Id == createMultipleDto.BreederId && b.IsActive &&
                           (!userBranchId.HasValue || b.BranchId == userBranchId.Value))
                .FirstOrDefaultAsync();

            if (breeder == null)
            {
                return BadRequest("المربي المحدد غير موجود");
            }

            // Check if animal type exists
            var animalTypeExists = await _context.AnimalTypes.AnyAsync(at => at.Id == createMultipleDto.AnimalTypeId && at.IsActive);
            if (!animalTypeExists)
            {
                return BadRequest("نوع الحيوان المحدد غير موجود");
            }

            var animals = new List<Animal>();
            for (int i = 0; i < createMultipleDto.Count; i++)
            {
                var animal = new Animal
                {
                    AnimalTypeId = createMultipleDto.AnimalTypeId,
                    BreederId = createMultipleDto.BreederId,
                    Gender = Gender.Male, // Default, can be updated later
                    Notes = createMultipleDto.Notes,
                    IsActive = true,
                    CreatedAt = DateTime.UtcNow
                };
                animals.Add(animal);
            }

            _context.Animals.AddRange(animals);
            await _context.SaveChangesAsync();

            var animalDtos = new List<AnimalDto>();
            foreach (var animal in animals)
            {
                var animalDto = await GetAnimal(animal.Id);
                animalDtos.Add(animalDto.Value);
            }

            return Ok(animalDtos);
        }

        [HttpPut("{id}")]
        public async Task<ActionResult<AnimalDto>> UpdateAnimal(int id, [FromBody] UpdateAnimalDto updateAnimalDto)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var userBranchId = GetUserBranchId();
            
            var animal = await _context.Animals
                .Include(a => a.Breeder)
                .Where(a => a.Id == id && (!userBranchId.HasValue || a.Breeder.BranchId == userBranchId.Value))
                .FirstOrDefaultAsync();

            if (animal == null)
            {
                return NotFound();
            }

            // Check if animal type exists
            var animalTypeExists = await _context.AnimalTypes.AnyAsync(at => at.Id == updateAnimalDto.AnimalTypeId && at.IsActive);
            if (!animalTypeExists)
            {
                return BadRequest("نوع الحيوان المحدد غير موجود");
            }

            animal.Name = updateAnimalDto.Name;
            animal.AnimalTypeId = updateAnimalDto.AnimalTypeId;
            animal.Gender = updateAnimalDto.Gender;
            animal.Age = updateAnimalDto.Age;
            animal.Color = updateAnimalDto.Color;
            animal.Breed = updateAnimalDto.Breed;
            animal.Weight = updateAnimalDto.Weight;
            animal.Notes = updateAnimalDto.Notes;
            animal.IsActive = updateAnimalDto.IsActive;
            animal.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            var animalDto = await GetAnimal(id);
            return Ok(animalDto.Value);
        }

        [HttpDelete("{id}")]
        public async Task<ActionResult> DeleteAnimal(int id)
        {
            var userBranchId = GetUserBranchId();
            
            var animal = await _context.Animals
                .Include(a => a.Breeder)
                .Where(a => a.Id == id && (!userBranchId.HasValue || a.Breeder.BranchId == userBranchId.Value))
                .FirstOrDefaultAsync();

            if (animal == null)
            {
                return NotFound();
            }

            // Check if animal has treatments
            var hasTreatments = await _context.Treatments.AnyAsync(t => t.AnimalId == id);

            if (hasTreatments)
            {
                // Soft delete
                animal.IsActive = false;
                animal.UpdatedAt = DateTime.UtcNow;
            }
            else
            {
                // Hard delete
                _context.Animals.Remove(animal);
            }

            await _context.SaveChangesAsync();
            return NoContent();
        }

        private int? GetUserBranchId()
        {
            var userRole = User.FindFirst(System.Security.Claims.ClaimTypes.Role)?.Value;
            if (userRole == UserRole.SystemAdmin.ToString())
            {
                return null; // System admin can see all branches
            }

            var branchIdClaim = User.FindFirst("BranchId")?.Value;
            if (int.TryParse(branchIdClaim, out int branchId))
            {
                return branchId;
            }

            return null;
        }
    }
}
