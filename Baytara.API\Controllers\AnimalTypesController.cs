using Baytara.API.Data;
using Baytara.Shared.DTOs;
using Baytara.Shared.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace Baytara.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class AnimalTypesController : ControllerBase
    {
        private readonly ApplicationDbContext _context;

        public AnimalTypesController(ApplicationDbContext context)
        {
            _context = context;
        }

        [HttpGet]
        public async Task<ActionResult<IEnumerable<AnimalTypeDto>>> GetAnimalTypes([FromQuery] AnimalCategory? category)
        {
            var query = _context.AnimalTypes.AsQueryable();

            if (category.HasValue)
            {
                query = query.Where(at => at.Category == category.Value);
            }

            var animalTypes = await query
                .Select(at => new AnimalTypeDto
                {
                    Id = at.Id,
                    Name = at.Name,
                    Category = at.Category,
                    TreatmentPrice = at.TreatmentPrice,
                    IsActive = at.IsActive,
                    CreatedAt = at.CreatedAt,
                    AnimalsCount = at.Animals.Count(a => a.IsActive)
                })
                .OrderBy(at => at.Category)
                .ThenBy(at => at.Name)
                .ToListAsync();

            return Ok(animalTypes);
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<AnimalTypeDto>> GetAnimalType(int id)
        {
            var animalType = await _context.AnimalTypes
                .Select(at => new AnimalTypeDto
                {
                    Id = at.Id,
                    Name = at.Name,
                    Category = at.Category,
                    TreatmentPrice = at.TreatmentPrice,
                    IsActive = at.IsActive,
                    CreatedAt = at.CreatedAt,
                    AnimalsCount = at.Animals.Count(a => a.IsActive)
                })
                .FirstOrDefaultAsync(at => at.Id == id);

            if (animalType == null)
            {
                return NotFound();
            }

            return Ok(animalType);
        }

        [HttpPost]
        [Authorize(Roles = "SystemAdmin,BranchManager")]
        public async Task<ActionResult<AnimalTypeDto>> CreateAnimalType([FromBody] CreateAnimalTypeDto createAnimalTypeDto)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            // Check if animal type with same name already exists
            var existingAnimalType = await _context.AnimalTypes
                .AnyAsync(at => at.Name.ToLower() == createAnimalTypeDto.Name.ToLower());

            if (existingAnimalType)
            {
                return BadRequest("نوع الحيوان موجود بالفعل");
            }

            var animalType = new AnimalType
            {
                Name = createAnimalTypeDto.Name,
                Category = createAnimalTypeDto.Category,
                TreatmentPrice = createAnimalTypeDto.TreatmentPrice,
                IsActive = true,
                CreatedAt = DateTime.UtcNow
            };

            _context.AnimalTypes.Add(animalType);
            await _context.SaveChangesAsync();

            var animalTypeDto = await GetAnimalType(animalType.Id);
            return CreatedAtAction(nameof(GetAnimalType), new { id = animalType.Id }, animalTypeDto.Value);
        }

        [HttpPut("{id}")]
        [Authorize(Roles = "SystemAdmin,BranchManager")]
        public async Task<ActionResult<AnimalTypeDto>> UpdateAnimalType(int id, [FromBody] UpdateAnimalTypeDto updateAnimalTypeDto)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var animalType = await _context.AnimalTypes.FindAsync(id);
            if (animalType == null)
            {
                return NotFound();
            }

            // Check if animal type with same name already exists (excluding current)
            var existingAnimalType = await _context.AnimalTypes
                .AnyAsync(at => at.Id != id && at.Name.ToLower() == updateAnimalTypeDto.Name.ToLower());

            if (existingAnimalType)
            {
                return BadRequest("نوع الحيوان موجود بالفعل");
            }

            animalType.Name = updateAnimalTypeDto.Name;
            animalType.Category = updateAnimalTypeDto.Category;
            animalType.TreatmentPrice = updateAnimalTypeDto.TreatmentPrice;
            animalType.IsActive = updateAnimalTypeDto.IsActive;
            animalType.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            var animalTypeDto = await GetAnimalType(id);
            return Ok(animalTypeDto.Value);
        }

        [HttpDelete("{id}")]
        [Authorize(Roles = "SystemAdmin")]
        public async Task<ActionResult> DeleteAnimalType(int id)
        {
            var animalType = await _context.AnimalTypes.FindAsync(id);
            if (animalType == null)
            {
                return NotFound();
            }

            // Check if animal type is used by animals
            var hasAnimals = await _context.Animals.AnyAsync(a => a.AnimalTypeId == id);

            if (hasAnimals)
            {
                // Soft delete
                animalType.IsActive = false;
                animalType.UpdatedAt = DateTime.UtcNow;
            }
            else
            {
                // Hard delete
                _context.AnimalTypes.Remove(animalType);
            }

            await _context.SaveChangesAsync();
            return NoContent();
        }

        [HttpGet("categories")]
        public ActionResult<IEnumerable<object>> GetAnimalCategories()
        {
            var categories = Enum.GetValues<AnimalCategory>()
                .Select(c => new
                {
                    Value = (int)c,
                    Name = c.ToString(),
                    DisplayName = GetCategoryDisplayName(c)
                })
                .ToList();

            return Ok(categories);
        }

        private string GetCategoryDisplayName(AnimalCategory category)
        {
            return category switch
            {
                AnimalCategory.Free => "مجاني",
                AnimalCategory.Economic => "اقتصادي",
                AnimalCategory.NonEconomic => "غير اقتصادي",
                _ => category.ToString()
            };
        }
    }
}
