using System.ComponentModel.DataAnnotations;

namespace Baytara.Shared.Models
{
    public class Breeder
    {
        public int Id { get; set; }
        
        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;
        
        [StringLength(15)]
        public string? PhoneNumber { get; set; }
        
        [StringLength(20)]
        public string? NationalId { get; set; }
        
        [StringLength(200)]
        public string? Location { get; set; }
        
        public int BranchId { get; set; }
        public Branch Branch { get; set; } = null!;
        
        public bool IsActive { get; set; } = true;
        
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime? UpdatedAt { get; set; }
        
        // Navigation properties
        public ICollection<Animal> Animals { get; set; } = new List<Animal>();
        public ICollection<Treatment> Treatments { get; set; } = new List<Treatment>();
        public ICollection<LabTest> LabTests { get; set; } = new List<LabTest>();
    }
}
