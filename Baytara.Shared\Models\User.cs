using System.ComponentModel.DataAnnotations;

namespace Baytara.Shared.Models
{
    public class User
    {
        public int Id { get; set; }
        
        [Required]
        [StringLength(100)]
        public string Username { get; set; } = string.Empty;
        
        [Required]
        [StringLength(255)]
        public string PasswordHash { get; set; } = string.Empty;
        
        [Required]
        [StringLength(100)]
        public string FullName { get; set; } = string.Empty;
        
        [StringLength(15)]
        public string? PhoneNumber { get; set; }
        
        [StringLength(100)]
        public string? Email { get; set; }
        
        public UserRole Role { get; set; }
        
        public int? BranchId { get; set; }
        public Branch? Branch { get; set; }
        
        public bool IsActive { get; set; } = true;
        
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime? UpdatedAt { get; set; }
        
        // Navigation properties
        public ICollection<Treatment> Treatments { get; set; } = new List<Treatment>();
        public ICollection<LabTest> LabTests { get; set; } = new List<LabTest>();
    }
    
    public enum UserRole
    {
        SystemAdmin = 1,
        BranchManager = 2,
        <PERSON> = 3,
        Technician = 4,
        Receptionist = 5
    }
}
