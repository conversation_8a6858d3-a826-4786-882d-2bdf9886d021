using Baytara.Shared.DTOs;
using System.Net.Http.Json;

namespace Baytara.Client.Services
{
    public class AuthService : IAuthService
    {
        private readonly HttpClient _httpClient;
        private readonly ILocalStorageService _localStorage;
        private UserDto? _currentUser;

        public event Action<bool> AuthenticationStateChanged = delegate { };

        public AuthService(HttpClient httpClient, ILocalStorageService localStorage)
        {
            _httpClient = httpClient;
            _localStorage = localStorage;
        }

        public async Task<LoginResponseDto> LoginAsync(LoginDto loginDto)
        {
            try
            {
                var response = await _httpClient.PostAsJsonAsync("api/auth/login", loginDto);
                var result = await response.Content.ReadFromJsonAsync<LoginResponseDto>();

                if (result != null && result.Success)
                {
                    await _localStorage.SetItemAsync("authToken", result.Token);
                    await _localStorage.SetItemAsync("currentUser", result.User);
                    _currentUser = result.User;
                    
                    // Set authorization header
                    _httpClient.DefaultRequestHeaders.Authorization = 
                        new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", result.Token);
                    
                    AuthenticationStateChanged.Invoke(true);
                }

                return result ?? new LoginResponseDto { Success = false, Message = "حدث خطأ غير متوقع" };
            }
            catch (Exception ex)
            {
                return new LoginResponseDto 
                { 
                    Success = false, 
                    Message = "حدث خطأ في الاتصال بالخادم" 
                };
            }
        }

        public async Task LogoutAsync()
        {
            await _localStorage.RemoveItemAsync("authToken");
            await _localStorage.RemoveItemAsync("currentUser");
            _currentUser = null;
            _httpClient.DefaultRequestHeaders.Authorization = null;
            AuthenticationStateChanged.Invoke(false);
        }

        public async Task<UserDto?> GetCurrentUserAsync()
        {
            if (_currentUser != null)
                return _currentUser;

            _currentUser = await _localStorage.GetItemAsync<UserDto>("currentUser");
            
            // If we have a user but no auth header, set it
            if (_currentUser != null)
            {
                var token = await GetTokenAsync();
                if (!string.IsNullOrEmpty(token))
                {
                    _httpClient.DefaultRequestHeaders.Authorization = 
                        new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);
                }
            }
            
            return _currentUser;
        }

        public async Task<bool> IsAuthenticatedAsync()
        {
            var token = await GetTokenAsync();
            var user = await GetCurrentUserAsync();
            return !string.IsNullOrEmpty(token) && user != null;
        }

        public async Task<string?> GetTokenAsync()
        {
            return await _localStorage.GetItemAsync<string>("authToken");
        }
    }
}
