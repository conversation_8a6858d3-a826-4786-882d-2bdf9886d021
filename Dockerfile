# Build stage
FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src

# Copy project files
COPY ["Baytara.API/Baytara.API.csproj", "Baytara.API/"]
COPY ["Baytara.Client/Baytara.Client.csproj", "Baytara.Client/"]
COPY ["Baytara.Shared/Baytara.Shared.csproj", "Baytara.Shared/"]

# Restore dependencies
RUN dotnet restore "Baytara.API/Baytara.API.csproj"

# Copy all source code
COPY . .

# Build the application
WORKDIR "/src/Baytara.API"
RUN dotnet build "Baytara.API.csproj" -c Release -o /app/build

# Publish stage
FROM build AS publish
RUN dotnet publish "Baytara.API.csproj" -c Release -o /app/publish /p:UseAppHost=false

# Runtime stage
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS final
WORKDIR /app

# Install Arabic fonts and locales
RUN apt-get update && apt-get install -y \
    fonts-noto-cjk \
    fonts-noto-color-emoji \
    locales \
    && rm -rf /var/lib/apt/lists/*

# Set Arabic locale
RUN sed -i '/ar_SA.UTF-8/s/^# //g' /etc/locale.gen && \
    locale-gen

ENV LANG ar_SA.UTF-8
ENV LANGUAGE ar_SA:ar
ENV LC_ALL ar_SA.UTF-8

# Copy published app
COPY --from=publish /app/publish .

# Expose port
EXPOSE 80
EXPOSE 443

ENTRYPOINT ["dotnet", "Baytara.API.dll"]
