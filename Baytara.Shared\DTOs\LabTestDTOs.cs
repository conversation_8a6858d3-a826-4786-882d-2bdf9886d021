using Baytara.Shared.Models;
using System.ComponentModel.DataAnnotations;

namespace Baytara.Shared.DTOs
{
    public class LabTestDto
    {
        public int Id { get; set; }
        public int AnimalId { get; set; }
        public string AnimalName { get; set; } = string.Empty;
        public string AnimalTypeName { get; set; } = string.Empty;
        public int BreederId { get; set; }
        public string BreederName { get; set; } = string.Empty;
        public LabTestType TestType { get; set; }
        public string TestName { get; set; } = string.Empty;
        public string? Description { get; set; }
        public int RequestedById { get; set; }
        public string RequestedByName { get; set; } = string.Empty;
        public int? ProcessedById { get; set; }
        public string? ProcessedByName { get; set; }
        public int BranchId { get; set; }
        public string BranchName { get; set; } = string.Empty;
        public LabTestStatus Status { get; set; }
        public string? Results { get; set; }
        public string? Notes { get; set; }
        public decimal Cost { get; set; }
        public DateTime RequestedAt { get; set; }
        public DateTime? ProcessedAt { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }

    public class CreateLabTestDto
    {
        [Required]
        public int AnimalId { get; set; }
        
        [Required]
        public int BreederId { get; set; }
        
        [Required]
        public LabTestType TestType { get; set; }
        
        [Required]
        [StringLength(200)]
        public string TestName { get; set; } = string.Empty;
        
        [StringLength(1000)]
        public string? Description { get; set; }
        
        [Required]
        public int RequestedById { get; set; }
        
        [Required]
        public int BranchId { get; set; }
        
        [StringLength(1000)]
        public string? Notes { get; set; }
        
        [Range(0, double.MaxValue)]
        public decimal Cost { get; set; }
    }

    public class UpdateLabTestDto
    {
        [Required]
        public LabTestType TestType { get; set; }
        
        [Required]
        [StringLength(200)]
        public string TestName { get; set; } = string.Empty;
        
        [StringLength(1000)]
        public string? Description { get; set; }
        
        [Required]
        public LabTestStatus Status { get; set; }
        
        [StringLength(2000)]
        public string? Results { get; set; }
        
        [StringLength(1000)]
        public string? Notes { get; set; }
        
        [Range(0, double.MaxValue)]
        public decimal Cost { get; set; }
        
        public int? ProcessedById { get; set; }
    }

    public class LabTestResultDto
    {
        [Required]
        public int LabTestId { get; set; }
        
        [Required]
        [StringLength(2000)]
        public string Results { get; set; } = string.Empty;
        
        [StringLength(1000)]
        public string? Notes { get; set; }
        
        [Required]
        public int ProcessedById { get; set; }
    }

    public class LabTestSummaryDto
    {
        public int TotalTests { get; set; }
        public int PendingTests { get; set; }
        public int InProgressTests { get; set; }
        public int CompletedTests { get; set; }
        public int CancelledTests { get; set; }
        public decimal TotalRevenue { get; set; }
        public decimal MonthlyRevenue { get; set; }
        public List<LabTestsByTypeDto> TestsByType { get; set; } = new();
        public List<LabTestsByDateDto> TestsByDate { get; set; } = new();
    }

    public class LabTestsByTypeDto
    {
        public LabTestType TestType { get; set; }
        public string TestTypeName { get; set; } = string.Empty;
        public int Count { get; set; }
        public decimal Revenue { get; set; }
    }

    public class LabTestsByDateDto
    {
        public DateTime Date { get; set; }
        public int Count { get; set; }
        public decimal Revenue { get; set; }
    }
}
