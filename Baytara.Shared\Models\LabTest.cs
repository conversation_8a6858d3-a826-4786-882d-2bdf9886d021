using System.ComponentModel.DataAnnotations;

namespace Baytara.Shared.Models
{
    public class LabTest
    {
        public int Id { get; set; }
        
        public int AnimalId { get; set; }
        public Animal Animal { get; set; } = null!;

        public int BreederId { get; set; }
        public Breeder Breeder { get; set; } = null!;

        public int? TreatmentId { get; set; }
        public Treatment? Treatment { get; set; }

        public LabTestType TestType { get; set; }

        public int RequestedById { get; set; }
        public User RequestedByUser { get; set; } = null!;

        public int? ProcessedById { get; set; }
        public User? ProcessedByUser { get; set; }

        public int BranchId { get; set; }
        public Branch Branch { get; set; } = null!;
        
        [Required]
        [StringLength(100)]
        public string TestName { get; set; } = string.Empty;
        
        [StringLength(1000)]
        public string? Description { get; set; }

        [StringLength(2000)]
        public string? Results { get; set; }

        [StringLength(1000)]
        public string? Notes { get; set; }

        public decimal Cost { get; set; }

        public LabTestStatus Status { get; set; } = LabTestStatus.Pending;

        public DateTime RequestedAt { get; set; } = DateTime.UtcNow;
        public DateTime? ProcessedAt { get; set; }
        
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime? UpdatedAt { get; set; }
    }
    
    public enum LabTestStatus
    {
        Pending = 1,
        InProgress = 2,
        Completed = 3,
        Cancelled = 4
    }

    public enum LabTestType
    {
        BloodTest = 1,
        UrineTest = 2,
        StoolTest = 3,
        SkinTest = 4,
        Biopsy = 5,
        Microbiology = 6,
        Parasitology = 7,
        Serology = 8,
        Histopathology = 9,
        Other = 10
    }
}
