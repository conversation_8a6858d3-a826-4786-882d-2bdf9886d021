<svg width="100" height="100" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background Circle -->
  <circle cx="50" cy="50" r="45" fill="url(#logoGradient)" stroke="#fff" stroke-width="2"/>
  
  <!-- Veterinary Cross -->
  <rect x="45" y="25" width="10" height="50" fill="white" rx="2"/>
  <rect x="25" y="45" width="50" height="10" fill="white" rx="2"/>
  
  <!-- Animal Paw Print -->
  <g transform="translate(35, 65)">
    <!-- Main pad -->
    <ellipse cx="15" cy="8" rx="8" ry="6" fill="white"/>
    <!-- Toes -->
    <circle cx="8" cy="2" r="3" fill="white"/>
    <circle cx="15" cy="0" r="3" fill="white"/>
    <circle cx="22" cy="2" r="3" fill="white"/>
    <circle cx="25" cy="8" r="2.5" fill="white"/>
  </g>
  
  <!-- Stethoscope -->
  <g transform="translate(15, 15)">
    <path d="M10 10 Q15 5 20 10 Q25 15 20 20 Q15 25 10 20 Q5 15 10 10" 
          fill="none" stroke="white" stroke-width="2"/>
    <circle cx="20" cy="20" r="3" fill="white"/>
    <path d="M20 23 L25 28" stroke="white" stroke-width="2"/>
    <circle cx="25" cy="28" r="2" fill="white"/>
  </g>
</svg>
