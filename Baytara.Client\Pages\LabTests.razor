@page "/labtests"
@using Baytara.Client.Services
@using Baytara.Shared.DTOs
@using Baytara.Shared.Models
@inject IApiService ApiService
@inject IJSRuntime JSRuntime

<PageTitle>المختبر - بيطره</PageTitle>

<div class="labtests-page">
    <div class="page-header">
        <div class="header-content">
            <h1 class="page-title">
                <i class="fas fa-microscope"></i>
                إدارة المختبر
            </h1>
            <p class="page-subtitle">طلب وإدارة الفحوصات المخبرية</p>
        </div>
        <div class="header-actions">
            <div class="filter-buttons">
                <button class="btn @(currentFilter == "all" ? "btn-primary" : "btn-outline")" @onclick="@(() => FilterLabTests("all"))">
                    <i class="fas fa-list"></i>
                    جميع الفحوصات
                </button>
                <button class="btn @(currentFilter == "pending" ? "btn-warning" : "btn-outline")" @onclick="@(() => FilterLabTests("pending"))">
                    <i class="fas fa-clock"></i>
                    معلقة
                </button>
                <button class="btn @(currentFilter == "completed" ? "btn-success" : "btn-outline")" @onclick="@(() => FilterLabTests("completed"))">
                    <i class="fas fa-check"></i>
                    مكتملة
                </button>
            </div>
            <button class="btn btn-primary" @onclick="ShowNewLabTestModal">
                <i class="fas fa-plus"></i>
                طلب فحص جديد
            </button>
        </div>
    </div>

    @if (isLoading)
    {
        <div class="loading-container">
            <div class="spinner">
                <i class="fas fa-spinner fa-spin"></i>
            </div>
            <p>جاري تحميل البيانات...</p>
        </div>
    }
    else if (filteredLabTests.Any())
    {
        <div class="labtests-grid">
            @foreach (var labTest in filteredLabTests)
            {
                <div class="labtest-card @GetLabTestCardClass(labTest.Status)">
                    <div class="card-header">
                        <div class="labtest-info">
                            <h3 class="labtest-title">
                                @labTest.TestName
                            </h3>
                            <p class="animal-info">@labTest.AnimalTypeName @(!string.IsNullOrEmpty(labTest.AnimalName) ? $"- {labTest.AnimalName}" : "")</p>
                            <p class="breeder-name">المربي: @labTest.BreederName</p>
                            <p class="requested-by">طلب بواسطة: @labTest.RequestedByName</p>
                        </div>
                        <div class="labtest-status">
                            <span class="status-badge @GetStatusClass(labTest.Status)">
                                <i class="@GetStatusIcon(labTest.Status)"></i>
                                @GetStatusText(labTest.Status)
                            </span>
                        </div>
                    </div>
                    
                    <div class="card-body">
                        <div class="labtest-details">
                            <div class="detail-item">
                                <span class="label">نوع الفحص:</span>
                                <span class="value">@GetTestTypeText(labTest.TestType)</span>
                            </div>
                            
                            @if (!string.IsNullOrEmpty(labTest.Description))
                            {
                                <div class="detail-item">
                                    <span class="label">الوصف:</span>
                                    <span class="value">@labTest.Description</span>
                                </div>
                            }
                            
                            @if (!string.IsNullOrEmpty(labTest.Results))
                            {
                                <div class="detail-item">
                                    <span class="label">النتائج:</span>
                                    <span class="value results">@labTest.Results</span>
                                </div>
                            }
                            
                            @if (!string.IsNullOrEmpty(labTest.Notes))
                            {
                                <div class="detail-item">
                                    <span class="label">ملاحظات:</span>
                                    <span class="value">@labTest.Notes</span>
                                </div>
                            }
                        </div>
                        
                        <div class="labtest-footer">
                            <div class="cost-info">
                                <span class="cost">
                                    <i class="fas fa-money-bill-wave"></i>
                                    التكلفة: @labTest.Cost.ToString("N2") ر.س
                                </span>
                            </div>
                            <div class="date-info">
                                <small class="text-muted">
                                    تاريخ الطلب: @labTest.RequestedAt.ToString("dd/MM/yyyy HH:mm")
                                </small>
                                @if (labTest.ProcessedAt.HasValue)
                                {
                                    <small class="text-muted">
                                        تاريخ الإنجاز: @labTest.ProcessedAt.Value.ToString("dd/MM/yyyy HH:mm")
                                    </small>
                                }
                            </div>
                        </div>
                        
                        <div class="card-actions">
                            @if (labTest.Status == LabTestStatus.Pending)
                            {
                                <button class="btn btn-info btn-sm" @onclick="() => StartProcessing(labTest.Id)">
                                    <i class="fas fa-play"></i>
                                    بدء المعالجة
                                </button>
                            }
                            @if (labTest.Status == LabTestStatus.InProgress)
                            {
                                <button class="btn btn-success btn-sm" @onclick="() => ShowResultsModal(labTest)">
                                    <i class="fas fa-check"></i>
                                    إدخال النتائج
                                </button>
                            }
                            <button class="btn btn-info btn-sm" @onclick="() => ViewLabTestDetails(labTest.Id)">
                                <i class="fas fa-eye"></i>
                                عرض التفاصيل
                            </button>
                            @if (labTest.Status != LabTestStatus.Completed)
                            {
                                <button class="btn btn-warning btn-sm" @onclick="() => ShowEditLabTestModal(labTest)">
                                    <i class="fas fa-edit"></i>
                                    تعديل
                                </button>
                            }
                        </div>
                    </div>
                </div>
            }
        </div>
    }
    else
    {
        <div class="empty-state">
            <i class="fas fa-microscope"></i>
            <h3>@GetEmptyStateTitle()</h3>
            <p>@GetEmptyStateMessage()</p>
            @if (currentFilter == "all")
            {
                <button class="btn btn-primary" @onclick="ShowNewLabTestModal">
                    <i class="fas fa-plus"></i>
                    طلب فحص جديد
                </button>
            }
        </div>
    }
</div>

<!-- New/Edit LabTest Modal -->
@if (showLabTestModal)
{
    <div class="modal-overlay" @onclick="CloseLabTestModal">
        <div class="modal-content" @onclick:stopPropagation="true">
            <div class="modal-header">
                <h3>@(editingLabTest == null ? "طلب فحص جديد" : "تعديل الفحص")</h3>
                <button class="btn btn-close" @onclick="CloseLabTestModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <div class="modal-body">
                <EditForm Model="@labTestForm" OnValidSubmit="@SaveLabTest">
                    <DataAnnotationsValidator />
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="breeder">المربي *</label>
                            <InputSelect id="breeder" @bind-Value="selectedBreederId" class="form-control" @onchange="OnBreederChanged">
                                <option value="0">اختر المربي</option>
                                @foreach (var breeder in breeders)
                                {
                                    <option value="@breeder.Id">@breeder.Name</option>
                                }
                            </InputSelect>
                        </div>
                        
                        <div class="form-group">
                            <label for="animal">الحيوان *</label>
                            <InputSelect id="animal" @bind-Value="labTestForm.AnimalId" class="form-control">
                                <option value="0">اختر الحيوان</option>
                                @foreach (var animal in breederAnimals)
                                {
                                    <option value="@animal.Id">@animal.AnimalTypeName @(!string.IsNullOrEmpty(animal.Name) ? $"- {animal.Name}" : "")</option>
                                }
                            </InputSelect>
                            <ValidationMessage For="@(() => labTestForm.AnimalId)" />
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="testType">نوع الفحص *</label>
                            <InputSelect id="testType" @bind-Value="labTestForm.TestType" class="form-control">
                                <option value="@LabTestType.BloodTest">فحص دم</option>
                                <option value="@LabTestType.UrineTest">فحص بول</option>
                                <option value="@LabTestType.StoolTest">فحص براز</option>
                                <option value="@LabTestType.SkinTest">فحص جلد</option>
                                <option value="@LabTestType.Biopsy">خزعة</option>
                                <option value="@LabTestType.Microbiology">فحص ميكروبيولوجي</option>
                                <option value="@LabTestType.Parasitology">فحص طفيليات</option>
                                <option value="@LabTestType.Serology">فحص مصلي</option>
                                <option value="@LabTestType.Histopathology">فحص نسيجي</option>
                                <option value="@LabTestType.Other">أخرى</option>
                            </InputSelect>
                            <ValidationMessage For="@(() => labTestForm.TestType)" />
                        </div>
                        
                        <div class="form-group">
                            <label for="testName">اسم الفحص *</label>
                            <InputText id="testName" @bind-Value="labTestForm.TestName" class="form-control" placeholder="اكتب اسم الفحص" />
                            <ValidationMessage For="@(() => labTestForm.TestName)" />
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="description">وصف الفحص</label>
                        <InputTextArea id="description" @bind-Value="labTestForm.Description" class="form-control" rows="3" placeholder="اكتب وصف مفصل للفحص المطلوب" />
                        <ValidationMessage For="@(() => labTestForm.Description)" />
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="cost">التكلفة (ر.س)</label>
                            <InputNumber id="cost" @bind-Value="labTestForm.Cost" class="form-control" placeholder="0.00" />
                            <ValidationMessage For="@(() => labTestForm.Cost)" />
                        </div>
                        
                        <div class="form-group">
                            <label for="notes">ملاحظات</label>
                            <InputTextArea id="notes" @bind-Value="labTestForm.Notes" class="form-control" rows="2" placeholder="أي ملاحظات إضافية" />
                            <ValidationMessage For="@(() => labTestForm.Notes)" />
                        </div>
                    </div>
                    
                    <div class="modal-actions">
                        <button type="button" class="btn btn-secondary" @onclick="CloseLabTestModal">إلغاء</button>
                        <button type="submit" class="btn btn-primary" disabled="@isSaving">
                            @if (isSaving)
                            {
                                <i class="fas fa-spinner fa-spin"></i>
                                <span>جاري الحفظ...</span>
                            }
                            else
                            {
                                <i class="fas fa-save"></i>
                                <span>حفظ الطلب</span>
                            }
                        </button>
                    </div>
                </EditForm>
            </div>
        </div>
    </div>
}

<!-- Results Modal -->
@if (showResultsModal)
{
    <div class="modal-overlay" @onclick="CloseResultsModal">
        <div class="modal-content" @onclick:stopPropagation="true">
            <div class="modal-header">
                <h3>إدخال نتائج الفحص</h3>
                <button class="btn btn-close" @onclick="CloseResultsModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <div class="modal-body">
                <div class="test-info">
                    <h4>@selectedLabTest?.TestName</h4>
                    <p>@selectedLabTest?.AnimalTypeName - @selectedLabTest?.BreederName</p>
                </div>
                
                <div class="form-group">
                    <label for="results">النتائج *</label>
                    <textarea id="results" @bind="testResults" class="form-control" rows="6" placeholder="اكتب نتائج الفحص بالتفصيل"></textarea>
                </div>
                
                <div class="form-group">
                    <label for="resultNotes">ملاحظات إضافية</label>
                    <textarea id="resultNotes" @bind="resultNotes" class="form-control" rows="3" placeholder="أي ملاحظات إضافية على النتائج"></textarea>
                </div>
                
                <div class="modal-actions">
                    <button type="button" class="btn btn-secondary" @onclick="CloseResultsModal">إلغاء</button>
                    <button type="button" class="btn btn-success" @onclick="SaveResults" disabled="@isSaving">
                        @if (isSaving)
                        {
                            <i class="fas fa-spinner fa-spin"></i>
                            <span>جاري الحفظ...</span>
                        }
                        else
                        {
                            <i class="fas fa-check"></i>
                            <span>حفظ النتائج</span>
                        }
                    </button>
                </div>
            </div>
        </div>
    </div>
}

<style>
    .labtests-page {
        max-width: 1400px;
        margin: 0 auto;
    }

    .page-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 30px;
        padding-bottom: 20px;
        border-bottom: 2px solid #eee;
        flex-wrap: wrap;
        gap: 20px;
    }

    .header-content h1 {
        font-size: 2.2rem;
        color: #333;
        margin-bottom: 5px;
        display: flex;
        align-items: center;
        gap: 15px;
    }

    .page-subtitle {
        font-size: 1.1rem;
        color: #666;
        margin-bottom: 0;
    }

    .header-actions {
        display: flex;
        gap: 20px;
        align-items: center;
        flex-wrap: wrap;
    }

    .filter-buttons {
        display: flex;
        gap: 10px;
        flex-wrap: wrap;
    }

    .btn {
        padding: 10px 20px;
        border: none;
        border-radius: 8px;
        cursor: pointer;
        font-size: 0.9rem;
        font-weight: 500;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 8px;
        text-decoration: none;
    }

    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
    }

    .btn-outline {
        background: white;
        border: 2px solid #e9ecef;
        color: #666;
    }

    .btn-outline:hover {
        border-color: #667eea;
        color: #667eea;
    }

    .btn-success { background: #28a745; color: white; }
    .btn-warning { background: #ffc107; color: white; }
    .btn-info { background: #17a2b8; color: white; }
    .btn-danger { background: #dc3545; color: white; }
    .btn-secondary { background: #6c757d; color: white; }

    .btn-sm {
        padding: 6px 12px;
        font-size: 0.8rem;
    }

    .loading-container {
        text-align: center;
        padding: 60px 20px;
        color: #666;
    }

    .loading-container .spinner {
        font-size: 3rem;
        color: #667eea;
        margin-bottom: 20px;
    }

    /* LabTests Grid */
    .labtests-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(450px, 1fr));
        gap: 25px;
    }

    .labtest-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        border-left: 5px solid;
    }

    .labtest-card.pending { border-left-color: #ffc107; }
    .labtest-card.in-progress { border-left-color: #17a2b8; }
    .labtest-card.completed { border-left-color: #28a745; }
    .labtest-card.cancelled { border-left-color: #dc3545; }

    .labtest-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }

    .card-header {
        padding: 20px;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-bottom: 1px solid #dee2e6;
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
    }

    .labtest-title {
        font-size: 1.3rem;
        font-weight: 600;
        color: #333;
        margin-bottom: 5px;
    }

    .animal-info {
        font-size: 0.9rem;
        color: #667eea;
        font-weight: 500;
        margin-bottom: 3px;
    }

    .breeder-name, .requested-by {
        font-size: 0.85rem;
        color: #666;
        margin-bottom: 3px;
    }

    .labtest-status {
        display: flex;
        align-items: center;
    }

    .status-badge {
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 500;
        display: flex;
        align-items: center;
        gap: 5px;
    }

    .status-badge.pending {
        background: rgba(255, 193, 7, 0.2);
        color: #856404;
        border: 1px solid rgba(255, 193, 7, 0.3);
    }

    .status-badge.in-progress {
        background: rgba(23, 162, 184, 0.2);
        color: #0c5460;
        border: 1px solid rgba(23, 162, 184, 0.3);
    }

    .status-badge.completed {
        background: rgba(40, 167, 69, 0.2);
        color: #155724;
        border: 1px solid rgba(40, 167, 69, 0.3);
    }

    .status-badge.cancelled {
        background: rgba(220, 53, 69, 0.2);
        color: #721c24;
        border: 1px solid rgba(220, 53, 69, 0.3);
    }

    @@media (max-width: 768px) {
        .labtests-grid {
            grid-template-columns: 1fr;
        }
    }
</style>

@code {
    private List<LabTestDto> labTests = new();
    private List<LabTestDto> filteredLabTests = new();
    private List<BreederDto> breeders = new();
    private List<AnimalDto> breederAnimals = new();
    private bool isLoading = true;
    private bool isSaving = false;
    private string currentFilter = "all";

    // Modal states
    private bool showLabTestModal = false;
    private bool showResultsModal = false;
    private LabTestDto? editingLabTest = null;
    private LabTestDto? selectedLabTest = null;

    // Form data
    private CreateLabTestDto labTestForm = new();
    private int selectedBreederId = 0;
    private string testResults = "";
    private string resultNotes = "";

    protected override async Task OnInitializedAsync()
    {
        await LoadData();
    }

    private async Task LoadData()
    {
        try
        {
            isLoading = true;
            StateHasChanged();

            // Load all required data
            var labTestsTask = LoadLabTests();
            var breedersTask = ApiService.GetBreedersAsync();

            await Task.WhenAll(labTestsTask, breedersTask);

            breeders = (await breedersTask).ToList();

            FilterLabTests(currentFilter);
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", "حدث خطأ في تحميل البيانات");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private async Task LoadLabTests()
    {
        labTests = (await ApiService.GetLabTestsAsync()).ToList();
    }

    private void FilterLabTests(string filter)
    {
        currentFilter = filter;

        filteredLabTests = filter switch
        {
            "pending" => labTests.Where(lt => lt.Status == LabTestStatus.Pending || lt.Status == LabTestStatus.InProgress).ToList(),
            "completed" => labTests.Where(lt => lt.Status == LabTestStatus.Completed).ToList(),
            _ => labTests.ToList()
        };

        StateHasChanged();
    }

    private string GetLabTestCardClass(LabTestStatus status)
    {
        return status switch
        {
            LabTestStatus.Pending => "pending",
            LabTestStatus.InProgress => "in-progress",
            LabTestStatus.Completed => "completed",
            LabTestStatus.Cancelled => "cancelled",
            _ => ""
        };
    }

    private string GetStatusClass(LabTestStatus status)
    {
        return status switch
        {
            LabTestStatus.Pending => "pending",
            LabTestStatus.InProgress => "in-progress",
            LabTestStatus.Completed => "completed",
            LabTestStatus.Cancelled => "cancelled",
            _ => ""
        };
    }

    private string GetStatusIcon(LabTestStatus status)
    {
        return status switch
        {
            LabTestStatus.Pending => "fas fa-clock",
            LabTestStatus.InProgress => "fas fa-play",
            LabTestStatus.Completed => "fas fa-check",
            LabTestStatus.Cancelled => "fas fa-times",
            _ => "fas fa-question"
        };
    }

    private string GetStatusText(LabTestStatus status)
    {
        return status switch
        {
            LabTestStatus.Pending => "معلق",
            LabTestStatus.InProgress => "قيد المعالجة",
            LabTestStatus.Completed => "مكتمل",
            LabTestStatus.Cancelled => "ملغي",
            _ => "غير محدد"
        };
    }

    private string GetTestTypeText(LabTestType testType)
    {
        return testType switch
        {
            LabTestType.BloodTest => "فحص دم",
            LabTestType.UrineTest => "فحص بول",
            LabTestType.StoolTest => "فحص براز",
            LabTestType.SkinTest => "فحص جلد",
            LabTestType.Biopsy => "خزعة",
            LabTestType.Microbiology => "فحص ميكروبيولوجي",
            LabTestType.Parasitology => "فحص طفيليات",
            LabTestType.Serology => "فحص مصلي",
            LabTestType.Histopathology => "فحص نسيجي",
            LabTestType.Other => "أخرى",
            _ => "غير محدد"
        };
    }

    private string GetEmptyStateTitle()
    {
        return currentFilter switch
        {
            "pending" => "لا توجد فحوصات معلقة",
            "completed" => "لا توجد فحوصات مكتملة",
            _ => "لا توجد فحوصات مسجلة"
        };
    }

    private string GetEmptyStateMessage()
    {
        return currentFilter switch
        {
            "pending" => "جميع الفحوصات تم إنجازها",
            "completed" => "لم يتم إكمال أي فحوصات بعد",
            _ => "ابدأ بطلب فحص جديد"
        };
    }

    private void ShowNewLabTestModal()
    {
        editingLabTest = null;
        labTestForm = new CreateLabTestDto();
        selectedBreederId = 0;
        breederAnimals.Clear();
        showLabTestModal = true;
    }

    private void ShowEditLabTestModal(LabTestDto labTest)
    {
        editingLabTest = labTest;
        labTestForm = new CreateLabTestDto
        {
            AnimalId = labTest.AnimalId,
            BreederId = labTest.BreederId,
            TestType = labTest.TestType,
            TestName = labTest.TestName,
            Description = labTest.Description,
            RequestedById = labTest.RequestedById,
            BranchId = labTest.BranchId,
            Notes = labTest.Notes,
            Cost = labTest.Cost
        };
        showLabTestModal = true;
    }

    private void CloseLabTestModal()
    {
        showLabTestModal = false;
        editingLabTest = null;
        labTestForm = new CreateLabTestDto();
        selectedBreederId = 0;
        breederAnimals.Clear();
    }

    private void ShowResultsModal(LabTestDto labTest)
    {
        selectedLabTest = labTest;
        testResults = labTest.Results ?? "";
        resultNotes = labTest.Notes ?? "";
        showResultsModal = true;
    }

    private void CloseResultsModal()
    {
        showResultsModal = false;
        selectedLabTest = null;
        testResults = "";
        resultNotes = "";
    }

    private async Task OnBreederChanged(ChangeEventArgs e)
    {
        if (int.TryParse(e.Value?.ToString(), out int breederId) && breederId > 0)
        {
            selectedBreederId = breederId;
            labTestForm.BreederId = breederId;

            // Load animals for selected breeder
            breederAnimals = (await ApiService.GetAnimalsAsync(breederId)).ToList();
            StateHasChanged();
        }
        else
        {
            selectedBreederId = 0;
            breederAnimals.Clear();
            labTestForm.AnimalId = 0;
        }
    }

    private async Task SaveLabTest()
    {
        try
        {
            isSaving = true;
            StateHasChanged();

            // Set required fields
            labTestForm.RequestedById = 1; // This should come from current user
            labTestForm.BranchId = 1; // This should come from current user

            if (editingLabTest == null)
            {
                await ApiService.CreateLabTestAsync(labTestForm);
                await JSRuntime.InvokeVoidAsync("alert", "تم إضافة طلب الفحص بنجاح");
                CloseLabTestModal();
                await LoadData();
            }
            else
            {
                var updateDto = new UpdateLabTestDto
                {
                    TestType = labTestForm.TestType,
                    TestName = labTestForm.TestName,
                    Description = labTestForm.Description,
                    Status = editingLabTest.Status,
                    Results = editingLabTest.Results,
                    Notes = labTestForm.Notes,
                    Cost = labTestForm.Cost,
                    ProcessedById = editingLabTest.ProcessedById
                };

                await ApiService.UpdateLabTestAsync(editingLabTest.Id, updateDto);
                await JSRuntime.InvokeVoidAsync("alert", "تم تحديث طلب الفحص بنجاح");
                CloseLabTestModal();
                await LoadData();
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", "حدث خطأ غير متوقع");
        }
        finally
        {
            isSaving = false;
            StateHasChanged();
        }
    }

    private async Task SaveResults()
    {
        if (selectedLabTest == null || string.IsNullOrWhiteSpace(testResults))
        {
            await JSRuntime.InvokeVoidAsync("alert", "يرجى إدخال النتائج");
            return;
        }

        try
        {
            isSaving = true;
            StateHasChanged();

            var updateDto = new UpdateLabTestDto
            {
                TestType = selectedLabTest.TestType,
                TestName = selectedLabTest.TestName,
                Description = selectedLabTest.Description,
                Status = LabTestStatus.Completed,
                Results = testResults,
                Notes = resultNotes,
                Cost = selectedLabTest.Cost,
                ProcessedById = 1 // This should come from current user
            };

            await ApiService.UpdateLabTestAsync(selectedLabTest.Id, updateDto);
            await JSRuntime.InvokeVoidAsync("alert", "تم حفظ النتائج بنجاح");
            CloseResultsModal();
            await LoadData();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", "حدث خطأ غير متوقع");
        }
        finally
        {
            isSaving = false;
            StateHasChanged();
        }
    }

    private async Task StartProcessing(int labTestId)
    {
        try
        {
            var labTest = labTests.FirstOrDefault(lt => lt.Id == labTestId);
            if (labTest != null)
            {
                var updateDto = new UpdateLabTestDto
                {
                    TestType = labTest.TestType,
                    TestName = labTest.TestName,
                    Description = labTest.Description,
                    Status = LabTestStatus.InProgress,
                    Results = labTest.Results,
                    Notes = labTest.Notes,
                    Cost = labTest.Cost,
                    ProcessedById = 1 // This should come from current user
                };

                await ApiService.UpdateLabTestAsync(labTestId, updateDto);
                await JSRuntime.InvokeVoidAsync("alert", "تم بدء معالجة الفحص");
                await LoadData();
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", "حدث خطأ غير متوقع");
        }
    }

    private async Task ViewLabTestDetails(int labTestId)
    {
        // Navigate to lab test details page or show detailed modal
        await JSRuntime.InvokeVoidAsync("alert", $"عرض تفاصيل الفحص رقم {labTestId}");
    }
}
