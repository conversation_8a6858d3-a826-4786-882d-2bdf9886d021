using System.ComponentModel.DataAnnotations;

namespace Baytara.Shared.Models
{
    public class AnimalType
    {
        public int Id { get; set; }
        
        [Required]
        [StringLength(50)]
        public string Name { get; set; } = string.Empty;
        
        public AnimalCategory Category { get; set; }
        
        public decimal TreatmentPrice { get; set; }
        
        public bool IsActive { get; set; } = true;
        
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime? UpdatedAt { get; set; }
        
        // Navigation properties
        public ICollection<Animal> Animals { get; set; } = new List<Animal>();
    }
    
    public enum AnimalCategory
    {
        Free = 1,        // مجاني - أرانب ودواجن
        Economic = 2,    // اقتصادي - جمال وخيول وأبقار وماعز وضأن - 100 بيسة لكل رأس
        NonEconomic = 3  // غير اقتصادي - قطط وكلاب وطيور زينة وأخرى - السعر يحدد من قبل الموظف
    }
}
