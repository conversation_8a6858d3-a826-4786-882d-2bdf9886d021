using Baytara.Shared.Models;
using System.ComponentModel.DataAnnotations;

namespace Baytara.Shared.DTOs
{
    public class AnimalTypeDto
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public AnimalCategory Category { get; set; }
        public decimal TreatmentPrice { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedAt { get; set; }
        public int AnimalsCount { get; set; }
    }
    
    public class CreateAnimalTypeDto
    {
        [Required(ErrorMessage = "اسم نوع الحيوان مطلوب")]
        [StringLength(50, ErrorMessage = "اسم نوع الحيوان يجب أن يكون أقل من 50 حرف")]
        public string Name { get; set; } = string.Empty;
        
        [Required(ErrorMessage = "فئة الحيوان مطلوبة")]
        public AnimalCategory Category { get; set; }
        
        [Required(ErrorMessage = "سعر العلاج مطلوب")]
        [Range(0, double.MaxValue, ErrorMessage = "سعر العلاج يجب أن يكون أكبر من أو يساوي 0")]
        public decimal TreatmentPrice { get; set; }
    }
    
    public class UpdateAnimalTypeDto
    {
        [Required(ErrorMessage = "اسم نوع الحيوان مطلوب")]
        [StringLength(50, ErrorMessage = "اسم نوع الحيوان يجب أن يكون أقل من 50 حرف")]
        public string Name { get; set; } = string.Empty;
        
        [Required(ErrorMessage = "فئة الحيوان مطلوبة")]
        public AnimalCategory Category { get; set; }
        
        [Required(ErrorMessage = "سعر العلاج مطلوب")]
        [Range(0, double.MaxValue, ErrorMessage = "سعر العلاج يجب أن يكون أكبر من أو يساوي 0")]
        public decimal TreatmentPrice { get; set; }
        
        public bool IsActive { get; set; }
    }
}
