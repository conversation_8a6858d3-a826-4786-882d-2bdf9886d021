using System.ComponentModel.DataAnnotations;

namespace Baytara.Shared.Models
{
    public class Treatment
    {
        public int Id { get; set; }
        
        public int AnimalId { get; set; }
        public Animal Animal { get; set; } = null!;
        
        public int BreederId { get; set; }
        public Breeder Breeder { get; set; } = null!;
        
        public int DoctorId { get; set; }
        public User Doctor { get; set; } = null!;
        
        public int BranchId { get; set; }
        public Branch Branch { get; set; } = null!;
        
        [StringLength(1000)]
        public string? Symptoms { get; set; }
        
        [StringLength(1000)]
        public string? Diagnosis { get; set; }
        
        [StringLength(1000)]
        public string? TreatmentPlan { get; set; }
        
        [StringLength(500)]
        public string? Notes { get; set; }
        
        public decimal TotalCost { get; set; }
        
        public TreatmentStatus Status { get; set; } = TreatmentStatus.Pending;
        
        public DateTime TreatmentDate { get; set; } = DateTime.UtcNow;
        public DateTime? CompletedAt { get; set; }
        
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime? UpdatedAt { get; set; }
        
        // Navigation properties
        public ICollection<TreatmentDisease> TreatmentDiseases { get; set; } = new List<TreatmentDisease>();
        public ICollection<TreatmentMedicine> TreatmentMedicines { get; set; } = new List<TreatmentMedicine>();
        public ICollection<LabTest> LabTests { get; set; } = new List<LabTest>();
    }
    
    public enum TreatmentStatus
    {
        Pending = 1,
        InProgress = 2,
        Completed = 3,
        Cancelled = 4
    }
}
