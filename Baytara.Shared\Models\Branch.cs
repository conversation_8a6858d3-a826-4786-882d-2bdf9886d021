using System.ComponentModel.DataAnnotations;

namespace Baytara.Shared.Models
{
    public class Branch
    {
        public int Id { get; set; }
        
        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;
        
        [StringLength(200)]
        public string? Address { get; set; }
        
        [StringLength(15)]
        public string? PhoneNumber { get; set; }
        
        [StringLength(100)]
        public string? Email { get; set; }
        
        public bool IsActive { get; set; } = true;
        
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime? UpdatedAt { get; set; }
        
        // Navigation properties
        public ICollection<User> Users { get; set; } = new List<User>();
        public ICollection<Breeder> Breeders { get; set; } = new List<Breeder>();
        public ICollection<Treatment> Treatments { get; set; } = new List<Treatment>();
        public ICollection<Medicine> Medicines { get; set; } = new List<Medicine>();
        public ICollection<LabTest> LabTests { get; set; } = new List<LabTest>();
    }
}
