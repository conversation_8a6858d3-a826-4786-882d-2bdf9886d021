using System.ComponentModel.DataAnnotations;

namespace Baytara.Shared.DTOs
{
    public class BreederDto
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string? PhoneNumber { get; set; }
        public string? NationalId { get; set; }
        public string? Location { get; set; }
        public int BranchId { get; set; }
        public string BranchName { get; set; } = string.Empty;
        public bool IsActive { get; set; }
        public DateTime CreatedAt { get; set; }
        public int AnimalsCount { get; set; }
        public int TreatmentsCount { get; set; }
    }
    
    public class CreateBreederDto
    {
        [Required(ErrorMessage = "اسم المربي مطلوب")]
        [StringLength(100, ErrorMessage = "اسم المربي يجب أن يكون أقل من 100 حرف")]
        public string Name { get; set; } = string.Empty;
        
        [StringLength(15, ErrorMessage = "رقم الهاتف يجب أن يكون أقل من 15 رقم")]
        public string? PhoneNumber { get; set; }
        
        [StringLength(20, ErrorMessage = "الرقم المدني يجب أن يكون أقل من 20 رقم")]
        public string? NationalId { get; set; }
        
        [StringLength(200, ErrorMessage = "المكان يجب أن يكون أقل من 200 حرف")]
        public string? Location { get; set; }
        
        [Required(ErrorMessage = "الفرع مطلوب")]
        public int BranchId { get; set; }
    }
    
    public class UpdateBreederDto
    {
        [Required(ErrorMessage = "اسم المربي مطلوب")]
        [StringLength(100, ErrorMessage = "اسم المربي يجب أن يكون أقل من 100 حرف")]
        public string Name { get; set; } = string.Empty;
        
        [StringLength(15, ErrorMessage = "رقم الهاتف يجب أن يكون أقل من 15 رقم")]
        public string? PhoneNumber { get; set; }
        
        [StringLength(20, ErrorMessage = "الرقم المدني يجب أن يكون أقل من 20 رقم")]
        public string? NationalId { get; set; }
        
        [StringLength(200, ErrorMessage = "المكان يجب أن يكون أقل من 200 حرف")]
        public string? Location { get; set; }
        
        [Required(ErrorMessage = "الفرع مطلوب")]
        public int BranchId { get; set; }
        
        public bool IsActive { get; set; }
    }
}
