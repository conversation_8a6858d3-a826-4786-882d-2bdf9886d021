<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام بيطره - عرض توضيحي</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            direction: rtl;
            text-align: right;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 40px 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        .header h1 {
            font-size: 3rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .header p {
            font-size: 1.3rem;
            opacity: 0.9;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 25px;
            margin-bottom: 50px;
        }

        .stat-card {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            display: flex;
            align-items: center;
            gap: 20px;
            transition: all 0.3s ease;
            border-right: 5px solid;
        }

        .stat-card:nth-child(1) { border-right-color: #667eea; }
        .stat-card:nth-child(2) { border-right-color: #28a745; }
        .stat-card:nth-child(3) { border-right-color: #ffc107; }
        .stat-card:nth-child(4) { border-right-color: #dc3545; }

        .stat-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        }

        .stat-icon {
            font-size: 4rem;
            width: 80px;
            height: 80px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .stat-content h3 {
            font-size: 2.5rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 8px;
        }

        .stat-content p {
            color: #666;
            font-size: 1.1rem;
            margin-bottom: 0;
        }

        .features-section {
            margin-top: 50px;
        }

        .features-section h2 {
            text-align: center;
            margin-bottom: 40px;
            color: #333;
            font-size: 2.5rem;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
        }

        .feature-card {
            background: white;
            padding: 40px 30px;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            text-align: center;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .feature-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        }

        .feature-icon {
            font-size: 5rem;
            margin-bottom: 25px;
            display: block;
        }

        .feature-card h3 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.5rem;
        }

        .feature-card p {
            color: #666;
            line-height: 1.8;
            font-size: 1rem;
        }

        .demo-note {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 20px;
            margin-top: 40px;
            text-align: center;
            color: #856404;
        }

        .demo-note h3 {
            margin-bottom: 10px;
            color: #856404;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .features-grid {
                grid-template-columns: 1fr;
            }

            .stat-card {
                flex-direction: column;
                text-align: center;
            }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
            100% {
                transform: scale(1);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header pulse">
            <h1>🐾 نظام بيطره</h1>
            <p>نظام شامل لإدارة العيادات البيطرية</p>
        </div>

        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon">👥</div>
                <div class="stat-content">
                    <h3>25</h3>
                    <p>إجمالي المربين</p>
                </div>
            </div>

            <div class="stat-card">
                <div class="stat-icon">🐄</div>
                <div class="stat-content">
                    <h3>150</h3>
                    <p>إجمالي الحيوانات</p>
                </div>
            </div>

            <div class="stat-card">
                <div class="stat-icon">💊</div>
                <div class="stat-content">
                    <h3>45</h3>
                    <p>إجمالي الأدوية</p>
                </div>
            </div>

            <div class="stat-card">
                <div class="stat-icon">🩺</div>
                <div class="stat-content">
                    <h3>89</h3>
                    <p>إجمالي العلاجات</p>
                </div>
            </div>
        </div>

        <div class="features-section">
            <h2>المميزات الرئيسية</h2>
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">👨‍⚕️</div>
                    <h3>إدارة المربين</h3>
                    <p>تسجيل وإدارة بيانات المربين والحيوانات مع إمكانية تتبع تاريخ العلاجات والمتابعة الدورية</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">💉</div>
                    <h3>إدارة الأدوية</h3>
                    <p>تتبع المخزون والتنبيهات التلقائية عند انخفاض الكمية أو اقتراب انتهاء الصلاحية</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">❤️</div>
                    <h3>نظام العلاج</h3>
                    <p>تشخيص شامل وخطط علاج مفصلة مع إمكانية صرف الأدوية وحساب التكلفة</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">📊</div>
                    <h3>التقارير والإحصائيات</h3>
                    <p>تقارير مفصلة عن الأداء المالي والعلاجات والمخزون مع إمكانية التصدير</p>
                </div>
            </div>
        </div>

        <div class="demo-note">
            <h3>🎉 عرض توضيحي</h3>
            <p>هذا عرض توضيحي لنظام إدارة العيادات البيطرية "بيطره". النظام الكامل يتضمن قاعدة بيانات وAPI و واجهات تفاعلية متقدمة.</p>
        </div>
    </div>

    <script>
        // إضافة تأثيرات تفاعلية بسيطة
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.stat-card, .feature-card');
            
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-8px) scale(1.02)';
                });
                
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });

            // تحديث الأرقام بشكل تدريجي
            const numbers = document.querySelectorAll('.stat-content h3');
            numbers.forEach(number => {
                const finalValue = parseInt(number.textContent);
                let currentValue = 0;
                const increment = finalValue / 50;
                
                const timer = setInterval(() => {
                    currentValue += increment;
                    if (currentValue >= finalValue) {
                        currentValue = finalValue;
                        clearInterval(timer);
                    }
                    number.textContent = Math.floor(currentValue);
                }, 30);
            });
        });
    </script>
</body>
</html>
