namespace Baytara.Shared.Models
{
    public class TreatmentMedicine
    {
        public int TreatmentId { get; set; }
        public Treatment Treatment { get; set; } = null!;
        
        public int MedicineId { get; set; }
        public Medicine Medicine { get; set; } = null!;
        
        public int Quantity { get; set; }
        
        public decimal UnitPrice { get; set; }
        
        public decimal TotalPrice { get; set; }
        
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    }
}
