# نظام إدارة العيادات البيطرية - بيطره 🐾

نظام شامل لإدارة العيادات البيطرية باللغة العربية مع دعم RTL كامل.

## ✨ المميزات الرئيسية

### 🏥 إدارة شاملة
- 👨‍⚕️ **إدارة المربين**: تسجيل وإدارة بيانات المربين مع تتبع تاريخ العلاجات
- 🐄 **إدارة الحيوانات**: تصنيف الحيوانات حسب النوع مع أسعار العلاج المختلفة
- 💊 **إدارة الأدوية**: تتبع المخزون مع تنبيهات انخفاض الكمية وانتهاء الصلاحية
- 🩺 **نظام العلاج**: تشخيص شامل مع صرف الأدوية وحساب التكلفة
- 🔬 **المختبر**: طلب وإدارة الفحوصات المخبرية مع تتبع النتائج
- 📊 **التقارير**: تقارير مفصلة عن الأداء المالي والعلاجات والمخزون

### 🔐 الأمان والمصادقة
- نظام مصادقة متقدم مع JWT
- أدوار مختلفة (مدير النظام، طبيب، فني مختبر)
- تشفير كلمات المرور
- حماية API endpoints

### 🌐 واجهة المستخدم
- تصميم عربي كامل مع دعم RTL
- واجهة متجاوبة تعمل على جميع الأجهزة
- تأثيرات بصرية متقدمة
- تجربة مستخدم سلسة

## 🛠️ التقنيات المستخدمة

### Backend
- **ASP.NET Core 9.0** - Web API
- **Entity Framework Core** - ORM
- **SQL Server** - قاعدة البيانات
- **JWT** - المصادقة
- **BCrypt** - تشفير كلمات المرور

### Frontend
- **Blazor WebAssembly** - SPA Framework
- **Bootstrap 5** - CSS Framework
- **Font Awesome** - الأيقونات
- **Custom CSS** - تصميم مخصص

### قاعدة البيانات
- **15 جدول مترابط**
- **Foreign Keys** و **Constraints**
- **Indexes** للأداء
- **Seed Data** للبيانات الأولية

## 📋 متطلبات التشغيل

- **.NET 9.0 SDK** أو أحدث
- **SQL Server** (LocalDB، SQL Server Express، أو SQL Server)
- **Visual Studio 2022** أو **VS Code** (اختياري)
- **Git** (لتحميل المشروع)

## 🚀 التشغيل السريع

### الطريقة الأولى: ملفات التشغيل المباشر

1. **تشغيل API**:
   ```bash
   # Windows
   run-api.bat
   
   # أو يدوياً
   cd Baytara.API
   dotnet run --urls "https://localhost:7001;http://localhost:5001"
   ```

2. **تشغيل العميل** (في terminal منفصل):
   ```bash
   # Windows
   run-client.bat
   
   # أو يدوياً
   cd Baytara.Client
   dotnet run --urls "https://localhost:7002;http://localhost:5002"
   ```

## 🔧 الإعداد التفصيلي

### 1. تحميل المشروع
```bash
git clone [repository-url]
cd Baytara
```

### 2. إعداد قاعدة البيانات
```bash
cd Baytara.API
dotnet ef database update
```

### 3. بناء المشروع
```bash
# بناء الحل كاملاً
dotnet build

# أو بناء كل مشروع منفصل
cd Baytara.API && dotnet build
cd ../Baytara.Client && dotnet build
```

### 4. تشغيل المشروع
```bash
# تشغيل API (Terminal 1)
cd Baytara.API
dotnet run

# تشغيل Client (Terminal 2)
cd Baytara.Client
dotnet run
```

## 🔑 بيانات الدخول الافتراضية

| الدور | اسم المستخدم | كلمة المرور | الصلاحيات |
|-------|-------------|------------|-----------|
| مدير النظام | `admin` | `admin123` | جميع الصلاحيات |
| طبيب | `doctor1` | `doctor123` | العلاجات والفحوصات |

## 📁 هيكل المشروع

```
Baytara/
├── 📁 Baytara.API/              # Web API Backend
│   ├── 📁 Controllers/          # API Controllers
│   ├── 📁 Data/                # Database Context
│   ├── 📁 Services/            # Business Logic
│   └── 📄 Program.cs           # Entry Point
├── 📁 Baytara.Client/          # Blazor WebAssembly Frontend
│   ├── 📁 Pages/               # Razor Pages
│   ├── 📁 Services/            # Client Services
│   ├── 📁 Layout/              # Layout Components
│   └── 📁 wwwroot/             # Static Files
├── 📁 Baytara.Shared/          # Shared Models & DTOs
│   ├── 📁 Models/              # Entity Models
│   └── 📁 DTOs/                # Data Transfer Objects
├── 📄 run-api.bat              # تشغيل API
├── 📄 run-client.bat           # تشغيل العميل
└── 📄 README.md                # هذا الملف
```

## 🌟 المميزات المتقدمة

### نظام الأدوية الذكي
- تتبع تلقائي للمخزون
- تنبيهات انخفاض الكمية
- تنبيهات انتهاء الصلاحية
- حساب تلقائي للتكلفة

### نظام العلاج المتطور
- تشخيص مفصل
- صرف أدوية متعددة
- حساب التكلفة الإجمالية
- تتبع حالة العلاج

### المختبر المتكامل
- أنواع فحوصات متعددة
- تتبع حالة الفحص
- إدخال النتائج
- ربط بالعلاجات

### التقارير الشاملة
- تقارير مالية
- تقارير العلاجات
- تقارير المخزون
- إحصائيات تفاعلية

## 🔄 التحديثات القادمة

- [ ] نظام الفواتير
- [ ] تصدير التقارير PDF
- [ ] نظام النسخ الاحتياطي
- [ ] تطبيق الهاتف المحمول
- [ ] نظام الإشعارات
- [ ] تكامل مع أنظمة الدفع

## 🎯 الحالة الحالية

✅ **مكتمل ويعمل بنجاح!**

- ✅ API Backend مكتمل
- ✅ Blazor Client مكتمل
- ✅ قاعدة البيانات مكتملة
- ✅ نظام المصادقة يعمل
- ✅ جميع الصفحات تعمل
- ✅ التصميم العربي مكتمل
- ✅ المختبر مضاف ويعمل

---

<div align="center">
  <strong>🐾 نظام بيطره - إدارة العيادات البيطرية بكفاءة واحترافية 🐾</strong>
</div>
