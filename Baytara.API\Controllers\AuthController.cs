using Baytara.API.Services;
using Baytara.API.Data;
using Baytara.Shared.DTOs;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace Baytara.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class AuthController : ControllerBase
    {
        private readonly IAuthService _authService;

        public AuthController(IAuthService authService)
        {
            _authService = authService;
        }

        [HttpPost("login")]
        public async Task<ActionResult<LoginResponseDto>> Login([FromBody] LoginDto loginDto)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(new LoginResponseDto
                {
                    Success = false,
                    Message = "البيانات المدخلة غير صحيحة"
                });
            }

            var result = await _authService.LoginAsync(loginDto);
            
            if (!result.Success)
            {
                return Unauthorized(result);
            }

            return Ok(result);
        }

        [HttpGet("me")]
        [Microsoft.AspNetCore.Authorization.Authorize]
        public async Task<ActionResult<UserDto>> GetCurrentUser()
        {
            var userIdClaim = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier);
            if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out int userId))
            {
                return Unauthorized();
            }

            var user = await _authService.GetUserByIdAsync(userId);
            if (user == null)
            {
                return NotFound();
            }

            return Ok(user);
        }

        [HttpGet("test-user")]
        public async Task<ActionResult> TestUser()
        {
            var user = await _authService.GetUserByUsernameAsync("admin");
            if (user == null)
            {
                return Ok(new { message = "المستخدم admin غير موجود" });
            }

            return Ok(new {
                message = "المستخدم موجود",
                username = user.Username,
                fullName = user.FullName,
                isActive = user.IsActive
            });
        }

        [HttpPost("create-simple-admin")]
        public async Task<ActionResult> CreateSimpleAdmin()
        {
            var context = HttpContext.RequestServices.GetRequiredService<ApplicationDbContext>();

            // حذف جميع المستخدمين الحاليين
            var allUsers = await context.Users.ToListAsync();
            context.Users.RemoveRange(allUsers);
            await context.SaveChangesAsync();

            // إنشاء مستخدم جديد بكلمة مرور بسيطة
            var simplePassword = "123";
            var hashedPassword = BCrypt.Net.BCrypt.HashPassword(simplePassword);

            var newUser = new Baytara.Shared.Models.User
            {
                Username = "admin",
                PasswordHash = hashedPassword,
                FullName = "مدير النظام",
                Email = "<EMAIL>",
                Role = Baytara.Shared.Models.UserRole.SystemAdmin,
                IsActive = true,
                BranchId = 1,
                CreatedAt = DateTime.UtcNow
            };

            context.Users.Add(newUser);
            await context.SaveChangesAsync();

            // اختبار كلمة المرور
            var testVerify = BCrypt.Net.BCrypt.Verify(simplePassword, hashedPassword);

            return Ok(new {
                message = "تم إنشاء المستخدم admin بكلمة مرور بسيطة",
                username = "admin",
                password = "123",
                passwordTest = testVerify,
                userId = newUser.Id
            });
        }
    }
}
