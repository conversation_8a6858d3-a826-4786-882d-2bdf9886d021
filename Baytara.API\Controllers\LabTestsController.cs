using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using Baytara.API.Data;
using Baytara.Shared.DTOs;
using Baytara.Shared.Models;


namespace Baytara.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class LabTestsController : ControllerBase
    {
        private readonly ApplicationDbContext _context;

        public LabTestsController(ApplicationDbContext context)
        {
            _context = context;
        }

        [HttpGet]
        public async Task<ActionResult<IEnumerable<LabTestDto>>> GetLabTests()
        {
            var labTests = await _context.LabTests
                .Include(lt => lt.Animal)
                    .ThenInclude(a => a.AnimalType)
                .Include(lt => lt.Breeder)
                .Include(lt => lt.RequestedByUser)
                .Include(lt => lt.ProcessedByUser)
                .Include(lt => lt.Branch)
                .OrderByDescending(lt => lt.CreatedAt)
                .ToListAsync();

            var labTestDtos = labTests.Select(lt => new LabTestDto
            {
                Id = lt.Id,
                AnimalId = lt.AnimalId,
                AnimalName = lt.Animal.Name ?? "",
                AnimalTypeName = lt.Animal.AnimalType.Name,
                BreederId = lt.BreederId,
                BreederName = lt.Breeder.Name,
                TestType = lt.TestType,
                TestName = lt.TestName,
                Description = lt.Description,
                RequestedById = lt.RequestedById,
                RequestedByName = lt.RequestedByUser.FullName,
                ProcessedById = lt.ProcessedById,
                ProcessedByName = lt.ProcessedByUser?.FullName,
                BranchId = lt.BranchId,
                BranchName = lt.Branch.Name,
                Status = lt.Status,
                Results = lt.Results,
                Notes = lt.Notes,
                Cost = lt.Cost,
                RequestedAt = lt.RequestedAt,
                ProcessedAt = lt.ProcessedAt,
                CreatedAt = lt.CreatedAt,
                UpdatedAt = lt.UpdatedAt
            }).ToList();

            return Ok(labTestDtos);
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<LabTestDto>> GetLabTest(int id)
        {
            var labTest = await _context.LabTests
                .Include(lt => lt.Animal)
                    .ThenInclude(a => a.AnimalType)
                .Include(lt => lt.Breeder)
                .Include(lt => lt.RequestedByUser)
                .Include(lt => lt.ProcessedByUser)
                .Include(lt => lt.Branch)
                .FirstOrDefaultAsync(lt => lt.Id == id);

            if (labTest == null)
            {
                return NotFound();
            }

            var labTestDto = new LabTestDto
            {
                Id = labTest.Id,
                AnimalId = labTest.AnimalId,
                AnimalName = labTest.Animal.Name ?? "",
                AnimalTypeName = labTest.Animal.AnimalType.Name,
                BreederId = labTest.BreederId,
                BreederName = labTest.Breeder.Name,
                TestType = labTest.TestType,
                TestName = labTest.TestName,
                Description = labTest.Description,
                RequestedById = labTest.RequestedById,
                RequestedByName = labTest.RequestedByUser.FullName,
                ProcessedById = labTest.ProcessedById,
                ProcessedByName = labTest.ProcessedByUser?.FullName,
                BranchId = labTest.BranchId,
                BranchName = labTest.Branch.Name,
                Status = labTest.Status,
                Results = labTest.Results,
                Notes = labTest.Notes,
                Cost = labTest.Cost,
                RequestedAt = labTest.RequestedAt,
                ProcessedAt = labTest.ProcessedAt,
                CreatedAt = labTest.CreatedAt,
                UpdatedAt = labTest.UpdatedAt
            };

            return Ok(labTestDto);
        }

        [HttpPost]
        public async Task<ActionResult<LabTestDto>> CreateLabTest(CreateLabTestDto createLabTestDto)
        {
            var labTest = new LabTest
            {
                AnimalId = createLabTestDto.AnimalId,
                BreederId = createLabTestDto.BreederId,
                TestType = createLabTestDto.TestType,
                TestName = createLabTestDto.TestName,
                Description = createLabTestDto.Description,
                RequestedById = createLabTestDto.RequestedById,
                BranchId = createLabTestDto.BranchId,
                Status = LabTestStatus.Pending,
                Notes = createLabTestDto.Notes,
                Cost = createLabTestDto.Cost,
                RequestedAt = DateTime.UtcNow,
                CreatedAt = DateTime.UtcNow
            };

            _context.LabTests.Add(labTest);
            await _context.SaveChangesAsync();

            return CreatedAtAction(nameof(GetLabTest), new { id = labTest.Id }, labTest);
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateLabTest(int id, UpdateLabTestDto updateLabTestDto)
        {
            var labTest = await _context.LabTests.FindAsync(id);

            if (labTest == null)
            {
                return NotFound();
            }

            labTest.TestType = updateLabTestDto.TestType;
            labTest.TestName = updateLabTestDto.TestName;
            labTest.Description = updateLabTestDto.Description;
            labTest.Status = updateLabTestDto.Status;
            labTest.Results = updateLabTestDto.Results;
            labTest.Notes = updateLabTestDto.Notes;
            labTest.Cost = updateLabTestDto.Cost;
            labTest.ProcessedById = updateLabTestDto.ProcessedById;
            
            if (updateLabTestDto.Status == LabTestStatus.Completed && labTest.ProcessedAt == null)
            {
                labTest.ProcessedAt = DateTime.UtcNow;
            }
            
            labTest.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            return NoContent();
        }

        [HttpPut("{id}/status")]
        public async Task<IActionResult> UpdateLabTestStatus(int id, [FromBody] LabTestStatus status)
        {
            var labTest = await _context.LabTests.FindAsync(id);

            if (labTest == null)
            {
                return NotFound();
            }

            labTest.Status = status;
            
            if (status == LabTestStatus.Completed && labTest.ProcessedAt == null)
            {
                labTest.ProcessedAt = DateTime.UtcNow;
            }
            
            labTest.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            return NoContent();
        }

        [HttpPut("{id}/results")]
        public async Task<IActionResult> UpdateLabTestResults(int id, [FromBody] string results)
        {
            var labTest = await _context.LabTests.FindAsync(id);

            if (labTest == null)
            {
                return NotFound();
            }

            labTest.Results = results;
            labTest.Status = LabTestStatus.Completed;
            labTest.ProcessedAt = DateTime.UtcNow;
            labTest.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            return NoContent();
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteLabTest(int id)
        {
            var labTest = await _context.LabTests.FindAsync(id);

            if (labTest == null)
            {
                return NotFound();
            }

            _context.LabTests.Remove(labTest);
            await _context.SaveChangesAsync();

            return NoContent();
        }

        [HttpGet("by-breeder/{breederId}")]
        public async Task<ActionResult<IEnumerable<LabTestDto>>> GetLabTestsByBreeder(int breederId)
        {
            var labTests = await _context.LabTests
                .Include(lt => lt.Animal)
                    .ThenInclude(a => a.AnimalType)
                .Include(lt => lt.Breeder)
                .Include(lt => lt.RequestedByUser)
                .Include(lt => lt.ProcessedByUser)
                .Include(lt => lt.Branch)
                .Where(lt => lt.BreederId == breederId)
                .OrderByDescending(lt => lt.CreatedAt)
                .ToListAsync();

            var labTestDtos = labTests.Select(lt => new LabTestDto
            {
                Id = lt.Id,
                AnimalId = lt.AnimalId,
                AnimalName = lt.Animal.Name ?? "",
                AnimalTypeName = lt.Animal.AnimalType.Name,
                BreederId = lt.BreederId,
                BreederName = lt.Breeder.Name,
                TestType = lt.TestType,
                TestName = lt.TestName,
                Description = lt.Description,
                RequestedById = lt.RequestedById,
                RequestedByName = lt.RequestedByUser.FullName,
                ProcessedById = lt.ProcessedById,
                ProcessedByName = lt.ProcessedByUser?.FullName,
                BranchId = lt.BranchId,
                BranchName = lt.Branch.Name,
                Status = lt.Status,
                Results = lt.Results,
                Notes = lt.Notes,
                Cost = lt.Cost,
                RequestedAt = lt.RequestedAt,
                ProcessedAt = lt.ProcessedAt,
                CreatedAt = lt.CreatedAt,
                UpdatedAt = lt.UpdatedAt
            }).ToList();

            return Ok(labTestDtos);
        }

        [HttpGet("by-animal/{animalId}")]
        public async Task<ActionResult<IEnumerable<LabTestDto>>> GetLabTestsByAnimal(int animalId)
        {
            var labTests = await _context.LabTests
                .Include(lt => lt.Animal)
                    .ThenInclude(a => a.AnimalType)
                .Include(lt => lt.Breeder)
                .Include(lt => lt.RequestedByUser)
                .Include(lt => lt.ProcessedByUser)
                .Include(lt => lt.Branch)
                .Where(lt => lt.AnimalId == animalId)
                .OrderByDescending(lt => lt.CreatedAt)
                .ToListAsync();

            var labTestDtos = labTests.Select(lt => new LabTestDto
            {
                Id = lt.Id,
                AnimalId = lt.AnimalId,
                AnimalName = lt.Animal.Name ?? "",
                AnimalTypeName = lt.Animal.AnimalType.Name,
                BreederId = lt.BreederId,
                BreederName = lt.Breeder.Name,
                TestType = lt.TestType,
                TestName = lt.TestName,
                Description = lt.Description,
                RequestedById = lt.RequestedById,
                RequestedByName = lt.RequestedByUser.FullName,
                ProcessedById = lt.ProcessedById,
                ProcessedByName = lt.ProcessedByUser?.FullName,
                BranchId = lt.BranchId,
                BranchName = lt.Branch.Name,
                Status = lt.Status,
                Results = lt.Results,
                Notes = lt.Notes,
                Cost = lt.Cost,
                RequestedAt = lt.RequestedAt,
                ProcessedAt = lt.ProcessedAt,
                CreatedAt = lt.CreatedAt,
                UpdatedAt = lt.UpdatedAt
            }).ToList();

            return Ok(labTestDtos);
        }

        [HttpGet("pending")]
        public async Task<ActionResult<IEnumerable<LabTestDto>>> GetPendingLabTests()
        {
            var labTests = await _context.LabTests
                .Include(lt => lt.Animal)
                    .ThenInclude(a => a.AnimalType)
                .Include(lt => lt.Breeder)
                .Include(lt => lt.RequestedByUser)
                .Include(lt => lt.ProcessedByUser)
                .Include(lt => lt.Branch)
                .Where(lt => lt.Status == LabTestStatus.Pending || lt.Status == LabTestStatus.InProgress)
                .OrderBy(lt => lt.RequestedAt)
                .ToListAsync();

            var labTestDtos = labTests.Select(lt => new LabTestDto
            {
                Id = lt.Id,
                AnimalId = lt.AnimalId,
                AnimalName = lt.Animal.Name ?? "",
                AnimalTypeName = lt.Animal.AnimalType.Name,
                BreederId = lt.BreederId,
                BreederName = lt.Breeder.Name,
                TestType = lt.TestType,
                TestName = lt.TestName,
                Description = lt.Description,
                RequestedById = lt.RequestedById,
                RequestedByName = lt.RequestedByUser.FullName,
                ProcessedById = lt.ProcessedById,
                ProcessedByName = lt.ProcessedByUser?.FullName,
                BranchId = lt.BranchId,
                BranchName = lt.Branch.Name,
                Status = lt.Status,
                Results = lt.Results,
                Notes = lt.Notes,
                Cost = lt.Cost,
                RequestedAt = lt.RequestedAt,
                ProcessedAt = lt.ProcessedAt,
                CreatedAt = lt.CreatedAt,
                UpdatedAt = lt.UpdatedAt
            }).ToList();

            return Ok(labTestDtos);
        }
    }
}
