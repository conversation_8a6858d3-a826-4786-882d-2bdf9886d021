@page "/medicines"
@using Baytara.Client.Services
@using Baytara.Shared.DTOs
@inject IApiService ApiService
@inject IJSRuntime JSRuntime

<PageTitle>الأدوية - بيطره</PageTitle>

<div class="medicines-page">
    <div class="page-header">
        <div class="header-content">
            <h1 class="page-title">
                <i class="fas fa-pills"></i>
                إدارة الأدوية
            </h1>
            <p class="page-subtitle">إدارة مخزون الأدوية البيطرية والتنبيهات</p>
        </div>
        <div class="header-actions">
            <div class="filter-buttons">
                <button class="btn @(currentFilter == "all" ? "btn-primary" : "btn-outline")" @onclick="@(() => FilterMedicines("all"))">
                    <i class="fas fa-list"></i>
                    جميع الأدوية
                </button>
                <button class="btn @(currentFilter == "lowstock" ? "btn-warning" : "btn-outline")" @onclick="@(() => FilterMedicines("lowstock"))">
                    <i class="fas fa-exclamation-triangle"></i>
                    مخزون منخفض
                </button>
                <button class="btn @(currentFilter == "expiring" ? "btn-danger" : "btn-outline")" @onclick="@(() => FilterMedicines("expiring"))">
                    <i class="fas fa-clock"></i>
                    منتهية الصلاحية
                </button>
            </div>
            <button class="btn btn-primary" @onclick="ShowAddMedicineModal">
                <i class="fas fa-plus"></i>
                إضافة دواء جديد
            </button>
        </div>
    </div>

    <!-- Alerts Summary -->
    @if (medicineAlerts.Any())
    {
        <div class="alerts-summary">
            <div class="alert-card low-stock">
                <div class="alert-icon">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div class="alert-content">
                    <h3>@medicineAlerts.Count(a => a.AlertType == "LowStock")</h3>
                    <p>أدوية منخفضة المخزون</p>
                </div>
            </div>
            <div class="alert-card expiring">
                <div class="alert-icon">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="alert-content">
                    <h3>@medicineAlerts.Count(a => a.AlertType == "Expiring")</h3>
                    <p>أدوية تنتهي قريباً</p>
                </div>
            </div>
            <div class="alert-card expired">
                <div class="alert-icon">
                    <i class="fas fa-times-circle"></i>
                </div>
                <div class="alert-content">
                    <h3>@medicineAlerts.Count(a => a.AlertType == "Expired")</h3>
                    <p>أدوية منتهية الصلاحية</p>
                </div>
            </div>
        </div>
    }

    @if (isLoading)
    {
        <div class="loading-container">
            <div class="spinner">
                <i class="fas fa-spinner fa-spin"></i>
            </div>
            <p>جاري تحميل البيانات...</p>
        </div>
    }
    else if (filteredMedicines.Any())
    {
        <div class="medicines-grid">
            @foreach (var medicine in filteredMedicines)
            {
                <div class="medicine-card @GetMedicineCardClass(medicine)">
                    <div class="card-header">
                        <div class="medicine-info">
                            <h3 class="medicine-name">@medicine.Name</h3>
                            <p class="medicine-manufacturer">@medicine.Manufacturer</p>
                        </div>
                        <div class="card-actions">
                            <button class="btn btn-icon btn-warning" @onclick="() => ShowEditMedicineModal(medicine)" title="تعديل">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-icon btn-danger" @onclick="() => DeleteMedicine(medicine.Id)" title="حذف">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="card-body">
                        @if (!string.IsNullOrEmpty(medicine.Description))
                        {
                            <p class="medicine-description">@medicine.Description</p>
                        }
                        
                        <div class="medicine-details">
                            <div class="detail-row">
                                <span class="label">الكمية المتوفرة:</span>
                                <span class="value quantity @(medicine.IsLowStock ? "low-stock" : "")">
                                    @medicine.Quantity @medicine.Unit
                                </span>
                            </div>
                            <div class="detail-row">
                                <span class="label">الحد الأدنى:</span>
                                <span class="value">@medicine.MinimumStock @medicine.Unit</span>
                            </div>
                            <div class="detail-row">
                                <span class="label">سعر الوحدة:</span>
                                <span class="value price">@medicine.UnitPrice.ToString("N2") ر.س</span>
                            </div>
                            @if (medicine.ExpiryDate.HasValue)
                            {
                                <div class="detail-row">
                                    <span class="label">تاريخ الانتهاء:</span>
                                    <span class="value expiry @(medicine.IsExpired ? "expired" : medicine.IsExpiringSoon ? "expiring" : "")">
                                        @medicine.ExpiryDate.Value.ToString("dd/MM/yyyy")
                                    </span>
                                </div>
                            }
                            @if (!string.IsNullOrEmpty(medicine.BatchNumber))
                            {
                                <div class="detail-row">
                                    <span class="label">رقم الدفعة:</span>
                                    <span class="value">@medicine.BatchNumber</span>
                                </div>
                            }
                        </div>
                        
                        <!-- Alert Badges -->
                        <div class="alert-badges">
                            @if (medicine.IsLowStock)
                            {
                                <span class="badge badge-warning">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    مخزون منخفض
                                </span>
                            }
                            @if (medicine.IsExpired)
                            {
                                <span class="badge badge-danger">
                                    <i class="fas fa-times-circle"></i>
                                    منتهي الصلاحية
                                </span>
                            }
                            else if (medicine.IsExpiringSoon)
                            {
                                <span class="badge badge-warning">
                                    <i class="fas fa-clock"></i>
                                    ينتهي قريباً
                                </span>
                            }
                        </div>
                        
                        <div class="card-footer">
                            <small class="text-muted">
                                الفرع: @medicine.BranchName
                            </small>
                            <small class="text-muted">
                                تاريخ الإضافة: @medicine.CreatedAt.ToString("dd/MM/yyyy")
                            </small>
                        </div>
                    </div>
                </div>
            }
        </div>
    }
    else
    {
        <div class="empty-state">
            <i class="fas fa-pills"></i>
            <h3>@GetEmptyStateTitle()</h3>
            <p>@GetEmptyStateMessage()</p>
            @if (currentFilter == "all")
            {
                <button class="btn btn-primary" @onclick="ShowAddMedicineModal">
                    <i class="fas fa-plus"></i>
                    إضافة دواء جديد
                </button>
            }
        </div>
    }
</div>

<!-- Add/Edit Medicine Modal -->
@if (showMedicineModal)
{
    <div class="modal-overlay" @onclick="CloseMedicineModal">
        <div class="modal-content large-modal" @onclick:stopPropagation="true">
            <div class="modal-header">
                <h3>@(editingMedicine == null ? "إضافة دواء جديد" : "تعديل بيانات الدواء")</h3>
                <button class="btn btn-close" @onclick="CloseMedicineModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <div class="modal-body">
                <EditForm Model="@medicineForm" OnValidSubmit="@SaveMedicine">
                    <DataAnnotationsValidator />
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="name">اسم الدواء *</label>
                            <InputText id="name" @bind-Value="medicineForm.Name" class="form-control" placeholder="أدخل اسم الدواء" />
                            <ValidationMessage For="@(() => medicineForm.Name)" />
                        </div>
                        
                        <div class="form-group">
                            <label for="manufacturer">الشركة المصنعة</label>
                            <InputText id="manufacturer" @bind-Value="medicineForm.Manufacturer" class="form-control" placeholder="أدخل اسم الشركة المصنعة" />
                            <ValidationMessage For="@(() => medicineForm.Manufacturer)" />
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="description">الوصف</label>
                        <InputTextArea id="description" @bind-Value="medicineForm.Description" class="form-control" rows="3" placeholder="أدخل وصف الدواء" />
                        <ValidationMessage For="@(() => medicineForm.Description)" />
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="unit">الوحدة</label>
                            <InputText id="unit" @bind-Value="medicineForm.Unit" class="form-control" placeholder="مثل: قرص، كبسولة، مل" />
                            <ValidationMessage For="@(() => medicineForm.Unit)" />
                        </div>
                        
                        <div class="form-group">
                            <label for="unitPrice">سعر الوحدة *</label>
                            <InputNumber id="unitPrice" @bind-Value="medicineForm.UnitPrice" class="form-control" placeholder="0.00" />
                            <ValidationMessage For="@(() => medicineForm.UnitPrice)" />
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="quantity">الكمية *</label>
                            <InputNumber id="quantity" @bind-Value="medicineForm.Quantity" class="form-control" placeholder="0" />
                            <ValidationMessage For="@(() => medicineForm.Quantity)" />
                        </div>
                        
                        <div class="form-group">
                            <label for="minimumStock">الحد الأدنى للمخزون *</label>
                            <InputNumber id="minimumStock" @bind-Value="medicineForm.MinimumStock" class="form-control" placeholder="0" />
                            <ValidationMessage For="@(() => medicineForm.MinimumStock)" />
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="batchNumber">رقم الدفعة</label>
                            <InputText id="batchNumber" @bind-Value="medicineForm.BatchNumber" class="form-control" placeholder="أدخل رقم الدفعة" />
                            <ValidationMessage For="@(() => medicineForm.BatchNumber)" />
                        </div>
                        
                        <div class="form-group">
                            <label for="expiryDate">تاريخ انتهاء الصلاحية</label>
                            <InputDate id="expiryDate" @bind-Value="medicineForm.ExpiryDate" class="form-control" />
                            <ValidationMessage For="@(() => medicineForm.ExpiryDate)" />
                        </div>
                    </div>
                    
                    <div class="modal-actions">
                        <button type="button" class="btn btn-secondary" @onclick="CloseMedicineModal">إلغاء</button>
                        <button type="submit" class="btn btn-primary" disabled="@isSaving">
                            @if (isSaving)
                            {
                                <i class="fas fa-spinner fa-spin"></i>
                                <span>جاري الحفظ...</span>
                            }
                            else
                            {
                                <i class="fas fa-save"></i>
                                <span>حفظ</span>
                            }
                        </button>
                    </div>
                </EditForm>
            </div>
        </div>
    </div>
}

<style>
    .medicines-page {
        max-width: 1400px;
        margin: 0 auto;
    }

    .page-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 30px;
        padding-bottom: 20px;
        border-bottom: 2px solid #eee;
        flex-wrap: wrap;
        gap: 20px;
    }

    .header-content h1 {
        font-size: 2.2rem;
        color: #333;
        margin-bottom: 5px;
        display: flex;
        align-items: center;
        gap: 15px;
    }

    .page-subtitle {
        font-size: 1.1rem;
        color: #666;
        margin-bottom: 0;
    }

    .header-actions {
        display: flex;
        gap: 20px;
        align-items: center;
        flex-wrap: wrap;
    }

    .filter-buttons {
        display: flex;
        gap: 10px;
        flex-wrap: wrap;
    }

    .btn {
        padding: 10px 20px;
        border: none;
        border-radius: 8px;
        cursor: pointer;
        font-size: 0.9rem;
        font-weight: 500;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 8px;
        text-decoration: none;
    }

    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
    }

    .btn-outline {
        background: white;
        border: 2px solid #e9ecef;
        color: #666;
    }

    .btn-outline:hover {
        border-color: #667eea;
        color: #667eea;
    }

    .btn-warning {
        background: #ffc107;
        color: white;
    }

    .btn-danger {
        background: #dc3545;
        color: white;
    }

    .btn-icon {
        padding: 8px;
        width: 36px;
        height: 36px;
        border-radius: 50%;
        justify-content: center;
    }

    .btn-secondary { background: #6c757d; color: white; }

    /* Alerts Summary */
    .alerts-summary {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }

    .alert-card {
        background: white;
        border-radius: 12px;
        padding: 20px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        display: flex;
        align-items: center;
        gap: 15px;
        border-left: 5px solid;
    }

    .alert-card.low-stock { border-left-color: #ffc107; }
    .alert-card.expiring { border-left-color: #ff9800; }
    .alert-card.expired { border-left-color: #dc3545; }

    .alert-icon {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.3rem;
        color: white;
    }

    .alert-card.low-stock .alert-icon { background: #ffc107; }
    .alert-card.expiring .alert-icon { background: #ff9800; }
    .alert-card.expired .alert-icon { background: #dc3545; }

    .alert-content h3 {
        font-size: 1.8rem;
        font-weight: 700;
        color: #333;
        margin-bottom: 5px;
    }

    .alert-content p {
        font-size: 0.9rem;
        color: #666;
        margin-bottom: 0;
    }

    .loading-container {
        text-align: center;
        padding: 60px 20px;
        color: #666;
    }

    .loading-container .spinner {
        font-size: 3rem;
        color: #667eea;
        margin-bottom: 20px;
    }

    /* Medicines Grid */
    .medicines-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
        gap: 25px;
    }

    .medicine-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        border-left: 5px solid #28a745;
    }

    .medicine-card.has-alerts {
        border-left-color: #ffc107;
    }

    .medicine-card.expired {
        border-left-color: #dc3545;
    }

    .medicine-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }

    .card-header {
        padding: 20px;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-bottom: 1px solid #dee2e6;
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
    }

    .medicine-name {
        font-size: 1.3rem;
        font-weight: 600;
        color: #333;
        margin-bottom: 5px;
    }

    .medicine-manufacturer {
        font-size: 0.9rem;
        color: #666;
        margin-bottom: 0;
    }

    .card-actions {
        display: flex;
        gap: 8px;
    }

    .card-body {
        padding: 20px;
    }

    .medicine-description {
        font-size: 0.9rem;
        color: #666;
        margin-bottom: 15px;
        font-style: italic;
    }

    .medicine-details {
        margin-bottom: 15px;
    }

    .detail-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
        padding: 5px 0;
    }

    .detail-row .label {
        font-size: 0.9rem;
        color: #666;
        font-weight: 500;
    }

    .detail-row .value {
        font-size: 0.9rem;
        color: #333;
        font-weight: 600;
    }

    .value.quantity.low-stock {
        color: #ffc107;
    }

    .value.price {
        color: #28a745;
    }

    .value.expiry.expiring {
        color: #ff9800;
    }

    .value.expiry.expired {
        color: #dc3545;
    }

    /* Alert Badges */
    .alert-badges {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        margin-bottom: 15px;
    }

    .badge {
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: 500;
        display: flex;
        align-items: center;
        gap: 4px;
    }

    .badge-warning {
        background: rgba(255, 193, 7, 0.2);
        color: #856404;
        border: 1px solid rgba(255, 193, 7, 0.3);
    }

    .badge-danger {
        background: rgba(220, 53, 69, 0.2);
        color: #721c24;
        border: 1px solid rgba(220, 53, 69, 0.3);
    }

    .card-footer {
        padding-top: 15px;
        border-top: 1px solid #eee;
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;
        gap: 10px;
    }

    .text-muted {
        color: #999 !important;
        font-size: 0.8rem;
    }

    /* Empty State */
    .empty-state {
        text-align: center;
        padding: 80px 20px;
        color: #666;
    }

    .empty-state i {
        font-size: 4rem;
        color: #ddd;
        margin-bottom: 20px;
    }

    .empty-state h3 {
        font-size: 1.5rem;
        margin-bottom: 10px;
        color: #333;
    }

    .empty-state p {
        font-size: 1rem;
        margin-bottom: 30px;
    }

    /* Modal Styles */
    .modal-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1000;
        backdrop-filter: blur(5px);
    }

    .modal-content {
        background: white;
        border-radius: 15px;
        width: 90%;
        max-width: 500px;
        max-height: 90vh;
        overflow-y: auto;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    }

    .large-modal {
        max-width: 700px;
    }

    .modal-header {
        padding: 20px 25px;
        border-bottom: 1px solid #eee;
        display: flex;
        justify-content: space-between;
        align-items: center;
        background: #f8f9fa;
        border-radius: 15px 15px 0 0;
    }

    .modal-header h3 {
        font-size: 1.3rem;
        color: #333;
        margin-bottom: 0;
    }

    .btn-close {
        background: none;
        border: none;
        font-size: 1.2rem;
        color: #666;
        cursor: pointer;
        padding: 5px;
        border-radius: 50%;
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .btn-close:hover {
        background: #e9ecef;
        color: #333;
    }

    .modal-body {
        padding: 25px;
    }

    .form-group {
        margin-bottom: 20px;
    }

    .form-group label {
        display: block;
        margin-bottom: 8px;
        font-weight: 500;
        color: #333;
    }

    .form-control {
        width: 100%;
        padding: 12px 15px;
        border: 2px solid #e9ecef;
        border-radius: 8px;
        font-size: 1rem;
        transition: border-color 0.3s ease;
    }

    .form-control:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }

    .form-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 15px;
    }

    .modal-actions {
        display: flex;
        gap: 15px;
        justify-content: flex-end;
        margin-top: 25px;
        padding-top: 20px;
        border-top: 1px solid #eee;
    }

    .validation-message {
        color: #dc3545;
        font-size: 0.875rem;
        margin-top: 5px;
        display: block;
    }

    /* Responsive Design */
    @@media (max-width: 768px) {
        .page-header {
            flex-direction: column;
            align-items: flex-start;
        }

        .header-actions {
            width: 100%;
            justify-content: space-between;
        }

        .filter-buttons {
            flex: 1;
        }

        .medicines-grid {
            grid-template-columns: 1fr;
        }

        .card-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 15px;
        }

        .card-actions {
            align-self: stretch;
            justify-content: space-between;
        }

        .form-row {
            grid-template-columns: 1fr;
        }

        .modal-actions {
            flex-direction: column;
        }

        .card-footer {
            flex-direction: column;
            align-items: flex-start;
        }
    }
</style>

@code {
    private List<MedicineDto> medicines = new();
    private List<MedicineDto> filteredMedicines = new();
    private List<MedicineAlertDto> medicineAlerts = new();
    private bool isLoading = true;
    private bool isSaving = false;
    private string currentFilter = "all";

    // Modal states
    private bool showMedicineModal = false;
    private MedicineDto? editingMedicine = null;

    // Forms
    private CreateMedicineDto medicineForm = new();

    protected override async Task OnInitializedAsync()
    {
        await LoadData();
    }

    private async Task LoadData()
    {
        try
        {
            isLoading = true;
            StateHasChanged();

            var medicinesTask = ApiService.GetMedicinesAsync();
            var alertsTask = ApiService.GetMedicineAlertsAsync();

            await Task.WhenAll(medicinesTask, alertsTask);

            medicines = (await medicinesTask).ToList();
            medicineAlerts = (await alertsTask).ToList();

            FilterMedicines(currentFilter);
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", "حدث خطأ في تحميل البيانات");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private void FilterMedicines(string filter)
    {
        currentFilter = filter;

        filteredMedicines = filter switch
        {
            "lowstock" => medicines.Where(m => m.IsLowStock).ToList(),
            "expiring" => medicines.Where(m => m.IsExpiringSoon || m.IsExpired).ToList(),
            _ => medicines.ToList()
        };

        StateHasChanged();
    }

    private string GetMedicineCardClass(MedicineDto medicine)
    {
        if (medicine.IsExpired)
            return "expired";
        if (medicine.IsLowStock || medicine.IsExpiringSoon)
            return "has-alerts";
        return "";
    }

    private string GetEmptyStateTitle()
    {
        return currentFilter switch
        {
            "lowstock" => "لا توجد أدوية منخفضة المخزون",
            "expiring" => "لا توجد أدوية منتهية أو تنتهي قريباً",
            _ => "لا توجد أدوية مسجلة"
        };
    }

    private string GetEmptyStateMessage()
    {
        return currentFilter switch
        {
            "lowstock" => "جميع الأدوية لديها مخزون كافي",
            "expiring" => "جميع الأدوية صالحة للاستخدام",
            _ => "ابدأ بإضافة الأدوية لإدارة المخزون"
        };
    }

    private void ShowAddMedicineModal()
    {
        editingMedicine = null;
        medicineForm = new CreateMedicineDto();
        showMedicineModal = true;
    }

    private void ShowEditMedicineModal(MedicineDto medicine)
    {
        editingMedicine = medicine;
        medicineForm = new CreateMedicineDto
        {
            Name = medicine.Name,
            Description = medicine.Description,
            Unit = medicine.Unit,
            Quantity = medicine.Quantity,
            MinimumStock = medicine.MinimumStock,
            UnitPrice = medicine.UnitPrice,
            ExpiryDate = medicine.ExpiryDate,
            BatchNumber = medicine.BatchNumber,
            Manufacturer = medicine.Manufacturer,
            BranchId = medicine.BranchId
        };
        showMedicineModal = true;
    }

    private void CloseMedicineModal()
    {
        showMedicineModal = false;
        editingMedicine = null;
        medicineForm = new CreateMedicineDto();
    }

    private async Task SaveMedicine()
    {
        try
        {
            isSaving = true;
            StateHasChanged();

            if (editingMedicine == null)
            {
                var result = await ApiService.CreateMedicineAsync(medicineForm);
                if (result != null)
                {
                    medicines.Insert(0, result);
                    FilterMedicines(currentFilter);
                    CloseMedicineModal();
                    await JSRuntime.InvokeVoidAsync("alert", "تم إضافة الدواء بنجاح");
                }
                else
                {
                    await JSRuntime.InvokeVoidAsync("alert", "حدث خطأ في إضافة الدواء");
                }
            }
            else
            {
                var updateDto = new UpdateMedicineDto
                {
                    Name = medicineForm.Name,
                    Description = medicineForm.Description,
                    Unit = medicineForm.Unit,
                    Quantity = medicineForm.Quantity,
                    MinimumStock = medicineForm.MinimumStock,
                    UnitPrice = medicineForm.UnitPrice,
                    ExpiryDate = medicineForm.ExpiryDate,
                    BatchNumber = medicineForm.BatchNumber,
                    Manufacturer = medicineForm.Manufacturer,
                    BranchId = medicineForm.BranchId,
                    IsActive = true
                };

                var result = await ApiService.UpdateMedicineAsync(editingMedicine.Id, updateDto);
                if (result != null)
                {
                    var index = medicines.FindIndex(m => m.Id == editingMedicine.Id);
                    if (index >= 0)
                    {
                        medicines[index] = result;
                    }
                    FilterMedicines(currentFilter);
                    CloseMedicineModal();
                    await JSRuntime.InvokeVoidAsync("alert", "تم تحديث بيانات الدواء بنجاح");
                }
                else
                {
                    await JSRuntime.InvokeVoidAsync("alert", "حدث خطأ في تحديث بيانات الدواء");
                }
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", "حدث خطأ غير متوقع");
        }
        finally
        {
            isSaving = false;
            StateHasChanged();
        }
    }

    private async Task DeleteMedicine(int medicineId)
    {
        var confirmed = await JSRuntime.InvokeAsync<bool>("confirm", "هل أنت متأكد من حذف هذا الدواء؟");
        if (confirmed)
        {
            try
            {
                var success = await ApiService.DeleteMedicineAsync(medicineId);
                if (success)
                {
                    medicines.RemoveAll(m => m.Id == medicineId);
                    FilterMedicines(currentFilter);
                    StateHasChanged();
                    await JSRuntime.InvokeVoidAsync("alert", "تم حذف الدواء بنجاح");
                }
                else
                {
                    await JSRuntime.InvokeVoidAsync("alert", "حدث خطأ في حذف الدواء");
                }
            }
            catch (Exception ex)
            {
                await JSRuntime.InvokeVoidAsync("alert", "حدث خطأ غير متوقع");
            }
        }
    }
}
